{"date": "Sat Jun 21 2025 22:03:56 GMT+0530 (India Standard Time)", "environment": "production", "error": {"address": "0.0.0.0", "code": "EADDRINUSE", "errno": -4091, "port": 3000, "syscall": "listen"}, "exception": true, "level": "error", "message": "uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3000\nError: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)", "os": {"loadavg": [0, 0, 0], "uptime": 23627.703}, "process": {"argv": ["C:\\Program Files\\nodejs\\node.exe", "C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\server.js"], "cwd": "C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend", "execPath": "C:\\Program Files\\nodejs\\node.exe", "gid": null, "memoryUsage": {"arrayBuffers": 18342288, "external": 22063788, "heapTotal": 80621568, "heapUsed": 49799152, "rss": 118882304}, "pid": 10340, "uid": null, "version": "v22.14.0"}, "service": "belilly-api", "stack": "Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)", "trace": [{"column": 16, "file": "node:net", "function": "Server.setupListenHandle [as _listen2]", "line": 1937, "method": "setupListenHandle [as _listen2]", "native": false}, {"column": 12, "file": "node:net", "function": "listenInCluster", "line": 1994, "method": null, "native": false}, {"column": 7, "file": "node:net", "function": null, "line": 2203, "method": null, "native": false}, {"column": 21, "file": "node:internal/process/task_queues", "function": "process.processTicksAndRejections", "line": 90, "method": "processTicksAndRejections", "native": false}]}