{"name": "Backend", "version": "1.0.0", "description": "JWT Authentication Backend", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "audit": "npm audit --production", "lint": "eslint .", "cleanup": "node scripts/cleanup.js"}, "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf-csrf": "^3.0.2", "dotenv": "^16.4.7", "express": "^4.21.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "google-auth-library": "^9.15.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "http-status-codes": "^2.3.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.3", "mongoose-sequence": "^6.0.1", "multer": "^1.4.5-lts.1", "rate-limiter-flexible": "^3.0.4", "redis": "^4.6.13", "sanitize-html": "^2.12.1", "secure-json-parse": "^2.7.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xss": "^1.0.14"}, "engines": {"node": ">=14.0.0"}, "author": "", "license": "ISC"}