{"version": 3, "file": "IsUrl.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsUrl.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gEAAiD;AAGpC,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,SAAgB,KAAK,CAAC,KAAa,EAAE,OAAkC;IACrE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,eAAc,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;AAFD,sBAEC;AAED;;;GAGG;AACH,SAAgB,KAAK,CAAC,OAAkC,EAAE,iBAAqC;IAC7F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACtE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iCAAiC,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAZD,sBAYC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isUrlValidator from 'validator/lib/isURL';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_URL = 'isUrl';\n\n/**\n * Checks if the string is a url.\n * If given value is not a string, then it returns false.\n */\nexport function isURL(value: string, options?: ValidatorJS.IsURLOptions): boolean {\n  return typeof value === 'string' && isUrlValidator(value, options);\n}\n\n/**\n * Checks if the string is a url.\n * If given value is not a string, then it returns false.\n */\nexport function IsUrl(options?: ValidatorJS.IsURLOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_URL,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isURL(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a URL address', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}