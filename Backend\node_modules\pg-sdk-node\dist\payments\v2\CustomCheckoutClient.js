"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomCheckoutClient = void 0;
const class_transformer_1 = require("class-transformer");
const BaseClient_1 = require("../../common/BaseClient");
const CommonUtils_1 = require("../../common/CommonUtils");
const Exceptions_1 = require("../../common/exception/Exceptions");
const HttpMethodType_1 = require("../../common/http/HttpMethodType");
const Env_1 = require("../../Env");
const Constants_1 = require("./customcheckout/Constants");
const CallbackResponse_1 = require("../../common/models/response/CallbackResponse");
const CustomCheckoutPayResponse_1 = require("./models/response/CustomCheckoutPayResponse");
const OrderStatusResponse_1 = require("../../common/models/response/OrderStatusResponse");
const RefundResponse_1 = require("../../common/models/response/RefundResponse");
const RefundStatusResponse_1 = require("../../common/models/response/RefundStatusResponse");
const PaymentFlowType_1 = require("../../common/models/PaymentFlowType");
const CreateSdkOrderResponse_1 = require("./models/response/CreateSdkOrderResponse");
const Constants_2 = require("../../common/exception/Constants");
const Headers_1 = require("../../common/constants/Headers");
const EventType_1 = require("../../common/events/models/enums/EventType");
const EventState_1 = require("../../common/events/models/enums/EventState");
const EventBuillder_1 = require("../../common/events/builders/EventBuillder");
class CustomCheckoutClient extends BaseClient_1.BaseClient {
    constructor(clientId, clientSecret, clientVersion, env, shouldPublishEvents) {
        super(clientId, clientSecret, clientVersion, env, shouldPublishEvents);
        /**
         * Initiate a Pay Order
         *
         * @param payRequest Request required to initiate the order. Depending on the instrument type, different builders can be used
         * @return Promise<CustomCheckoutPayResponse> which contains the data according to the instrument used
         */
        this.pay = (payRequest) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.PAY_API;
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.POST, url, CustomCheckoutPayResponse_1.CustomCheckoutPayResponse, this.headers, payRequest);
                this.eventPublisher.send((0, EventBuillder_1.buildCustomCheckoutPayRequest)(EventState_1.EventState.SUCCESS, EventType_1.EventType.PAY_SUCCESS, payRequest, url));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildCustomCheckoutPayRequest)(EventState_1.EventState.FAILED, EventType_1.EventType.PAY_FAILED, payRequest, url, error));
                throw error;
            }
        });
        /**
         * Gets status of an order
         *
         * @param merchantOrderId Order id generated by merchant
         * @param details         true -> order status has all attempt details under paymentDetails list
         *                        false -> order status has only latest attempt details under paymentDetails list
         * @return Promise<OrderStatusResponse> which contains the details about the order
         */
        this.getOrderStatus = (merchantOrderId, details = false) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.ORDER_STATUS_API.replace('{ORDER_ID}', merchantOrderId);
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.GET, url, OrderStatusResponse_1.OrderStatusResponse, this.headers, {}, { [Constants_1.CustomCheckoutConstants.ORDER_DETAILS]: details.toString() });
                this.eventPublisher.send((0, EventBuillder_1.buildOrderStatusEvent)(EventState_1.EventState.SUCCESS, merchantOrderId, PaymentFlowType_1.PaymentFlowType.PG, url, EventType_1.EventType.ORDER_STATUS_SUCCESS));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildOrderStatusEvent)(EventState_1.EventState.FAILED, merchantOrderId, PaymentFlowType_1.PaymentFlowType.PG, url, EventType_1.EventType.ORDER_STATUS_FAILED, error));
                throw error;
            }
        });
        /**
         * Initiate a refund of an order which is in completed state
         *
         * @param refundRequest Request required to initiate the order. It is build using RefundRequest.builder()
         * @return Promise<RefundResponse> which contains the details about the refund
         */
        this.refund = (refundRequest) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.REFUND_API;
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.POST, url, RefundResponse_1.RefundResponse, this.headers, refundRequest);
                this.eventPublisher.send((0, EventBuillder_1.buildRefundEvent)(EventState_1.EventState.SUCCESS, refundRequest, url, PaymentFlowType_1.PaymentFlowType.PG, EventType_1.EventType.REFUND_SUCCESS));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildRefundEvent)(EventState_1.EventState.FAILED, refundRequest, url, PaymentFlowType_1.PaymentFlowType.PG, EventType_1.EventType.REFUND_FAILED, error));
                throw error;
            }
        });
        /**
         * Gets the status of refund
         *
         * @param refundId Generated by merchant at the time of initiating the refund
         * @return Promise<RefundStatusResponse> which contains the status about the refund
         */
        this.getRefundStatus = (refundId) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.REFUND_STATUS_API.replace('{REFUND_ID}', refundId);
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.GET, url, RefundStatusResponse_1.RefundStatusResponse, this.headers);
                this.eventPublisher.send((0, EventBuillder_1.buildRefundStatusEvent)(EventState_1.EventState.SUCCESS, EventType_1.EventType.REFUND_STATUS_SUCCESS, refundId, url, PaymentFlowType_1.PaymentFlowType.PG));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildRefundStatusEvent)(EventState_1.EventState.FAILED, EventType_1.EventType.REFUND_STATUS_FAILED, refundId, url, PaymentFlowType_1.PaymentFlowType.PG));
                throw error;
            }
        });
        /**
         * Gets the status of a transaction attempted
         *
         * @param transactionId Transaction attempt id generated by PhonePe
         * @return Promise<OrderStatusResponse> which contains the details about that specific transactionId
         */
        this.getTransactionStatus = (transactionId) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.TRANSACTION_STATUS_API.replace('{TRANSACTION_ID}', transactionId);
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.GET, url, OrderStatusResponse_1.OrderStatusResponse, this.headers);
                this.eventPublisher.send((0, EventBuillder_1.buildTransactionStatusEvent)(EventState_1.EventState.SUCCESS, EventType_1.EventType.TRANSACTION_STATUS_SUCCESS, transactionId, url, PaymentFlowType_1.PaymentFlowType.PG));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildTransactionStatusEvent)(EventState_1.EventState.FAILED, EventType_1.EventType.TRANSACTION_STATUS_FAILED, transactionId, url, PaymentFlowType_1.PaymentFlowType.PG, error));
                throw error;
            }
        });
        /**
         * Create order token for SDK integrated order requests
         *
         * @param sdkRequest Request object build using CreateSdkOrderRequest.builder()
         * @return Promise<CreateSdkOrderResponse> which contains token details to be consumed by the UI
         */
        this.createSdkOrder = (sdkRequest) => __awaiter(this, void 0, void 0, function* () {
            const url = Constants_1.CustomCheckoutConstants.CREATE_ORDER_API;
            try {
                const response = yield this.requestViaAuthRefresh(HttpMethodType_1.HttpMethodType.POST, url, CreateSdkOrderResponse_1.CreateSdkOrderResponse, this.headers, sdkRequest);
                this.eventPublisher.send((0, EventBuillder_1.buildCreateSdkOrderEvent)(EventState_1.EventState.SUCCESS, EventType_1.EventType.CREATE_SDK_ORDER_SUCCESS, sdkRequest, url, PaymentFlowType_1.PaymentFlowType.PG));
                return response;
            }
            catch (error) {
                this.eventPublisher.send((0, EventBuillder_1.buildCreateSdkOrderEvent)(EventState_1.EventState.SUCCESS, EventType_1.EventType.CREATE_SDK_ORDER_SUCCESS, sdkRequest, url, PaymentFlowType_1.PaymentFlowType.PG));
                throw error;
            }
        });
        /**
         * Validate if the callback is valid
         *
         * @param username      username set by the merchant on the dashboard
         * @param password      password set by the merchant on the dashboard
         * @param authorization String data under `authorization` key of response headers
         * @param responseBody  Callback response body
         * @return CallbackResponse Deserialized callback body
         * @throws PhonePeException when callback is not valid
         */
        this.validateCallback = (username, password, authorization, responseBody) => {
            if (!CommonUtils_1.CommonUtils.isCallbackValid(username, password, authorization)) {
                throw new Exceptions_1.PhonePeException('Invalid Callback', 417);
            }
            const parsedBody = JSON.parse(responseBody);
            return (0, class_transformer_1.plainToClass)(CallbackResponse_1.CallbackResponse, parsedBody);
        };
        this.prepareHeaders = () => {
            return {
                [Headers_1.Headers.CONTENT_TYPE]: Headers_1.Headers.APPLICATION_JSON,
                [Headers_1.Headers.SOURCE]: Headers_1.Headers.INTEGRATION,
                [Headers_1.Headers.SOURCE_VERSION]: Headers_1.Headers.API_VERSION,
                [Headers_1.Headers.SOURCE_PLATFORM]: Headers_1.Headers.SDK_TYPE,
                [Headers_1.Headers.SOURCE_PLATFORM_VERSION]: Headers_1.Headers.SDK_VERSION,
            };
        };
        this.eventPublisher.send((0, EventBuillder_1.buildInitClientEvent)(EventType_1.EventType.CUSTOM_CHECKOUT_CLIENT_INITIALIZED, PaymentFlowType_1.PaymentFlowType.PG));
        this.headers = this.prepareHeaders();
    }
}
exports.CustomCheckoutClient = CustomCheckoutClient;
/**
 * Generates a CustomCheckout Client for interacting with the PhonePe APIs
 *
 * @param clientId      Unique clientId assigned to merchant by PhonePe
 * @param clientSecret  Secret provided by PhonePe
 * @param clientVersion The client version used for secure transactions
 * @param env           Set to `Env.SANDBOX` for the SANDBOX environment  or `Env.PRODUCTION` for the production
 *                      environment.
 * @param shouldPublishEvents When true, events are sent to PhonePe providing smoother experience
 * @return CustomCheckoutClient object for interacting with the PhonePe APIs
 */
CustomCheckoutClient.getInstance = (clientId, clientSecret, clientVersion, env, shouldPublishEvents = true) => {
    shouldPublishEvents = shouldPublishEvents && env == Env_1.Env.PRODUCTION;
    if (CustomCheckoutClient._client == undefined) {
        CustomCheckoutClient._client = new CustomCheckoutClient(clientId, clientSecret, clientVersion, env, shouldPublishEvents);
        return CustomCheckoutClient._client;
    }
    const requestedClientSHA = CommonUtils_1.CommonUtils.calculateSha256({
        clientId: clientId,
        clientSecret: clientSecret,
        clientVersion: clientVersion,
        env: env,
        shouldPublishEvents: shouldPublishEvents,
        paymentFlowType: PaymentFlowType_1.PaymentFlowType.PG,
    });
    const cachedClientSHA = CommonUtils_1.CommonUtils.calculateSha256({
        clientId: CustomCheckoutClient._client.merchantConfig.clientId,
        clientSecret: CustomCheckoutClient._client.merchantConfig.clientSecret,
        clientVersion: CustomCheckoutClient._client.merchantConfig.clientVersion,
        env: CustomCheckoutClient._client.env,
        shouldPublishEvents: CustomCheckoutClient._client.shouldPublishEvents,
        paymentFlowType: PaymentFlowType_1.PaymentFlowType.PG,
    });
    if (requestedClientSHA == cachedClientSHA)
        return CustomCheckoutClient._client;
    throw new Exceptions_1.PhonePeException(Constants_2.Constants.CLIENT_EXCEPTION(CustomCheckoutClient._client.constructor.name));
};
