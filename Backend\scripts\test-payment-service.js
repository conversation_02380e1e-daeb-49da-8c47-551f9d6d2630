/**
 * Payment Service Test Script
 * 
 * Tests the PhonePe SDK integration and fallback mechanisms
 */

import { 
  initiatePayment, 
  verifyPayment, 
  getSDKStatus, 
  getPaymentConfig,
  healthCheck 
} from '../service/paymentservice.js';

async function testPaymentService() {
  console.log('🧪 Testing Payment Service Integration\n');

  try {
    // Test 1: Check SDK Status
    console.log('1️⃣ Checking SDK Status...');
    const sdkStatus = getSDKStatus();
    console.log('SDK Status:', JSON.stringify(sdkStatus, null, 2));
    
    if (sdkStatus.sdkAvailable) {
      console.log('✅ PhonePe SDK is available');
    } else {
      console.log('⚠️  PhonePe SDK not available, using fallback');
    }

    // Test 2: Check Configuration
    console.log('\n2️⃣ Checking Payment Configuration...');
    const config = getPaymentConfig();
    console.log('Configuration:', JSON.stringify(config, null, 2));

    // Test 3: Health Check
    console.log('\n3️⃣ Running Health Check...');
    const health = await healthCheck();
    console.log('Health Status:', JSON.stringify(health, null, 2));

    // Test 4: Test Payment Initiation (Dry Run)
    console.log('\n4️⃣ Testing Payment Initiation (Dry Run)...');
    const testPaymentData = {
      phone: '**********',
      price: 100, // ₹1.00 for testing
      transactionId: `TEST_${Date.now()}`
    };

    console.log('Test Payment Data:', testPaymentData);

    try {
      const paymentResponse = await initiatePayment(testPaymentData);
      console.log('✅ Payment initiation successful');
      console.log('Response:', JSON.stringify(paymentResponse, null, 2));

      // Test 5: Test Payment Verification
      if (paymentResponse.success) {
        console.log('\n5️⃣ Testing Payment Verification...');
        try {
          const verificationResponse = await verifyPayment({ 
            transactionId: testPaymentData.transactionId 
          });
          console.log('✅ Payment verification successful');
          console.log('Verification Response:', JSON.stringify(verificationResponse, null, 2));
        } catch (verifyError) {
          console.log('⚠️  Payment verification failed (expected for test transaction)');
          console.log('Verification Error:', verifyError.message);
        }
      }

    } catch (paymentError) {
      console.log('⚠️  Payment initiation failed (may be expected in test environment)');
      console.log('Payment Error:', paymentError.message);
    }

    // Test 6: Environment Validation
    console.log('\n6️⃣ Environment Validation...');
    const requiredEnvVars = [
      'PHONEPE_CLIENT_ID',
      'PHONEPE_CLIENT_SECRET',
      'BASE_URL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length === 0) {
      console.log('✅ All required environment variables are set');
    } else {
      console.log('❌ Missing environment variables:', missingVars);
    }

    // Test Summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log(`SDK Available: ${sdkStatus.sdkAvailable ? '✅' : '❌'}`);
    console.log(`Client Initialized: ${sdkStatus.clientInitialized ? '✅' : '❌'}`);
    console.log(`Environment: ${config.environment}`);
    console.log(`Fallback Mode: ${sdkStatus.fallbackMode ? '⚠️  Yes' : '✅ No'}`);
    console.log(`Health Status: ${health.status === 'healthy' ? '✅' : '❌'}`);

    if (sdkStatus.fallbackMode) {
      console.log('\n⚠️  Note: Running in fallback mode. This is normal if SDK is not installed.');
      console.log('   To install SDK, run: node scripts/install-phonepe-sdk.js');
    }

    console.log('\n🎉 Payment service test completed successfully!');

  } catch (error) {
    console.error('\n❌ Payment service test failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Additional utility functions for testing

/**
 * Test specific SDK functionality
 */
async function testSDKSpecific() {
  console.log('\n🔧 Testing SDK-Specific Features...');
  
  try {
    // Try to import SDK directly
    const phonePeModule = await import('phonepe-pg-sdk-node');
    console.log('✅ SDK module imported successfully');
    
    const PhonePeSDK = phonePeModule.default || phonePeModule.PhonePe || phonePeModule;
    if (typeof PhonePeSDK === 'function') {
      console.log('✅ SDK constructor available');
    } else {
      console.log('⚠️  SDK constructor not found');
    }
    
  } catch (error) {
    console.log('❌ SDK import failed:', error.message);
    console.log('   This is expected if SDK is not installed');
  }
}

/**
 * Test environment configuration
 */
function testEnvironmentConfig() {
  console.log('\n🌍 Testing Environment Configuration...');
  
  const envVars = {
    // PhonePe SDK Configuration
    PHONEPE_CLIENT_ID: process.env.PHONEPE_CLIENT_ID,
    PHONEPE_CLIENT_SECRET: process.env.PHONEPE_CLIENT_SECRET ? '***MASKED***' : undefined,
    PHONEPE_ENV: process.env.PHONEPE_ENV,
    PHONEPE_CLIENT_VERSION: process.env.PHONEPE_CLIENT_VERSION,
    BASE_URL: process.env.BASE_URL
  };
  
  console.log('Environment Variables:');
  Object.entries(envVars).forEach(([key, value]) => {
    const status = value ? '✅' : '❌';
    console.log(`  ${status} ${key}: ${value || 'NOT SET'}`);
  });
}

// Run tests based on command line arguments
const args = process.argv.slice(2);

if (args.includes('--sdk-only')) {
  testSDKSpecific();
} else if (args.includes('--env-only')) {
  testEnvironmentConfig();
} else if (args.includes('--all')) {
  testEnvironmentConfig();
  await testSDKSpecific();
  await testPaymentService();
} else {
  // Default: run main test
  await testPaymentService();
}

console.log('\n💡 Usage:');
console.log('  node scripts/test-payment-service.js           # Run main tests');
console.log('  node scripts/test-payment-service.js --sdk-only # Test SDK only');
console.log('  node scripts/test-payment-service.js --env-only # Test environment only');
console.log('  node scripts/test-payment-service.js --all      # Run all tests');
