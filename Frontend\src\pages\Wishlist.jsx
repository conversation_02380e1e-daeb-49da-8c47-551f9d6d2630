// Wishlist.jsx

import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import useStatus from "../hooks/useStatus";
import useToggle from "../hooks/useToggle";
import Login from "../components/Login";
import { useSelector } from "react-redux";
import { selectUserProfile } from "../app/users/usersSelector";
import { FiHeart, FiShoppingCart, FiTrash2, FiArrowRight, FiAlertCircle } from "react-icons/fi";

// Helper function to normalize product data
const normalizeProduct = (product) => {
  if (!product) {
    return {
      id: "unknown",
      name: "Unknown Product",
      color: "Default",
      price: 0,
      brand: "Unknown Brand",
      imageUrl: "https://via.placeholder.com/150",
    };
  }
  return {
    id: product.product_id || product.Product_id || "unknown",
    name: product.name || product.Name || "Unknown Product",
    color: product.color || product.Color || "Default",
    price: product.price || product.Price || 0,
    brand: product.brand || product.Brand || "Unknown Brand",
    imageUrl: product.imageurl || product.Imageurl || product.Image_url || "https://via.placeholder.com/150",
  };
};

// Wishlist Item Component
const WishlistItem = ({ product, onRemove, onAddToCart, isRemoving, isAddingToCart }) => {
  // Skip rendering if product is undefined or null
  if (!product) {
    return null;
  }

  const normalizedProduct = normalizeProduct(product);

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-4 mb-4 flex flex-col sm:flex-row items-center gap-4 relative overflow-hidden border border-gray-100">
      {/* Loading Overlay */}
      {(isRemoving || isAddingToCart) && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}

      {/* Product Image */}
      <div className="w-full sm:w-24 h-24 rounded-lg overflow-hidden bg-gray-100">
        <img
          src={normalizedProduct.imageUrl}
          alt={normalizedProduct.name}
          className="w-full h-full object-cover object-center"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23e2e8f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='14' fill='%231e293b' text-anchor='middle' dominant-baseline='middle'%3ENo Image%3C/text%3E%3C/svg%3E";
          }}
        />
      </div>

      {/* Product Details */}
      <div className="flex-grow text-center sm:text-left">
        <h3 className="text-lg font-medium text-gray-900 mb-1">{normalizedProduct.name}</h3>
        <p className="text-sm text-gray-500 mb-1">Color: {normalizedProduct.color}</p>
        <p className="text-sm text-gray-500 mb-2">Brand: {normalizedProduct.brand}</p>
        <p className="text-lg font-semibold text-gray-900">₹{normalizedProduct.price}</p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col items-end gap-2">
        <button
          onClick={() => onAddToCart(product)}
          disabled={isRemoving || isAddingToCart}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Add to cart"
        >
          <FiShoppingCart size={16} />
          <span>Add to Cart</span>
        </button>

        <button
          onClick={() => onRemove(normalizedProduct.id)}
          disabled={isRemoving || isAddingToCart}
          className="text-red-500 hover:text-red-700 flex items-center gap-1 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Remove from wishlist"
        >
          <FiTrash2 size={16} />
          <span>Remove</span>
        </button>
      </div>
    </div>
  );
};

// Loading Skeleton Component
const WishlistItemSkeleton = () => (
  <div className="bg-white rounded-xl shadow-sm p-4 mb-4 flex flex-col sm:flex-row items-center gap-4 animate-pulse">
    <div className="w-full sm:w-24 h-24 bg-gray-200 rounded-lg"></div>
    <div className="flex-grow">
      <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
      <div className="h-6 bg-gray-200 rounded w-1/4"></div>
    </div>
    <div className="flex flex-col gap-2">
      <div className="h-10 bg-gray-200 rounded w-32"></div>
      <div className="h-6 bg-gray-200 rounded w-20"></div>
    </div>
  </div>
);

// Empty Wishlist Component
const EmptyWishlist = () => (
  <div className="bg-white rounded-xl shadow-sm p-8 text-center">
    <div className="w-20 h-20 mx-auto mb-4 flex items-center justify-center rounded-full bg-gray-100">
      <FiHeart size={32} className="text-gray-400" />
    </div>
    <h2 className="text-2xl font-medium text-gray-900 mb-2">Your wishlist is empty</h2>
    <p className="text-gray-500 mb-6 max-w-md mx-auto">
      Looks like you haven't added anything to your wishlist yet. Explore our products and find something you'll love!
    </p>
    <Link
      to="/"
      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-pink-600 hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500"
    >
      Start Shopping
      <FiArrowRight className="ml-2" />
    </Link>
  </div>
);

const Wishlist = () => {
  const location = useLocation();
  const { data: statusData, error: statusError, loading: statusLoading, refreshData } = useStatus();
  const { toggleWishlist, toggleCart } = useToggle();
  const [localError, setLocalError] = useState(null);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [removingItems, setRemovingItems] = useState({});
  const [addingToCartItems, setAddingToCartItems] = useState({});
  // Get user from Redux state
  const user = useSelector(selectUserProfile);

  // Debug user state changes
  useEffect(() => {
    console.log("User state changed:", user);
  }, [user]);

  // State to track if we're checking authentication
  const [checkingAuth, setCheckingAuth] = useState(true);

  // Reference to store the timeout
  const authTimeoutRef = React.useRef(null);

  // Initial setup - always show loader on mount
  useEffect(() => {
    // Always show loader initially
    setCheckingAuth(true);

    // Hide login popup while loading
    setShowLoginPopup(false);

    // Set a timeout to ensure we don't show the loader forever
    // in case user state never loads
    authTimeoutRef.current = setTimeout(() => {
      setCheckingAuth(false);

      // Only show login popup if user is not authenticated
      if (!user) {
        setShowLoginPopup(true);
      }
    }, 2000); // 2 second maximum wait

    // Clean up timeout if component unmounts
    return () => {
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
      }
    };
  }, []); // Only run on mount

  // Handle user state changes
  useEffect(() => {
    // If we have a user and we're still checking auth, we can stop checking
    if (user && checkingAuth) {
      console.log("User authenticated, stopping loader");

      // Clear any existing timeout
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
        authTimeoutRef.current = null;
      }

      // Hide loader and login popup
      setCheckingAuth(false);
      setShowLoginPopup(false);
    }
  }, [user, checkingAuth]);

  // Handle authentication errors
  useEffect(() => {
    // Check for API errors that might indicate authentication issues
    if (statusError && statusError.includes('401')) {
      // If API returns 401, show the login popup
      setShowLoginPopup(true);
      setCheckingAuth(false);
    }
  }, [statusError]);

  // Filter products that have wishlist: true
  const wishlistProducts = statusData?.products?.filter(item => item.wishlist) || [];

  // Handle item removal from wishlist
  const handleRemoveItem = async (productId) => {
    try {
      // Set loading state for this item
      setRemovingItems(prev => ({ ...prev, [productId]: true }));

      // Find the product in the wishlist
      const productToRemove = wishlistProducts.find(p =>
        (p.product_id === productId || p.Product_id === productId)
      );

      if (!productToRemove) {
        console.error(`Product with ID ${productId} not found in wishlist`);
        return;
      }

      // Call the toggleWishlist function to remove the item
      await toggleWishlist(productToRemove);

      // Refresh data from server
      refreshData();
    } catch (error) {
      console.error(`Error removing item ${productId} from wishlist:`, error);
      setLocalError("Failed to remove item from wishlist. Please try again.");
    } finally {
      // Clear loading state for this item
      setRemovingItems(prev => ({ ...prev, [productId]: false }));
    }
  };

  // Handle adding item to cart
  const handleAddToCart = async (product) => {
    const productId = product.product_id || product.Product_id;

    try {
      // Set loading state for this item
      setAddingToCartItems(prev => ({ ...prev, [productId]: true }));

      // Call the toggleCart function to add the item
      await toggleCart(product);

      // Refresh data from server
      refreshData();

      // Show success message
      setLocalError(null);
    } catch (error) {
      console.error(`Error adding item to cart:`, error);
      setLocalError("Failed to add item to cart. Please try again.");
    } finally {
      // Clear loading state for this item
      setAddingToCartItems(prev => ({ ...prev, [productId]: false }));
    }
  };

  // Show loader while checking authentication or loading status data
  if (checkingAuth || statusLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
            <div className="animate-pulse flex items-center">
              <div className="h-6 bg-gray-200 rounded px-4 py-1 text-gray-500">
                {checkingAuth ? "Loading your profile..." : "Loading your wishlist..."}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <WishlistItemSkeleton key={i} />
            ))}
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (statusError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 rounded-xl p-6 text-center">
            <FiAlertCircle className="mx-auto h-12 w-12 text-red-400" />
            <h2 className="mt-2 text-lg font-medium text-red-800">Error loading your wishlist</h2>
            <p className="mt-1 text-sm text-red-700">{statusError}</p>
            <div className="mt-6">
              <button
                onClick={refreshData}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
          <p className="text-gray-500">{wishlistProducts.length} {wishlistProducts.length === 1 ? 'item' : 'items'}</p>
        </div>

        {/* Error Message */}
        {localError && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{localError}</p>
          </div>
        )}

        {/* Wishlist Content */}
        {wishlistProducts.length === 0 ? (
          <EmptyWishlist />
        ) : (
          <div className="space-y-4">
            {wishlistProducts.map((product) => {
              if (!product) return null;
              const productId = product.product_id || product.Product_id;
              if (!productId) return null;
              return (
                <WishlistItem
                  key={productId}
                  product={product}
                  onRemove={handleRemoveItem}
                  onAddToCart={handleAddToCart}
                  isRemoving={removingItems[productId]}
                  isAddingToCart={addingToCartItems[productId]}
                />
              );
            })}
          </div>
        )}
      </div>
      <Footer />

      {/* Login Popup */}
      <Login
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        redirectUrl={location.pathname}
      />
    </div>
  );
};

export default Wishlist;
