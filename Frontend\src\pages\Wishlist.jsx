// Wishlist.jsx

import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import useStatus from "../hooks/useStatus";
import useToggle from "../hooks/useToggle";
import Login from "../components/Login";
import { useSelector } from "react-redux";
import { selectUserProfile } from "../app/users/usersSelector";

const Wishlist = () => {
  const location = useLocation();
  const { data: statusData, error: statusError, loading: statusLoading, refreshData } = useStatus();
  const { toggleWishlist } = useToggle();
  const [localError, setLocalError] = useState(null);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  // Get user from Redux state
  const user = useSelector(selectUserProfile);

  // Debug user state changes
  useEffect(() => {
    console.log("User state changed:", user);
  }, [user]);

  // State to track if we're checking authentication
  const [checkingAuth, setCheckingAuth] = useState(true);

  // Reference to store the timeout
  const authTimeoutRef = React.useRef(null);

  // Initial setup - always show loader on mount
  useEffect(() => {
    // Always show loader initially
    setCheckingAuth(true);

    // Hide login popup while loading
    setShowLoginPopup(false);

    // Set a timeout to ensure we don't show the loader forever
    // in case user state never loads
    authTimeoutRef.current = setTimeout(() => {
      setCheckingAuth(false);

      // Only show login popup if user is not authenticated
      if (!user) {
        setShowLoginPopup(true);
      }
    }, 2000); // 2 second maximum wait

    // Clean up timeout if component unmounts
    return () => {
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
      }
    };
  }, []); // Only run on mount

  // Handle user state changes
  useEffect(() => {
    // If we have a user and we're still checking auth, we can stop checking
    if (user && checkingAuth) {
      console.log("User authenticated, stopping loader");

      // Clear any existing timeout
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
        authTimeoutRef.current = null;
      }

      // Hide loader and login popup
      setCheckingAuth(false);
      setShowLoginPopup(false);
    }
  }, [user, checkingAuth]);

  // Handle authentication errors
  useEffect(() => {
    // Check for API errors that might indicate authentication issues
    if (statusError && statusError.includes('401')) {
      // If API returns 401, show the login popup
      setShowLoginPopup(true);
      setCheckingAuth(false);
    }
  }, [statusError]);

  // Filter products that have wishlist: true
  const wishlistProducts = statusData?.products?.filter(item => item.wishlist) || [];

  const handleLikeClick = async (product) => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setShowLoginPopup(true);
      return;
    }
    const { product_id, Name, Color, Price } = product;
    try {
      await toggleWishlist(product_id, Name, Color, Price);
      refreshData();
    } catch (err) {
      setLocalError(err.message);
    }
  };

  // Show loader while checking authentication or loading status data
  if (checkingAuth || statusLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <div className="text-center">
            <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
            <div className="mt-4">
              {checkingAuth ? "Loading your profile..." : "Loading your favorites..."}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (statusError) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <div className="text-red-500 text-center">{statusError}</div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      <section className="py-12 px-6 max-w-7xl mx-auto flex-grow">
        <div className="text-center mb-12">
          {localError && <p className="text-red-500 mt-2">{localError}</p>}
        </div>

        {wishlistProducts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">Your favorites collection is empty</p>
            <Link to="/" className="text-blue-600 hover:text-blue-800">
              Continue Shopping
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
            {wishlistProducts.map((product) => (
              <div key={product.product_id} className="group relative">
                <div className="aspect-square w-full overflow-hidden rounded-xl bg-gray-100 transform will-change-transform transition duration-300 group-hover:scale-105">
                  <Link
                    to={`/products/${product.product_id}?color=${product.Color || 'default'}&price=${product.Price || '0'}`}
                    state={{ product }}
                  >
                    <img
                      src={product.Image_url || product.Imageurl}
                      alt={product.Name}
                      loading="lazy"
                      className="h-full w-full object-cover object-center"
                    />
                  </Link>
                </div>
                <div className="mt-4 flex justify-between">
                  <h3 className="text-sm text-gray-700 font-medium">
                    {product.Name}
                  </h3>
                  <p className="text-sm font-semibold text-gray-900">
                    ₹{product.Price}
                  </p>
                </div>
                <button
                  onClick={() => handleLikeClick(product)}
                  className="absolute top-3 left-3 p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white transition text-red-500"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 32 29.6">
                    <path d="M23.6,0c-3.4,0-6.3,2.7-7.6,5.6C14.7,2.7,11.8,0,8.4,0C3.8,0,0,3.8,0,8.4c0,9.4,9.5,11.9,16,21.2c6.1-9.3,16-12.1,16-21.2C32,3.8,28.2,0,23.6,0z" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}
      </section>
      <Footer />

      {/* Login Popup */}
      <Login
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        redirectUrl={location.pathname}
      />
    </div>
  );
};

export default Wishlist;
