"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueuedEventPublisher = void 0;
const EventPublisher_1 = require("./EventPublisher");
const Constants_1 = require("../Constants");
const HttpCommand_1 = require("../../http/HttpCommand");
const BulkEvent_1 = require("../models/BulkEvent");
const Headers_1 = require("../../constants/Headers");
const http_1 = require("../../http");
class QueuedEventPublisher extends EventPublisher_1.EventPublisher {
    constructor(httpClient, hostUrl, queueHandler) {
        super();
        this._httpCommand = new HttpCommand_1.HttpCommand(hostUrl, httpClient);
        this._queueHandler = queueHandler;
        this.scheduler = false;
    }
    setAuthTokenSupplier(authTokenSupplier) {
        this._authTokenSupplier = authTokenSupplier;
    }
    startPublishingEvents(authTokenSupplier) {
        this._authTokenSupplier = authTokenSupplier;
        if (this.scheduler == false) {
            this.scheduler = true;
            setTimeout(() => this.run(), Constants_1.Constants.INITIAL_DELAY);
        }
    }
    send(event) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this._queueHandler.add(event);
        });
    }
    run() {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.scheduler) {
                yield this.sendEvents();
                setTimeout(() => this.run(), Constants_1.Constants.DELAY);
            }
        });
    }
    sendEvents() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this._queueHandler.isEmpty()) {
                    return;
                }
                const bulkEventBatch = yield this.createEventBatches();
                bulkEventBatch.forEach((sdkEventList) => __awaiter(this, void 0, void 0, function* () {
                    try {
                        yield this.sendBatchData(sdkEventList);
                    }
                    catch (error) {
                        console.error('Error occurred sending events batch to backend', error);
                    }
                }));
            }
            catch (error) {
                console.error('Error occurred sending events batch to backend', error);
            }
        });
    }
    sendBatchData(sdkEventList) {
        return __awaiter(this, void 0, void 0, function* () {
            const bulkEvent = BulkEvent_1.BulkEvent.builder().events(sdkEventList).build();
            const headers = yield this.getHeaders();
            yield this._httpCommand.request(Constants_1.Constants.EVENTS_ENDPOINT, http_1.HttpMethodType.POST, headers, bulkEvent);
        });
    }
    getHeaders() {
        return __awaiter(this, void 0, void 0, function* () {
            return {
                [Headers_1.Headers.ACCEPT]: Headers_1.Headers.APPLICATION_JSON,
                [Headers_1.Headers.OAUTH_AUTHORIZATION]: yield this._authTokenSupplier(),
                [Headers_1.Headers.CONTENT_TYPE]: Headers_1.Headers.APPLICATION_JSON,
            };
        });
    }
    createEventBatches() {
        return __awaiter(this, void 0, void 0, function* () {
            const CUR_QUEUE_SIZE = this._queueHandler.size();
            const bulkEventBatch = [];
            let currentBatch = [];
            for (let numEventsProcessed = 0; numEventsProcessed < CUR_QUEUE_SIZE; numEventsProcessed++) {
                const event = yield this._queueHandler.poll();
                if (event == undefined) {
                    break;
                }
                currentBatch.push(event);
                if (currentBatch.length == Constants_1.Constants.MAX_EVENTS_IN_BATCH) {
                    bulkEventBatch.push(currentBatch);
                    currentBatch = [];
                }
            }
            if (currentBatch.length != 0) {
                bulkEventBatch.push(currentBatch);
            }
            return bulkEventBatch;
        });
    }
}
exports.QueuedEventPublisher = QueuedEventPublisher;
