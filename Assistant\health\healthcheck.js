import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { Pinecone } from '@pinecone-database/pinecone';

/**
 * Test Gemini AI connectivity with secure error handling
 * @returns {Object} - Sanitized service status
 */
const testGeminiConnectivity = async () => {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      return { status: 'error' };
    }

    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Simple test request with timeout
    const result = await Promise.race([
      model.generateContent("Health check"),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
    ]);

    if (result && result.response) {
      return { status: 'healthy' };
    } else {
      return { status: 'error' };
    }
  } catch (error) {
    return { status: 'error' };
  }
};

/**
 * Test Pinecone connectivity with secure error handling
 * @returns {Object} - Sanitized service status
 */
const testPineconeConnectivity = async () => {
  try {
    const apiKey = process.env.PINECONE_API_KEY;
    const indexName = process.env.PINECONE_INDEX_NAME;

    if (!apiKey) {
      return { status: 'warning' };
    }

    const pinecone = new Pinecone({ apiKey });

    // Test connection with timeout
    const indexesPromise = pinecone.listIndexes();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Timeout')), 5000)
    );

    const indexes = await Promise.race([indexesPromise, timeoutPromise]);

    // Check if our specific index exists
    const indexExists = indexes.indexes?.some(index => index.name === indexName);

    if (indexExists) {
      return { status: 'healthy' };
    } else {
      return { status: 'warning' };
    }
  } catch (error) {
    return { status: 'warning' };
  }
};

/**
 * Test MCP server connectivity with secure error handling
 * @returns {Object} - Sanitized service status
 */
const testMCPConnectivity = async () => {
  try {
    const mcpUrl = process.env.MCP_SERVER_URL;
    if (!mcpUrl) {
      console.error('Health Check: MCP server URL not configured');
      return { status: 'error' };
    }

    // Test MCP health endpoint with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${mcpUrl}/mcp/health`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Belilly-AI-Assistant-Lambda/1.0'
      }
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      console.log('Health Check: MCP server healthy');
      return { status: 'healthy' };
    } else {
      console.error('Health Check: MCP server returned error status:', response.status);
      return { status: 'error' };
    }
  } catch (error) {
    console.error('Health Check: MCP server connectivity error:', error.message);
    return { status: 'error' };
  }
};

/**
 * Main health check handler
 * @param {Object} event - Lambda event
 * @returns {Object} - Lambda response
 */
export const handleHealthCheck = async (event) => {
  try {
    console.log('Health check request received');

    // Run all health checks in parallel with timeout
    const healthCheckPromise = Promise.all([
      testGeminiConnectivity(),
      testPineconeConnectivity(),
      testMCPConnectivity()
    ]);

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Health check timeout')), 10000)
    );

    const [geminiStatus, pineconeStatus, mcpStatus] = await Promise.race([
      healthCheckPromise,
      timeoutPromise
    ]);

    // Determine overall health status
    const hasErrors = [geminiStatus, pineconeStatus, mcpStatus].some(
      service => service.status === 'error'
    );

    const hasWarnings = [geminiStatus, pineconeStatus, mcpStatus].some(
      service => service.status === 'warning'
    );

    const overallStatus = hasErrors ? 'unhealthy' : hasWarnings ? 'degraded' : 'healthy';
    const statusCode = hasErrors ? 503 : 200;

    // Sanitized response - no sensitive information
    const healthData = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      services: {
        ai: { status: geminiStatus.status },
        memory: { status: pineconeStatus.status },
        backend: { status: mcpStatus.status }
      }
    };

    console.log('Health check completed:', overallStatus);

    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      },
      body: JSON.stringify(healthData)
    };

  } catch (error) {
    console.error('Health check error:', error.message);

    // Generic error response - no sensitive information
    const errorResponse = {
      status: 'error',
      timestamp: new Date().toISOString(),
      message: 'Health check failed'
    };

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      },
      body: JSON.stringify(errorResponse)
    };
  }
};

export default { handleHealthCheck };
