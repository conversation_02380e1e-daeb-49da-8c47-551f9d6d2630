{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 00:13:19.905"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 00:13:19.986"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 00:13:20.114"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 00:13:20.118"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-21 00:13:20.408"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 00:13:40.050"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 00:13:40.056"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 00:13:40.058"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 00:13:40.059"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 00:13:40.105"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 00:13:40.118"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 00:13:40.137"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 00:13:40.139"}
{"environment":"production","level":"info","message":"HTTP server running on port 3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 00:13:40.249"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 12:24:51.002"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 12:24:51.034"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 12:24:51.048"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 12:24:51.060"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 12:24:51.300"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 12:24:51.387"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 12:24:51.424"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 12:24:51.449"}
{"environment":"production","level":"info","message":"HTTP server running on port 3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 12:24:51.981"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 12:40:46.224"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 12:40:46.320"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 12:40:47.367"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 12:40:47.373"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 12:41:06.655"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 12:41:06.664"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 12:41:06.666"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 12:41:06.710"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 12:41:06.797"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 12:41:06.809"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 12:41:06.818"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 12:41:06.893"}
{"environment":"production","level":"info","message":"HTTP server running on port 5000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 12:41:07.056"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 13:38:04.321"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 13:38:04.335"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 13:38:04.455"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 13:38:04.473"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 13:38:04.621"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 13:38:04.636"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 13:38:04.658"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 13:38:04.693"}
{"environment":"production","level":"info","message":"HTTP server running on port 5000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 13:38:04.923"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-21 13:44:43.334"}
{"data":{},"environment":"production","errorType":"UNKNOWN_ERROR","ip":"::1","isOperational":false,"level":"error","message":"Not allowed by CORS","method":"POST","path":"/v1/redirect/purchases/status","requestId":"63deb9d9-a6c3-4f6c-b9c0-35e85219268d","service":"belilly-api","stack":"Error: Not allowed by CORS\n    at origin (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/server.js:186:16)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:212:5","statusCode":500,"timestamp":"2025-06-21 13:45:10.241","userId":"anonymous"}
{"environment":"production","level":"error","message":"CRITICAL ERROR - Consider restarting the process","service":"belilly-api","timestamp":"2025-06-21 13:45:10.453"}
{"data":{"resource":"/.well-known/appspecific/com.chrome.devtools.json"},"environment":"production","errorType":"NOT_FOUND_ERROR","ip":"::1","isOperational":true,"level":"warn","message":"Resource not found - /.well-known/appspecific/com.chrome.devtools.json","method":"GET","path":"/.well-known/appspecific/com.chrome.devtools.json","requestId":"3b654da6-9e65-4656-8e36-c84157fe8d86","service":"belilly-api","stack":"ApiError: Resource not found - /.well-known/appspecific/com.chrome.devtools.json\n    at ApiError.notFound (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:232:12)\n    at notFoundHandler (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:364:26)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at requestIdMiddleware (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:486:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)","statusCode":404,"timestamp":"2025-06-21 13:45:12.172","userId":"anonymous"}
{"data":{},"environment":"production","errorType":"UNKNOWN_ERROR","ip":"::1","isOperational":false,"level":"error","message":"Not allowed by CORS","method":"OPTIONS","path":"/.well-known/appspecific/com.chrome.devtools.json","requestId":"9dafaa13-46cb-49b8-9b13-b69e3855a9bd","service":"belilly-api","stack":"Error: Not allowed by CORS\n    at origin (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/server.js:186:16)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:212:5","statusCode":500,"timestamp":"2025-06-21 13:45:12.270","userId":"anonymous"}
{"environment":"production","level":"error","message":"CRITICAL ERROR - Consider restarting the process","service":"belilly-api","timestamp":"2025-06-21 13:45:12.285"}
{"data":{"resource":"/favicon.ico"},"environment":"production","errorType":"NOT_FOUND_ERROR","ip":"::1","isOperational":true,"level":"warn","message":"Resource not found - /favicon.ico","method":"GET","path":"/favicon.ico","requestId":"a7605dac-f1cd-4336-a9a1-838b5e314956","service":"belilly-api","stack":"ApiError: Resource not found - /favicon.ico\n    at ApiError.notFound (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:232:12)\n    at notFoundHandler (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:364:26)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at requestIdMiddleware (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:486:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)","statusCode":404,"timestamp":"2025-06-21 13:45:12.367","userId":"anonymous"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 14:15:44.772"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 14:15:44.983"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 14:15:45.306"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 14:15:45.310"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-21 14:15:45.444"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 14:15:59.408"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 14:15:59.413"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 14:15:59.414"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 14:15:59.416"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 14:15:59.522"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 14:15:59.543"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 14:15:59.549"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 14:15:59.560"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 14:15:59.666"}
{"data":{"resource":"/v1/products/counter"},"environment":"production","errorType":"NOT_FOUND_ERROR","ip":"127.0.0.1","isOperational":true,"level":"warn","message":"Resource not found - /v1/products/counter","method":"GET","path":"/v1/products/counter","requestId":"8d2c32b1-9eaa-4ab5-a302-fd5861fb6eb0","service":"belilly-api","stack":"ApiError: Resource not found - /v1/products/counter\n    at ApiError.notFound (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:232:12)\n    at notFoundHandler (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:364:26)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at requestIdMiddleware (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:486:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)","statusCode":404,"timestamp":"2025-06-21 14:16:48.508","userId":"anonymous"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 15:01:08.127"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 15:01:08.196"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 15:01:08.302"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 15:01:08.303"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 15:01:20.223"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 15:01:20.229"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 15:01:20.230"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 15:01:20.231"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 15:01:20.281"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 15:01:20.288"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 15:01:20.297"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 15:01:20.304"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 15:01:20.437"}
{"data":{"resource":"/v1/products/counter"},"environment":"production","errorType":"NOT_FOUND_ERROR","ip":"127.0.0.1","isOperational":true,"level":"warn","message":"Resource not found - /v1/products/counter","method":"GET","path":"/v1/products/counter","requestId":"c18f11e5-1055-43cf-a46a-7fde407eab46","service":"belilly-api","stack":"ApiError: Resource not found - /v1/products/counter\n    at ApiError.notFound (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:232:12)\n    at notFoundHandler (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:364:26)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at requestIdMiddleware (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:486:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)","statusCode":404,"timestamp":"2025-06-21 15:01:37.367","userId":"anonymous"}
{"data":{"resource":"/v1/products/counter"},"environment":"production","errorType":"NOT_FOUND_ERROR","ip":"127.0.0.1","isOperational":true,"level":"warn","message":"Resource not found - /v1/products/counter","method":"GET","path":"/v1/products/counter","requestId":"796142dc-efea-495c-9d90-dbafecf291ee","service":"belilly-api","stack":"ApiError: Resource not found - /v1/products/counter\n    at ApiError.notFound (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:232:12)\n    at notFoundHandler (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:364:26)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at requestIdMiddleware (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/errorhandler.js:486:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)","statusCode":404,"timestamp":"2025-06-21 15:02:25.007","userId":"anonymous"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-21 15:36:59.098"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-21 15:36:59.107"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-21 15:36:59.108"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-21 15:36:59.108"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-21 15:36:59.176"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-21 15:36:59.184"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-21 15:36:59.189"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-21 15:36:59.194"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-21 15:36:59.366"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesCod","service":"belilly-api","timestamp":"2025-06-21 15:50:22.657"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 16:53:44.237"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 16:53:44.418"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-21 16:53:44.644"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-21 16:53:44.654"}
