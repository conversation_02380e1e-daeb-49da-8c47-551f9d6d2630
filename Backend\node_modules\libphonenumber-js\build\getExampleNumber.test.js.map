{"version": 3, "file": "getExampleNumber.test.js", "names": ["describe", "it", "phoneNumber", "getExampleNumber", "examples", "metadata", "nationalNumber", "should", "equal", "number", "countryCallingCode", "country", "expect", "to", "be", "undefined"], "sources": ["../source/getExampleNumber.test.js"], "sourcesContent": ["import examples from '../examples.mobile.json' assert { type: 'json' }\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport getExampleNumber from './getExampleNumber.js'\r\n\r\ndescribe('getExampleNumber', () => {\r\n\tit('should get an example number', () => {\r\n\t\tconst phoneNumber = getExampleNumber('RU', examples, metadata)\r\n\t\tphoneNumber.nationalNumber.should.equal('9123456789')\r\n\t\tphoneNumber.number.should.equal('+79123456789')\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t})\r\n\r\n\tit('should handle a non-existing country', () => {\r\n\t\texpect(getExampleNumber('XX', examples, metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;AAEAA,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAMC,WAAW,GAAG,IAAAC,4BAAA,EAAiB,IAAjB,EAAuBC,0BAAvB,EAAiCC,uBAAjC,CAApB;IACAH,WAAW,CAACI,cAAZ,CAA2BC,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAN,WAAW,CAACO,MAAZ,CAAmBF,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;IACAN,WAAW,CAACQ,kBAAZ,CAA+BH,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAN,WAAW,CAACS,OAAZ,CAAoBJ,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;EACA,CANC,CAAF;EAQAP,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChDW,MAAM,CAAC,IAAAT,4BAAA,EAAiB,IAAjB,EAAuBC,0BAAvB,EAAiCC,uBAAjC,CAAD,CAAN,CAAmDQ,EAAnD,CAAsDC,EAAtD,CAAyDC,SAAzD;EACA,CAFC,CAAF;AAGA,CAZO,CAAR"}