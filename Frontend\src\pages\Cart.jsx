// Cart.jsx

import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import Navbar from "../components/Navbar";
import Footer from "../components/Footer";
import useStatus from "../hooks/useStatus";
import useToggle from "../hooks/useToggle";
import { useSelector } from "react-redux";
import { selectToggleState } from "../app/toggle/toggleSelector";
import { selectUserProfile } from "../app/users/usersSelector";
import Login from "../components/Login";
import Cookies from "js-cookie";
import secureStorage from "../utils/secureStorage";
import { FiShoppingBag, FiTrash2, FiMinus, FiPlus, FiArrowRight, FiShoppingCart, FiAlertCircle } from "react-icons/fi";

// Helper function to normalize product data
const normalizeProduct = (product) => {
  if (!product) {
    return {
      id: "unknown",
      name: "Unknown Product",
      color: "Default",
      price: 0,
      brand: "Unknown Brand",
      imageUrl: "https://via.placeholder.com/150",
    };
  }
  return {
    id: product.product_id || product.Product_id || "unknown",
    name: product.name || product.Name || "Unknown Product",
    color: product.color || product.Color || "Default",
    price: product.price || product.Price || 0,
    brand: product.brand || product.Brand || "Unknown Brand",
    imageUrl: product.imageurl || product.Imageurl || product.Image_url || "https://via.placeholder.com/150",
  };
};

// Cart Item Component
const CartItem = ({ product, quantity, onQuantityChange, onRemove, isRemoving }) => {
  // Skip rendering if product is undefined or null
  if (!product) {
    return null;
  }

  const normalizedProduct = normalizeProduct(product);

  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-4 mb-4 flex flex-col sm:flex-row items-center gap-4 relative overflow-hidden border border-gray-100">
      {/* Loading Overlay */}
      {isRemoving && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}

      {/* Product Image */}
      <div className="w-full sm:w-24 h-24 rounded-lg overflow-hidden bg-gray-100">
        <img
          src={normalizedProduct.imageUrl}
          alt={normalizedProduct.name}
          className="w-full h-full object-cover object-center"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23e2e8f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='14' fill='%231e293b' text-anchor='middle' dominant-baseline='middle'%3ENo Image%3C/text%3E%3C/svg%3E";
          }}
        />
      </div>

      {/* Product Details */}
      <div className="flex-grow text-center sm:text-left">
        <h3 className="text-lg font-medium text-gray-900 mb-1">{normalizedProduct.name}</h3>
        <p className="text-sm text-gray-500 mb-1">Color: {normalizedProduct.color}</p>
        <p className="text-lg font-semibold text-gray-900 mb-2">₹{normalizedProduct.price}</p>

        {/* Quantity Controls */}
        <div className="flex items-center justify-center sm:justify-start gap-2">
          <button
            onClick={() => onQuantityChange(normalizedProduct.id, quantity - 1)}
            disabled={quantity <= 1 || isRemoving}
            className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Decrease quantity"
          >
            <FiMinus size={16} />
          </button>

          <input
            type="number"
            min="1"
            max="10"
            value={quantity}
            onChange={(e) => onQuantityChange(normalizedProduct.id, parseInt(e.target.value))}
            disabled={isRemoving}
            className="w-12 h-8 text-center border border-gray-300 rounded-md mx-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:bg-gray-100"
            aria-label="Quantity"
          />

          <button
            onClick={() => onQuantityChange(normalizedProduct.id, quantity + 1)}
            disabled={quantity >= 10 || isRemoving}
            className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Increase quantity"
          >
            <FiPlus size={16} />
          </button>
        </div>
      </div>

      {/* Item Total & Remove Button */}
      <div className="flex flex-col items-end gap-2">
        <div className="text-right">
          <p className="text-sm text-gray-500">Item Total</p>
          <p className="text-lg font-bold text-gray-900">₹{(normalizedProduct.price * quantity).toFixed(2)}</p>
        </div>

        <button
          onClick={() => onRemove(normalizedProduct.id)}
          disabled={isRemoving}
          className="text-red-500 hover:text-red-700 flex items-center gap-1 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Remove item"
        >
          <FiTrash2 size={16} />
          <span>Remove</span>
        </button>
      </div>
    </div>
  );
};

// Loading Skeleton Component
const CartItemSkeleton = () => (
  <div className="bg-white rounded-xl shadow-sm p-4 mb-4 flex flex-col sm:flex-row items-center gap-4 animate-pulse">
    <div className="w-full sm:w-24 h-24 bg-gray-200 rounded-lg"></div>
    <div className="flex-grow">
      <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
      <div className="h-6 bg-gray-200 rounded w-1/4 mb-3"></div>
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        <div className="w-12 h-8 bg-gray-200 rounded-md"></div>
        <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
      </div>
    </div>
    <div>
      <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
      <div className="h-6 bg-gray-200 rounded w-24 mb-3"></div>
      <div className="h-4 bg-gray-200 rounded w-16"></div>
    </div>
  </div>
);

// Empty Cart Component
const EmptyCart = () => (
  <div className="bg-white rounded-xl shadow-sm p-8 text-center">
    <div className="w-20 h-20 mx-auto mb-4 flex items-center justify-center rounded-full bg-gray-100">
      <FiShoppingCart size={32} className="text-gray-400" />
    </div>
    <h2 className="text-2xl font-medium text-gray-900 mb-2">Your Cart is empty</h2>
    <p className="text-gray-500 mb-6 max-w-md mx-auto">
      Looks like you haven't added anything to your cart yet. Explore our products and find something you'll love!
    </p>
    <Link
      to="/"
      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
    >
      Start Shopping
      <FiArrowRight className="ml-2" />
    </Link>
  </div>
);

// Main cart Component
const cart = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { data: statusData, error: statusError, loading: statusLoading, refreshData } = useStatus();
  const { toggleCart } = useToggle();
  // We use useSelector to access the toggle state, but we don't need to use the values directly
  useSelector(selectToggleState);
  // Get user from Redux state
  const user = useSelector(selectUserProfile);
  const [quantities, setQuantities] = useState(() => {
    // Use secure storage instead of localStorage
    return secureStorage.getTemporary("cartQuantities", {});
  });
  const [removingItems, setRemovingItems] = useState({});
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  // State to track if we're checking authentication
  const [checkingAuth, setCheckingAuth] = useState(true);

  // Reference to store the timeout
  const authTimeoutRef = React.useRef(null);

  // Initial setup - always show loader on mount
  useEffect(() => {
    // Always show loader initially
    setCheckingAuth(true);

    // Hide login popup while loading
    setShowLoginPopup(false);

    // Set a timeout to ensure we don't show the loader forever
    // in case user state never loads
    authTimeoutRef.current = setTimeout(() => {
      setCheckingAuth(false);

      // Only show login popup if user is not authenticated
      if (!user) {
        setShowLoginPopup(true);
      }
    }, 1000); // 1 second delay

    // Clean up timeout if component unmounts
    return () => {
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
      }
    };
  }, []);

  // Handle user state changes
  useEffect(() => {
    // If we have a user and we're still checking auth, we can stop checking
    if (user && checkingAuth) {
      // Clear any existing timeout
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
        authTimeoutRef.current = null;
      }

      // Hide loader and login popup
      setCheckingAuth(false);
      setShowLoginPopup(false);
    }
  }, [user, checkingAuth]);

  // Handle authentication errors
  useEffect(() => {
    // Check for API errors that might indicate authentication issues
    if (statusError && statusError.includes('401')) {
      // If API returns 401, show the login popup
      setShowLoginPopup(true);
      setCheckingAuth(false);
    }
  }, [statusError]);

  // Filter products that have cart: true and are valid (not null or undefined)
  const cartProducts = statusData?.products?.filter(item => item && item.cart) || [];

  // Save quantities to secure storage whenever they change
  useEffect(() => {
    secureStorage.setTemporary("cartQuantities", quantities);
  }, [quantities]);

  // Handle quantity changes
  const handleQuantityChange = (productId, newQuantity) => {
    if (isNaN(newQuantity) || newQuantity < 1) newQuantity = 1;
    if (newQuantity > 10) newQuantity = 10;

    setQuantities(prev => ({
      ...prev,
      [productId]: newQuantity
    }));
  };

  // Handle item removal
  const handleRemoveItem = async (productId) => {
    try {
      // Set loading state for this item
      setRemovingItems(prev => ({ ...prev, [productId]: true }));

      // Find the product in the cart
      const productToRemove = cartProducts.find(p =>
        (p.product_id === productId || p.Product_id === productId)
      );

      if (!productToRemove) {
        console.error(`Product with ID ${productId} not found in cart`);
        return;
      }

      // Call the toggleCart function to remove the item
      await toggleCart(productToRemove);

      // Remove from quantities
      const newQuantities = { ...quantities };
      delete newQuantities[productId];
      setQuantities(newQuantities);

      // Refresh data from server
      refreshData();
    } catch (error) {
      console.error(`Error removing item ${productId} from cart:`, error);
    } finally {
      // Clear loading state for this item
      setRemovingItems(prev => ({ ...prev, [productId]: false }));
    }
  };

  // Calculate total price
  const calculateTotals = () => {
    const subtotal = cartProducts.reduce((sum, product) => {
      const normalizedProduct = normalizeProduct(product);
      const quantity = quantities[normalizedProduct.id] || 1;
      return sum + (normalizedProduct.price * quantity);
    }, 0);

    const shipping = 0; // Free shipping
    const total = subtotal + shipping;

    return { subtotal, shipping, total };
  };

  const { subtotal, shipping, total } = calculateTotals();

  // Handle checkout
  const handleCheckout = () => {
    navigate('/checkout');
  };

  // Loading state
  if (statusLoading || checkingAuth) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
            <div className="animate-pulse flex items-center">
              <div className="h-6 bg-gray-200 rounded px-4 py-1 text-gray-500">
                {checkingAuth ? "Loading your profile..." : "Loading your cart..."}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-4">
              {[1, 2, 3].map((i) => (
                <CartItemSkeleton key={i} />
              ))}
            </div>

            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-sm p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-6"></div>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                  </div>
                  <div className="flex justify-between">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                  </div>
                  <div className="pt-4 mt-4 border-t">
                    <div className="flex justify-between mb-6">
                      <div className="h-6 bg-gray-200 rounded w-16"></div>
                      <div className="h-6 bg-gray-200 rounded w-20"></div>
                    </div>
                    <div className="h-12 bg-gray-200 rounded w-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (statusError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 rounded-xl p-6 text-center">
            <FiAlertCircle className="mx-auto h-12 w-12 text-red-400" />
            <h2 className="mt-2 text-lg font-medium text-red-800">Error loading your cart</h2>
            <p className="mt-1 text-sm text-red-700">{statusError}</p>
            <div className="mt-6">
              <button
                onClick={refreshData}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
          <p className="text-gray-500">{cartProducts.length} {cartProducts.length === 1 ? 'item' : 'items'}</p>
        </div>

        {/* Cart Content */}
        {cartProducts.length === 0 ? (
          <EmptyCart />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {cartProducts.map((product) => {
                if (!product) return null;
                const productId = product.product_id || product.Product_id;
                if (!productId) return null;
                return (
                  <CartItem
                    key={productId}
                    product={product}
                    quantity={quantities[productId] || 1}
                    onQuantityChange={handleQuantityChange}
                    onRemove={handleRemoveItem}
                    isRemoving={removingItems[productId]}
                  />
                );
              })}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 sticky top-24">
                <h2 className="text-xl font-bold text-gray-900 mb-6">Order Summary</h2>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="text-gray-900 font-medium">₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Shipping</span>
                    <span className="text-green-600 font-medium">{shipping === 0 ? 'Free' : `₹${shipping.toFixed(2)}`}</span>
                  </div>
                  <div className="pt-4 mt-4 border-t border-gray-200">
                    <div className="flex justify-between mb-6">
                      <span className="text-lg font-bold text-gray-900">Total</span>
                      <span className="text-lg font-bold text-gray-900">₹{total.toFixed(2)}</span>
                    </div>
                    <button
                      onClick={handleCheckout}
                      className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiShoppingBag className="mr-2" />
                      Proceed to Checkout
                    </button>
                  </div>
                </div>

                {/* Secure Checkout Message */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-center text-sm text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    Secure Checkout
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <Footer />

      {/* Login Popup */}
      <Login
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        redirectUrl={location.pathname}
      />
    </div>
  );
};

export default cart;
