// components/Cards_dual.jsx

import React, { useState, useRef, useEffect, memo, useCallback } from "react";
import { Link } from "react-router-dom";
import { useSelector } from "react-redux";
import useProducts from "../../hooks/useProducts";
import useStatus from "../../hooks/useStatus";
import useToggle from "../../hooks/useToggle";
import { selectUserProfile } from "../../app/users/usersSelector";

// Skeleton loader component for product cards
const ProductCardSkeleton = () => (
  <div className="group relative">
    <div className="aspect-[4/5.6] w-full overflow-hidden rounded-lg bg-slate-200" />
    <div className="mt-4 flex justify-between">
      <div className="h-4 w-20 bg-slate-200 rounded" />
      <div className="mb-14 h-4 w-12 bg-slate-200 rounded" />
    </div>
  </div>
);

/**
 * ProductCardWithLoader component:
 * - Displays a skeleton until the product image is fully loaded.
 * - Once loaded, it renders product details and the like (heart) button.
 */
const ProductCardWithLoader = ({ product, isProductLiked, handleLikeClick }) => {
  const [imgLoaded, setImgLoaded] = useState(false);
  const productId = product.Product_id || product.product_id;
  const name = product.Name || product.name;
  const color = product.Color || product.color || "default";
  const price = product.Price || product.price;
  const brand = product.Brand || product.brand || "Brand";
  const imageUrl = product.Imageurl || product.imageurl;
  const discountPrice = product.OldPrice || product.oldPrice || product.discountPrice;

  const onImageLoad = () => setImgLoaded(true);

  if (!imgLoaded) {
    return (
      <div className="relative">
        <ProductCardSkeleton />
        {/* Preload the image invisibly */}
        <img
          src={imageUrl}
          alt={name}
          onLoad={onImageLoad}
          style={{ display: "none" }}
        />
      </div>
    );
  }

  return (
    <div className="group relative">
      <div className="aspect-4/5 w-full overflow-hidden rounded-lg bg-gray-100">
        <Link
          to={`/products/${productId}?color=${color}&price=${price}`}
          state={{ product }}
        >
          <img
            src={imageUrl}
            alt={name}
            loading="lazy"
            className="h-full w-full object-cover object-center"
          />
        </Link>
      </div>
      <div className="mt-2 sm:mt-4 flex flex-col sm:flex-row justify-between items-start">
        <div className="w-full sm:w-auto">
          <h3 className="text-xs sm:text-sm text-gray-700 font-medium truncate">
            {name}
          </h3>
          <p className="text-xs text-gray-500 truncate">{brand}</p>
        </div>
        <div className="text-right mt-1 sm:mt-0 w-full sm:w-auto">
          <p className="text-xs sm:text-sm font-semibold text-gray-900">₹{price}</p>
          {discountPrice && (
            <p className="text-xs text-gray-500 line-through">₹{discountPrice}</p>
          )}
        </div>
      </div>
      <button
        onClick={(e) => {
          e.preventDefault();
          handleLikeClick(product);
        }}
        className={`absolute top-2 sm:top-3 left-2 sm:left-3 p-1.5 sm:p-2 rounded-full bg-white/80 backdrop-blur-sm hover:bg-white ${
          isProductLiked(productId) ? "text-red-500" : "text-gray-500"
        }`}
      >
        <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 32 29.6">
          <path d="M23.6,0c-3.4,0-6.3,2.7-7.6,5.6C14.7,2.7,11.8,0,8.4,0C3.8,0,0,3.8,0,8.4c0,9.4,9.5,11.9,16,21.2
            c6.1-9.3,16-12.1,16-21.2C32,3.8,28.2,0,23.6,0z" />
        </svg>
      </button>
    </div>
  );
};

/**
 * Cards_dual component
 * - Manages fetching products data and user wishlist status.
 * - Implements infinite scrolling using IntersectionObserver.
 * - Uses skeleton loaders until both products and status are ready.
 */
const Cards_dual = memo(({ route, params = {} }) => {
  const {
    products: apiProducts = [],
    loading,
    loadMore,
    nextCursor,
    error: productsError,
    endReached
  } = useProducts(route, params);
  const products = Array.isArray(apiProducts) ? apiProducts : [];

  const { data: statusData, loading: statusLoading, error: statusError, refreshData } = useStatus();
  const { toggleWishlist, optimistic } = useToggle();
  const [localError, setLocalError] = useState(null);

  // Get user authentication state from Redux
  const user = useSelector(selectUserProfile);

  // Determine list of products that are liked
  const likedProducts =
    statusData?.products
      ?.filter((item) => item.wishlist === true)
      ?.map((item) => item.product_id.toString()) || [];

  /**
   * isProductLiked checks if the product (by ID) is marked as liked.
   * It considers optimistic updates as well as persistent state.
   */
  const isProductLiked = useCallback(
    (productId) => {
      if (!productId) return false;
      const idStr = productId.toString();
      return optimistic[idStr] ?? likedProducts.includes(idStr);
    },
    [optimistic, likedProducts]
  );

  /**
   * handleLikeClick handles the toggling of wishlist status.
   * It verifies user authentication and calls the toggle function.
   */
  const handleLikeClick = (product) => {
    if (!user) {
      setLocalError("You need to be logged in to like products.");
      return;
    }

    const Product_id = product.Product_id || product.product_id;
    const Name = product.Name || product.name;
    const Color = product.Color || product.color;
    const Price = product.Price || product.price;
    const Brand = product.Brand || product.brand;
    const Imageurl = product.Imageurl || product.imageurl;

    if (!Product_id) {
      setLocalError("Invalid product data");
      return;
    }

    toggleWishlist(Product_id, Name, Color, Price, Brand, Imageurl).catch((err) => {
      setLocalError(err.message || "Error toggling favorite");
    });

    setTimeout(refreshData, 100);
  };

  // Clear local error after 3 seconds.
  useEffect(() => {
    if (localError) {
      const timer = setTimeout(() => setLocalError(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [localError]);

  // Set up IntersectionObserver for smooth infinite scrolling
  const loaderRef = useRef(null);
  useEffect(() => {
    if (loading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        // Use requestAnimationFrame for smooth handling of the intersection callback
        if (entries[0].isIntersecting && !loading && nextCursor && !endReached) {
          window.requestAnimationFrame(() => loadMore());
        }
      },
      {
        threshold: 0.1,
        rootMargin: "150px"
      }
    );

    if (loaderRef.current) observer.observe(loaderRef.current);
    return () => {
      if (loaderRef.current) observer.unobserve(loaderRef.current);
    };
  }, [loadMore, nextCursor, loading, endReached]);

  // Show skeleton loaders if both products and status are still loading
  if ((loading || statusLoading) && products.length === 0) {
    return (
      <section className="px-2 max-w-7xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-6 lg:pl-4">
          {Array(8).fill(0).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </div>
      </section>
    );
  }

  // Show fallback UI if an error occurred with fetching products or status
  if ((productsError && !endReached) || statusError) {
    return (
      <section className="px-2 max-w-7xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-6 lg:pl-4">
          {Array(8).fill(0).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </div>
      </section>
    );
  }

  return (
    <section className="px-2 max-w-7xl mx-auto">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-6 lg:pl-4">
        {products.length > 0
          ? products.map((product) => (
              <ProductCardWithLoader
                key={product.Product_id || product.product_id}
                product={product}
                isProductLiked={isProductLiked}
                handleLikeClick={handleLikeClick}
              />
            ))
          : Array(8).fill(0).map((_, index) => <ProductCardSkeleton key={index} />)}
      </div>
      <div ref={loaderRef} className="py-8 text-center">
        {loading && products.length > 0 ? (
          <div className="flex justify-center items-center space-x-2">
            <div className="w-4 h-4 bg-gray-200 rounded-full animate-pulse" />
            <div className="w-4 h-4 bg-gray-300 rounded-full animate-pulse delay-150" />
            <div className="w-4 h-4 bg-gray-200 rounded-full animate-pulse delay-300" />
            <span className="text-sm text-gray-500">Loading more...</span>
          </div>
        ) : nextCursor && !endReached ? (
          <div className="text-sm text-gray-400">Scroll for more</div>
        ) : products.length > 0 ? (
          <div className="text-sm text-gray-500">No more products</div>
        ) : null}
      </div>
      {localError && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg transition-opacity duration-300">
          {localError}
        </div>
      )}
    </section>
  );
});

export default Cards_dual;
