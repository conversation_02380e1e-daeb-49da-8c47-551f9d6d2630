{"version": 3, "file": "isValidNumberForRegion.test.js", "names": ["metadata", "type", "isValidNumberForRegionCustom", "_isValidNumberForRegion", "isValidNumberForRegion", "parameters", "push", "apply", "describe", "it", "should", "equal", "expect", "phone", "country", "to"], "sources": ["../../source/legacy/isValidNumberForRegion.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport isValidNumberForRegionCustom from './isValidNumberForRegion.js'\r\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'\r\n\r\nfunction isValidNumberForRegion(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn isValidNumberForRegionCustom.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidNumberForRegion', () => {\r\n\tit('should detect if is valid number for region', () => {\r\n\t\tisValidNumberForRegion('07624369230', 'GB').should.equal(false)\r\n\t\tisValidNumberForRegion('07624369230', 'IM').should.equal(true)\r\n\t})\r\n\r\n\tit('should validate arguments', () => {\r\n\t\texpect(() => isValidNumberForRegion({ phone: '7624369230', country: 'GB' })).to.throw('number must be a string')\r\n\t\texpect(() => isValidNumberForRegion('7624369230')).to.throw('country must be a string')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Not a \"viable\" phone number.\r\n\t\tisValidNumberForRegion('7', 'GB').should.equal(false)\r\n\r\n\t\t// `options` argument `if/else` coverage.\r\n\t\t_isValidNumberForRegion('07624369230', 'GB', {}, metadata).should.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,4BAAP,MAAyC,6BAAzC;AACA,OAAOC,uBAAP,MAAoC,8BAApC;;AAEA,SAASC,sBAAT,GAA+C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC9CA,UAAU,CAACC,IAAX,CAAgBN,QAAhB;EACA,OAAOE,4BAA4B,CAACK,KAA7B,CAAmC,IAAnC,EAAyCF,UAAzC,CAAP;AACA;;AAEDG,QAAQ,CAAC,wBAAD,EAA2B,YAAM;EACxCC,EAAE,CAAC,6CAAD,EAAgD,YAAM;IACvDL,sBAAsB,CAAC,aAAD,EAAgB,IAAhB,CAAtB,CAA4CM,MAA5C,CAAmDC,KAAnD,CAAyD,KAAzD;IACAP,sBAAsB,CAAC,aAAD,EAAgB,IAAhB,CAAtB,CAA4CM,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCG,MAAM,CAAC;MAAA,OAAMR,sBAAsB,CAAC;QAAES,KAAK,EAAE,YAAT;QAAuBC,OAAO,EAAE;MAAhC,CAAD,CAA5B;IAAA,CAAD,CAAN,CAA6EC,EAA7E,UAAsF,yBAAtF;IACAH,MAAM,CAAC;MAAA,OAAMR,sBAAsB,CAAC,YAAD,CAA5B;IAAA,CAAD,CAAN,CAAmDW,EAAnD,UAA4D,0BAA5D;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACAL,sBAAsB,CAAC,GAAD,EAAM,IAAN,CAAtB,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,KAA/C,EAFqC,CAIrC;;IACAR,uBAAuB,CAAC,aAAD,EAAgB,IAAhB,EAAsB,EAAtB,EAA0BH,QAA1B,CAAvB,CAA2DU,MAA3D,CAAkEC,KAAlE,CAAwE,KAAxE;EACA,CANC,CAAF;AAOA,CAlBO,CAAR"}