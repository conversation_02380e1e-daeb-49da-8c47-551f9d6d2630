{"version": 3, "file": "IsHSL.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHSL.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,cAAc,MAAM,qBAAqB,CAAC;AAEjD,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;;;GAIG;AACH,MAAM,UAAU,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YAChD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+BAA+B,EAA5C,CAA4C,EAAE,iBAAiB,CAAC;SAC5G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHSLValidator from 'validator/lib/isHSL';\n\nexport const IS_HSL = 'isHSL';\n\n/**\n * Check if the string is an HSL (hue, saturation, lightness, optional alpha) color based on CSS Colors Level 4 specification.\n * Comma-separated format supported. Space-separated format supported with the exception of a few edge cases (ex: hsl(200grad+.1%62%/1)).\n * If given value is not a string, then it returns false.\n */\nexport function isHSL(value: unknown): boolean {\n  return typeof value === 'string' && isHSLValidator(value);\n}\n\n/**\n * Check if the string is an HSL (hue, saturation, lightness, optional alpha) color based on CSS Colors Level 4 specification.\n * Comma-separated format supported. Space-separated format supported with the exception of a few edge cases (ex: hsl(200grad+.1%62%/1)).\n * If given value is not a string, then it returns false.\n */\nexport function IsHSL(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HSL,\n      validator: {\n        validate: (value, args): boolean => isHSL(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a HSL color', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}