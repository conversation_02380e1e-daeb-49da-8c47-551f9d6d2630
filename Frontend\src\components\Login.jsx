// Login.jsx

import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { FiMail, FiUser, FiPhone, FiArrowLeft } from "react-icons/fi";
import { loadGoogleLogin, loadFacebookLogin } from "../hooks/useOAuth";
import { useAuthenticate, useValidateToken, useGoogleAuth, useFacebookAuth } from "../hooks/useAuth";
import { useOTP } from "../hooks/useOTP";
import Turnstile from "react-turnstile";
import { cacheToken, getCachedToken, clearTokenCache, clearCachedToken } from "../utils/turnstileCache";
import { validateEmail, validatePhone, validatePassword, validateName, validateOTP } from "../utils/formValidation";

const Login = ({ isOpen, onClose, redirectUrl = "/" }) => {
  // 'loginMethod' controls the current UI flow: "phone", "otp", or "email"
  const [loginMethod, setLoginMethod] = useState("phone");
  // Track the source of OTP (phone or email)
  const [otpSource, setOtpSource] = useState("phone");
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [username, setUsername] = useState("");
  // Separate token states for each Turnstile instance
  const [phoneTurnstileToken, setPhoneTurnstileToken] = useState("");
  const [passwordTurnstileToken, setPasswordTurnstileToken] = useState("");
  // Load Turnstile site key from environment variables
  const [turnstileKey] = useState(import.meta.env.VITE_TURNSTILE_SITE_KEY || "");
  const [currentAction, setCurrentAction] = useState("phone_login"); // Track current action for token caching
  // Store Turnstile widget IDs for each step
  const [phoneWidgetId, setPhoneWidgetId] = useState(null);
  const [passwordWidgetId, setPasswordWidgetId] = useState(null);

  // Track loading state of Turnstile widgets
  const [phoneTurnstileLoading, setPhoneTurnstileLoading] = useState(true);
  const [passwordTurnstileLoading, setPasswordTurnstileLoading] = useState(true);
  // Track if Turnstile script is loaded
  const [turnstileScriptLoaded, setTurnstileScriptLoaded] = useState(false);
  // isNewUser is set to true when we detect a new user during password verification

  // Track the current step in the phone login flow
  // "input" -> "verify" (with Turnstile)
  const [phoneStep, setPhoneStep] = useState("input");

  // Track the current step in the email login flow
  // "email" -> "password" -> "name" -> "verify" (if new user)
  const [emailStep, setEmailStep] = useState("email");

  // States for displaying error/success messages
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [loading, setLoading] = useState(false);

  // Field-specific validation errors
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [nameError, setNameError] = useState("");
  const [otpError, setOtpError] = useState("");

  const navigate = useNavigate();

  // Use the hooks
  const validateTokenFn = useValidateToken();
  const authenticate = useAuthenticate();
  const googleAuth = useGoogleAuth();
  const facebookAuth = useFacebookAuth();
  const { sendOTP, verifyOTP } = useOTP();

  // Track if social logins have been initialized
  const [socialLoginsInitialized, setSocialLoginsInitialized] = useState(false);

  // Track if the component is mounted
  const isMountedRef = useRef(true);

  // Track if token validation has been performed
  const [tokenValidated, setTokenValidated] = useState(false);

  useEffect(() => {
    // Set isMounted to true when component mounts
    isMountedRef.current = true;

    // Clear the logout flag when the login component mounts
    // This ensures that auto-login can work again after a user has logged out
    sessionStorage.removeItem('user_logged_out');

    // Cleanup function to set isMounted to false when component unmounts
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // First useEffect for token validation
  useEffect(() => {
    // Skip token validation if already performed or component is not open
    if (tokenValidated || !isOpen) return;

    // Validate token if available
    const checkToken = async () => {
      // Since we're using HTTP-only cookies, we can't access the token directly
      // Instead, we'll try to validate by making an API call that requires authentication
      try {
        const isValid = await validateTokenFn(null, // No token needed since it's in HTTP-only cookies
            // onSuccess callback
            () => {
              // Token is valid, user is already logged in
              console.log("Token is valid, user is already logged in");

              // Only proceed if component is still mounted
              if (isMountedRef.current) {
                // Close the login popup and redirect if needed
                onClose();

                // If redirectUrl is provided and not the current page, navigate to it
                if (redirectUrl && redirectUrl !== "/" && window.location.pathname !== redirectUrl) {
                  navigate(redirectUrl);
                }
              }
            },
            // onError callback
            (error) => {
              console.error("Error validating token:", error);
              if (isMountedRef.current) {
                Cookies.remove("token");
              }
            }
          );

          // Mark token validation as completed
          if (isMountedRef.current) {
            setTokenValidated(true);

            if (!isValid) {
              Cookies.remove("token");
            }
          }
        } catch (err) {
          console.error("Error validating token:", err);
          if (isMountedRef.current) {
            Cookies.remove("token");
            setTokenValidated(true);
          }
        }
      } else {
        // No token, mark validation as completed
        if (isMountedRef.current) {
          setTokenValidated(true);
        }
      }
    };

    checkToken();
  }, [isOpen, tokenValidated, validateTokenFn, navigate, redirectUrl, onClose]);

  // Separate useEffect for social login initialization
  useEffect(() => {
    // Load social login flows only when the popup is open
    if (isOpen && !socialLoginsInitialized) {
      console.log("Initializing social logins...");
      // Small delay to ensure the popup is fully rendered before loading social buttons
      const timeoutId = setTimeout(() => {
        // Load Google login with popup flow
        loadGoogleLogin((response) => {
          if (response && response.credential) {
            console.log("Google credential received");

            // Show loading state
            setLoading(true);

            // Call the authentication function
            googleAuth(response.credential,
              (error) => {
                setLoading(false);
                // Only set error if there's an actual error message
                if (error) {
                  console.error("Google authentication error:", error);
                  setError("Google sign-in failed. Please try again.");
                }
              },
              (message) => {
                setLoading(false);
                console.log("Google auth success:", message);
                setSuccess(message || "Google sign-in successful!");
              },
              redirectUrl
            );
          } else {
            console.log("Google login failed or was cancelled");
            // Don't show error for user cancellation
          }
        });

        // Load Facebook login with improved error handling
        loadFacebookLogin((response) => {
          try {
            // Handle successful login
            if (response && response.authResponse) {
              const { accessToken, userID } = response.authResponse;
              console.log("Facebook auth response received", { userID });

              // Show loading state
              setLoading(true);

              // Call the authentication function
              facebookAuth(accessToken, userID,
                (error) => {
                  setLoading(false);
                  // Only set error if there's an actual error message
                  if (error) {
                    console.error("Facebook authentication error:", error);
                    setError("Facebook sign-in failed. Please try again.");
                  }
                },
                (message) => {
                  setLoading(false);
                  console.log("Facebook auth success:", message);
                  setSuccess(message || "Facebook sign-in successful!");
                },
                redirectUrl
              );
            }
            // Handle development mock response
            else if (response && response.status === 'connected' && response.authResponse) {
              const { accessToken, userID } = response.authResponse;
              console.log("Facebook mock auth response received", { userID });

              // Show loading state
              setLoading(true);

              // Call the authentication function with mock data
              facebookAuth(accessToken, userID,
                (error) => {
                  setLoading(false);
                  // Only set error if there's an actual error message
                  if (error) {
                    console.error("Facebook authentication error:", error);
                    setError("Facebook sign-in failed. Please try again.");
                  }
                },
                (message) => {
                  setLoading(false);
                  console.log("Facebook auth success:", message);
                  setSuccess(message || "Facebook sign-in successful!");
                },
                redirectUrl
              );
            }
            // Handle explicit error response
            else if (response && response.status === 'error') {
              console.error("Facebook login error:", response.error);
              setError("Facebook login failed: " + response.error);
            }
            // Handle cancellation or other failures
            else {
              console.log("Facebook login cancelled or failed", response);
              // Don't show error for user cancellation
            }
          } catch (err) {
            console.error("Unexpected error in Facebook login handler:", err);
            setError("An unexpected error occurred. Please try again later.");
          }
        });

        // Mark social logins as initialized
        setSocialLoginsInitialized(true);
        console.log("Social logins initialized");
      }, 300); // Delay loading social buttons to prevent flickering

      // Clean up timeout if component unmounts
      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, socialLoginsInitialized, googleAuth, facebookAuth, setSuccess, setError, setLoading, redirectUrl]);

  // Validate Turnstile key
  useEffect(() => {
    if (!turnstileKey && process.env.NODE_ENV === 'production') {
      console.error('SECURITY ERROR: VITE_TURNSTILE_SITE_KEY is not set in environment variables');
    }
  }, [turnstileKey]);

  // Preload Turnstile script when component mounts
  useEffect(() => {
    // Only load if not already loaded and we have a valid key
    if (!turnstileScriptLoaded && turnstileKey) {
      // Check if script is already loaded
      if (document.querySelector('script[src*="turnstile"]')) {
        setTurnstileScriptLoaded(true);
      } else {
        // Create a preload link
        const preloadLink = document.createElement('link');
        preloadLink.rel = 'preload';
        preloadLink.as = 'script';
        preloadLink.href = 'https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit';
        document.head.appendChild(preloadLink);

        // Load the script immediately
        const script = document.createElement('script');
        script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit';
        script.async = true;
        script.defer = true;
        script.onload = () => {
          setTurnstileScriptLoaded(true);
          console.log("Turnstile script loaded");
        };
        document.head.appendChild(script);
      }
    }
  }, [turnstileScriptLoaded]); // Only run once on mount

  const handlePhoneInput = () => {
    // Validate phone number
    const validation = validatePhone(phone);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }

    // Batch state updates to prevent flickering
    // Clear any previous errors and set the step in one update
    setError("");
    setSuccess("");
    setCurrentAction("phone_login");
    setPhoneStep("verify");
  };

  const handlePhoneContinue = async () => {
    // Check if we have a cached token or need a fresh verification
    let token = phoneTurnstileToken;
    if (!token) {
      // Try to get a cached token
      token = getCachedToken("phone_login");
      if (token) {
        setPhoneTurnstileToken(token);
      } else {
        setError("Please complete the security check");
        setSuccess(""); // Clear any success message
        return;
      }
    }

    try {
      // Clear any previous error messages
      setError("");
      // Show sending state
      setSuccess("Sending OTP...");

      // Create custom success and error handlers to prevent multiple messages
      const customSetSuccess = (message) => {
        // Only set success message if we're still in the process
        if (success === "Sending OTP...") {
          setSuccess(message);
        }
      };

      const customSetError = (message) => {
        // Clear any success message when an error occurs
        setSuccess("");
        setError(message);
      };

      // Send OTP to the phone number with Turnstile token and action
      const result = await sendOTP(phone, customSetError, customSetSuccess, token, currentAction);

      // Only proceed if OTP was sent successfully
      if (result) {
        // Set OTP source to phone
        setOtpSource("phone");
        // Switch to OTP verification view
        setLoginMethod("otp");
      }
    } catch (error) {
      // Clear any success message
      setSuccess("");
      setError(error.message || "Failed to send OTP. Please try again.");

      // If there's an error, it might be due to an invalid token
      // Reset the token to force a new verification
      setPhoneTurnstileToken("");
      clearCachedToken("phone_login");
      // Reset loading state
      setPhoneTurnstileLoading(true);
      // Only try to reset the Turnstile widget if it exists
      try {
        if (phoneWidgetId && window.turnstile) {
          window.turnstile.reset(phoneWidgetId);
        }
      } catch (error) {
        console.warn("Failed to reset Turnstile widget:", error);
        // Continue without resetting - the widget will be reinitialized
      }
    }
  };

  // Handle Turnstile verification
  const handleTurnstileVerify = (token) => {
    if (!token) {
      console.error("Turnstile verification failed: No token received");
      return;
    }

    console.log("Turnstile verification successful");

    // Use a callback to ensure state updates are batched
    // Set the appropriate token state based on the current action
    if (currentAction === "phone_login") {
      setPhoneTurnstileToken(token);
    } else if (currentAction === "password_login") {
      setPasswordTurnstileToken(token);
    } else {
      console.warn("Unknown action type for Turnstile verification:", currentAction);
    }

    setError(""); // Clear any previous errors
    setSuccess(""); // Clear any previous success messages

    // Cache the token for the current action
    // This allows us to reuse the token for a short period
    cacheToken(currentAction, token);
  };

  // Handle Turnstile loading error
  const handleTurnstileLoadError = () => {
    console.error("Turnstile failed to load");
    // Show an error message
    setError("Security verification failed to load. Please refresh the page and try again.");

    // Set loading state to false for all Turnstile widgets
    setPhoneTurnstileLoading(false);
    setPasswordTurnstileLoading(false);

    // Only use fallback in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Using fallback token in development');
      // Set the appropriate token state based on the current action
      if (currentAction === "phone_login") {
        setPhoneTurnstileToken("fallback-token-for-development");
      } else if (currentAction === "password_login") {
        setPasswordTurnstileToken("fallback-token-for-development");
      }
    }
  };

  // Handle Turnstile expiration
  const handleTurnstileExpire = () => {
    console.log("Turnstile token expired for action:", currentAction);

    // Reset the appropriate token state based on the current action
    if (currentAction === "phone_login") {
      setPhoneTurnstileToken("");
      // Clear the cached token
      clearCachedToken("phone_login");
    } else if (currentAction === "password_login") {
      setPasswordTurnstileToken("");
      // Clear the cached token
      clearCachedToken("password_login");
    }

    // Optionally show a message to the user
    setError("Security verification expired. Please complete the verification again.");
  };

  // Handle Turnstile error
  const handleTurnstileError = (errorCode) => {
    console.log("Turnstile error:", errorCode);

    // Don't show error for "nothing-to-reset" as it's an internal error
    if (errorCode === "nothing-to-reset") {
      console.warn("Turnstile reset called on non-existent widget");
      return; // Don't show error to user for this case
    }

    let errorMessage = "Security verification failed. ";

    // Provide more specific error messages based on error code
    switch(errorCode) {
      case "missing-input-secret":
        errorMessage += "The secret parameter is missing.";
        break;
      case "invalid-input-secret":
        errorMessage += "The secret parameter is invalid or malformed.";
        break;
      case "missing-input-response":
        errorMessage += "The response parameter is missing.";
        break;
      case "invalid-input-response":
        errorMessage += "The response parameter is invalid or malformed.";
        break;
      case "bad-request":
        errorMessage += "The request is invalid or malformed.";
        break;
      case "timeout-or-duplicate":
        errorMessage += "The response is no longer valid: either is too old or has been used previously.";
        break;
      case "internal-error":
        errorMessage += "An internal error occurred while validating the response.";
        break;
      case "network-error":
        errorMessage += "Network error. Please check your connection and try again.";
        break;
      case "expired-challenge":
        errorMessage += "The verification has expired. Please try again.";
        break;
      case "invalid-container":
        errorMessage += "There was a problem with the verification container. Please refresh the page.";
        break;
      default:
        errorMessage += "Please try again.";
    }

    setError(errorMessage);

    // Reset the appropriate token state based on the current action
    if (currentAction === "phone_login") {
      setPhoneTurnstileToken("");
      clearCachedToken("phone_login");
      // Reset loading state to trigger re-render
      setPhoneTurnstileLoading(true);
    } else if (currentAction === "password_login") {
      setPasswordTurnstileToken("");
      clearCachedToken("password_login");
      // Reset loading state to trigger re-render
      setPasswordTurnstileLoading(true);
    }
  };

  const handleVerifyOtp = async () => {
    // Validate OTP
    const validation = validateOTP(otp);
    if (!validation.isValid) {
      setError(validation.error);
      setSuccess(""); // Clear any success message
      return;
    }

    try {
      // Clear any previous error messages
      setError("");
      // Show verifying state
      setSuccess("Verifying OTP...");

      // Determine which recipient to use based on the OTP source
      const recipient = otpSource === 'email' ? email : phone;

      // Create custom success and error handlers to prevent multiple messages
      const customSetSuccess = (message) => {
        // Only set success message if we're still in the process
        if (success === "Verifying OTP...") {
          setSuccess(message);
        }
      };

      const customSetError = (message) => {
        setSuccess("");
        setError(message);
      };

      // Verify the OTP - user will be automatically registered if they don't exist
      await verifyOTP(recipient, otp, customSetError, customSetSuccess, redirectUrl);
    } catch (error) {
      setSuccess("");
      setError(error.message || "Failed to verify OTP. Please try again.");
    }
  };

  const handleEmailContinue = () => {
    const validation = validateEmail(email);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }

    setError("");
    setSuccess("");

    // Set the action for the password step (where we'll need Turnstile)
    setCurrentAction("password_login");

    // Move to password step
    setEmailStep("password");

    // Reset Turnstile token to ensure fresh verification at password step
    setPasswordTurnstileToken("");
    // Reset loading state
    setPasswordTurnstileLoading(true);

    // We'll reset the Turnstile widget after it's loaded in the password step
    // Don't try to reset it here as it might not exist yet
  };

  // Handle email OTP request
  const handleEmailOTP = async () => {
    // Validate email
    const validation = validateEmail(email);
    if (!validation.isValid) {
      setError(validation.error);
      setSuccess(""); // Clear any success message
      return;
    }

    // Check if we have a cached token or need a fresh verification
    let token = passwordTurnstileToken;
    if (!token) {
      // Try to get a cached token
      token = getCachedToken("password_login");
      if (token) {
        setPasswordTurnstileToken(token);
      } else {
        setError("Please complete the security check");
        setSuccess("");
        return;
      }
    }

    try {
      // Clear any previous error messages
      setError("");
      // Show sending state
      setSuccess("Sending OTP...");

      // Set the action for email OTP
      setCurrentAction("email_login");

      // Create custom success and error handlers to prevent multiple messages
      const customSetSuccess = (message) => {
        // Only set success message if we're still in the process
        if (success === "Sending OTP...") {
          setSuccess(message);
        }
      };

      const customSetError = (message) => {
        // Clear any success message when an error occurs
        setSuccess("");
        setError(message);
      };

      // Send OTP to the email with Turnstile token
      const result = await sendOTP(email, customSetError, customSetSuccess, token, "email_login");

      // Only proceed if OTP was sent successfully
      if (result) {
        // Set OTP source to email
        setOtpSource("email");

        // Switch to OTP verification view
        setLoginMethod("otp");
      }
    } catch (error) {
      // Clear any success message
      setSuccess("");
      setError(error.message || "Failed to send email OTP. Please try again.");

      // If there's an error, it might be due to an invalid token
      // Reset the token to force a new verification
      setPasswordTurnstileToken("");
      clearCachedToken("password_login");
      // Reset loading state
      setPasswordTurnstileLoading(true);
      // Only try to reset the Turnstile widget if it exists
      try {
        if (passwordWidgetId && window.turnstile) {
          window.turnstile.reset(passwordWidgetId);
        }
      } catch (error) {
        console.warn("Failed to reset Turnstile widget:", error);
        // Continue without resetting - the widget will be reinitialized
      }
    }
  };

  // Note: Hooks are now defined at the top level of the component

  // Handle password step - validate password and check if user exists
  const handlePasswordContinue = async () => {
    // Validate password
    const validation = validatePassword(password);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }

    // Set the current action
    setCurrentAction("password_login");

    // Check if we have a cached token or need a fresh verification
    let token = passwordTurnstileToken;
    if (!token) {
      // Try to get a cached token
      token = getCachedToken("password_login");
      if (token) {
        setPasswordTurnstileToken(token);
      } else {
        setError("Please complete the security check");
        return;
      }
    }

    // Use the authenticate function from the hook
    try {
      // Show authenticating state
      setSuccess("Authenticating...");

      await authenticate(
        email,
        password,
        "", // No username at this step
        setError,
        setSuccess,
        () => {
          // Simply move to the name step for new users
          setEmailStep("name");
        }, // Custom function to set email step when user is new
        redirectUrl
      );
    } catch (error) {
      setError(error.message || "An error occurred. Please try again.");
      setSuccess("");

      // If there's an error, it might be due to an invalid token
      // Reset the token to force a new verification
      setPasswordTurnstileToken("");
      // Reset loading state
      setPasswordTurnstileLoading(true);
      // Only try to reset the Turnstile widget if it exists
      try {
        if (passwordWidgetId && window.turnstile) {
          window.turnstile.reset(passwordWidgetId);
        }
      } catch (error) {
        console.warn("Failed to reset Turnstile widget:", error);
        // Continue without resetting - the widget will be reinitialized
      }
    }
  };

  // Name step for new users - validate name and proceed to OTP verification
  const handleNameContinue = async () => {
    // Validate name
    const validation = validateName(username);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }

    try {
      // Show sending OTP state
      setSuccess("Sending OTP...");

      // Create custom success and error handlers to prevent multiple messages
      const customSetSuccess = (message) => {
        // Only set success message if we're still in the process
        if (success === "Sending OTP...") {
          setSuccess(message);
        }
      };

      const customSetError = (message) => {
        // Clear any success message when an error occurs
        setSuccess("");
        setError(message);
      };

      // Send OTP to the email for verification
      const result = await sendOTP(email, customSetError, customSetSuccess, passwordTurnstileToken, "register_user");

      // Only proceed if OTP was sent successfully
      if (result) {
        // Set OTP source to email
        setOtpSource("email");

        // Move to verify step
        setEmailStep("verify");
      }
    } catch (error) {
      setError(error.message || "Failed to send verification OTP. Please try again.");
      setSuccess("");
    }
  };

  // Final step for new users - verify OTP and create account
  const handleRegisterWithOTP = async () => {
    // Validate OTP
    const validation = validateOTP(otp);
    if (!validation.isValid) {
      setError(validation.error);
      setSuccess(""); // Clear any success message
      return;
    }

    try {
      // Show creating account state
      setSuccess("Creating account...");

      // First verify the OTP
      const otpVerified = await verifyOTP(email, otp,
        (errorMsg) => {
          setError(errorMsg);
          setSuccess("");
        },
        () => {
          // OTP verification successful, now create the account
        }
      );

      if (otpVerified) {
        // Now authenticate/register the user
        await authenticate(
          email,
          password,
          username,
          setError,
          setSuccess,
          () => {}, // Not needed here as we already know it's a new user
          redirectUrl
        );
      }
    } catch (error) {
      setError(error.message || "An error occurred during registration.");
      setSuccess("");
    }
  };

  // Note: We're using separate functions for each step instead of a combined function

  // Reset state when popup is closed
  const handleClose = () => {
    // Batch state updates to prevent flickering
    setTimeout(() => {
      setUsername("");
      setEmail("");
      setPassword("");
      setPhone(""); // Clear phone number when closing popup
      setOtp(""); // Clear OTP when closing popup
      setEmailStep("email");
      setPhoneStep("input");
      setError("");
      setSuccess("");
      // Clear all field-specific validation errors
      setEmailError("");
      setPasswordError("");
      setPhoneError("");
      setNameError("");
      setOtpError("");
      // Reset all Turnstile tokens
      setPhoneTurnstileToken("");
      setPasswordTurnstileToken("");

      // Reset all Turnstile loading states
      setPhoneTurnstileLoading(true);
      setPasswordTurnstileLoading(true);

      // Don't reset social logins initialization state
      // This prevents the Google button from reloading when switching methods

      // Clear all cached tokens when closing the popup
      // This ensures a fresh verification next time
      clearTokenCache();
    }, 100); // Small delay to ensure animation completes first

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center sm:items-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 fade-in"
        onClick={handleClose}
        style={{ animationDuration: '0.3s' }}
      ></div>

      {/* Popup Card */}
      <div className="relative w-full max-w-sm mx-auto bg-white rounded-lg shadow-xl p-6 pt-8 space-y-6 animate-slideInUp">
        {/* Navigation Header - Contains Back and Close buttons */}
        <div className="flex justify-between items-center mb-6 pt-2">
          {/* Back Button - Shown in password and name steps for email, and verify step for phone */}
          <div className="w-6">
            {(loginMethod === "email" && (emailStep === "password" || emailStep === "name" || emailStep === "verify")) ||
             (loginMethod === "phone" && phoneStep === "verify") ||
             (loginMethod === "otp") ? (
              <button
                onClick={() => {
                  if (loginMethod === "email") {
                    if (emailStep === "password") {
                      setEmailStep("email");
                    } else if (emailStep === "name") {
                      setEmailStep("password");
                    } else if (emailStep === "verify") {
                      setEmailStep("name");
                      // Clear OTP
                      setOtp("");
                      setOtpError("");
                    }
                  } else if (loginMethod === "phone" && phoneStep === "verify") {
                    setPhoneStep("input");
                  } else if (loginMethod === "otp") {
                    // Go back to the appropriate screen based on OTP source
                    if (otpSource === "email") {
                      setLoginMethod("email");
                      setEmailStep("password");
                    } else {
                      setLoginMethod("phone");
                      setPhoneStep("verify");
                    }
                    // Clear OTP
                    setOtp("");
                    setOtpError("");
                  }
                  setError("");
                  setSuccess("");
                }}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                aria-label="Back"
              >
                <FiArrowLeft className="h-7 w-7" />
              </button>
            ) : null}
          </div>

          {/* Close Button */}
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none ml-auto"
            aria-label="Close"
          >
            <svg
              className="h-7 w-7"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Header */}
        <div className="text-center">
          {loginMethod === "email" ? (
            <>
              {emailStep === "email" && (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Welcome Back</h2>
                  <p className="text-sm text-gray-500 mt-1">signup or login</p>
                </>
              )}
              {emailStep === "password" && (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Enter Password</h2>
                  <p className="text-sm text-gray-500 mt-1">Please enter your password</p>
                </>
              )}
              {emailStep === "name" && (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Create Account</h2>
                  <p className="text-sm text-gray-500 mt-1">Complete your registration</p>
                </>
              )}
              {emailStep === "verify" && (
                <>
                  <h2 className="text-2xl font-bold text-gray-800">Verify Email</h2>
                  <p className="text-sm text-gray-500 mt-1">Enter the verification code sent to your email</p>
                </>
              )}
            </>
          ) : loginMethod === "otp" ? (
            <>
              <h2 className="text-2xl font-bold text-gray-800">Verify OTP</h2>
              <p className="text-sm text-gray-500 mt-1">Enter the OTP sent to your {otpSource === "email" ? "email" : "phone"}</p>
            </>
          ) : (
            <>
              <h2 className="text-2xl font-bold text-gray-800">Welcome Back</h2>
              <p className="text-sm text-gray-500 mt-1">signup or login</p>
            </>
          )}
        </div>

        {/* Display any error or success messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm text-center">
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm text-center">
            {success}
          </div>
        )}

        {/* Dynamic Input Section */}
        {loginMethod === "phone" && (
          <div className="space-y-4">
            <label className="block text-sm font-medium text-gray-600">Phone Number</label>
            <div className="flex">
              <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm">
                +91
              </span>
              <input type="tel" value={phone}
                onChange={(e) => { setPhone(e.target.value); setError("");
                  if (e.target.value.trim()) {
                    const validation = validatePhone(e.target.value);
                    setPhoneError(validation.isValid ? "" : validation.error);
                  } else {
                    setPhoneError("");
                  }
                }}
                onKeyDown={(e) => e.key === 'Enter' && phoneStep === "input" && handlePhoneInput()}
                placeholder="Enter phone number"
                className={`flex-1 block w-full rounded-r-lg border ${phoneError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
              />
            </div>
            {phoneError && (
              <p className="text-red-500 text-xs mt-1">{phoneError}</p>
            )}

            {phoneStep === "input" ? (
              <button
                onClick={handlePhoneInput}
                disabled={phoneError !== ""}
                className={`w-full py-2 rounded-lg transition-colors relative ${
                  phoneError !== ""
                    ? "bg-blue-300 text-white cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                Continue
              </button>
            ) : (
              <>
                {/* Cloudflare Turnstile Widget - Managed Mode - Only shown in verify step */}
                <div className="turnstile-container w-full" style={{ height: '70px' }}>
                  <Turnstile
                    sitekey={turnstileKey}
                    onVerify={handleTurnstileVerify}
                    onExpire={handleTurnstileExpire}
                    onError={handleTurnstileError}
                    onLoad={(widgetId) => {
                      console.log("Turnstile loaded successfully");
                      setPhoneWidgetId(widgetId);
                      setPhoneTurnstileLoading(false);
                      setPhoneTurnstileToken("");
                    }}
                    onLoadError={handleTurnstileLoadError}
                    action="phone_login"
                    theme="light"
                    size="normal"
                    refreshExpired="auto"
                    className="custom-turnstile"
                    idempotency={phone} // Add idempotency key based on phone to ensure unique instance
                  />
                </div>

                <button
                  onClick={handlePhoneContinue}
                  className={`w-full py-2 rounded-lg transition-colors relative mt-4 ${
                    !phoneTurnstileToken || phoneTurnstileLoading
                      ? "bg-blue-300 text-white cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                  disabled={!phoneTurnstileToken || phoneTurnstileLoading}
                >
                  {success === "Sending OTP..." ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending OTP...
                    </div>
                  ) : (
                    "Continue"
                  )}
                </button>


              </>
            )}
          </div>
        )}

        {loginMethod === "otp" && (
          <div className="space-y-4">
            <div className="bg-blue-50 text-blue-600 p-3 rounded-lg text-sm mb-4">
              <div className="flex items-center">
                {otpSource === "email" ? (
                  <>
                    <FiMail className="mr-2 flex-shrink-0" />
                    <div className="flex flex-col">
                      <span>OTP has been sent to your email:</span>
                      <strong className="mt-1">{email}</strong>
                      <span className="text-xs mt-1">Please check your inbox</span>
                    </div>
                  </>
                ) : (
                  <>
                    <FiPhone className="mr-2 flex-shrink-0" />
                    <div className="flex flex-col">
                      <span>OTP has been sent to your phone:</span>
                      <strong className="mt-1">+91 {phone}</strong>
                      <span className="text-xs mt-1">Please check your SMS messages</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            <label className="block text-sm font-medium text-gray-600">Enter OTP</label>
            <input
              type="text"
              value={otp}
              onChange={(e) => {
                setOtp(e.target.value);
                // Clear main error when typing
                setError("");
                // Validate on change
                if (e.target.value.trim()) {
                  const validation = validateOTP(e.target.value);
                  setOtpError(validation.isValid ? "" : validation.error);
                } else {
                  setOtpError("");
                }
              }}
              placeholder="OTP"
              className={`w-full rounded-lg border ${otpError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
            />
            {otpError && (
              <p className="text-red-500 text-xs mt-1">{otpError}</p>
            )}
            <button
              onClick={handleVerifyOtp}
              disabled={otpError !== "" || success === "Verifying OTP..."}
              className={`w-full py-2 rounded-lg transition-colors ${
                otpError !== "" || success === "Verifying OTP..."
                  ? "bg-blue-300 text-white cursor-not-allowed"
                  : "bg-blue-600 text-white hover:bg-blue-700"
              }`}
            >
              {success === "Verifying OTP..." ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </div>
              ) : (
                "Verify OTP"
              )}
            </button>

            <div className="text-center mt-2">
              <button
                onClick={() => {
                  // Go back to the appropriate screen based on OTP source
                  if (otpSource === "email") {
                    setLoginMethod("email");
                    setEmailStep("password");
                  } else {
                    setLoginMethod("phone");
                    setPhoneStep("verify");
                  }
                  // Clear OTP
                  setOtp("");
                  setOtpError("");
                  setError("");
                  setSuccess("");
                }}
                className="text-blue-600 text-sm hover:underline"
              >
                Didn't receive OTP? Try again
              </button>
            </div>
          </div>
        )}

        {loginMethod === "email" && (
          <div className="space-y-4">
            {/* Email Step */}
            {emailStep === "email" && (
              <>
                <label className="block text-sm font-medium text-gray-600">Email Address</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    // Clear main error when typing
                    setError("");
                    // Validate on change
                    if (e.target.value.trim()) {
                      const validation = validateEmail(e.target.value);
                      setEmailError(validation.isValid ? "" : validation.error);
                    } else {
                      setEmailError("");
                    }
                  }}
                  onKeyDown={(e) => e.key === 'Enter' && handleEmailContinue()}
                  placeholder="Enter email"
                  className={`w-full rounded-lg border ${emailError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
                  autoFocus
                />
                {emailError && (
                  <p className="text-red-500 text-xs mt-1">{emailError}</p>
                )}
                <button
                  onClick={handleEmailContinue}
                  disabled={emailError !== ""}
                  className={`w-full py-2 rounded-lg transition-colors ${
                    emailError !== ""
                      ? "bg-blue-300 text-white cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                >
                  Continue
                </button>
              </>
            )}

            {/* Password Step */}
            {emailStep === "password" && (
              <>
                <label className="block text-sm font-medium text-gray-600">Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    // Clear main error when typing
                    setError("");
                    // Validate on change
                    if (e.target.value.trim()) {
                      const validation = validatePassword(e.target.value);
                      setPasswordError(validation.isValid ? "" : validation.error);
                    } else {
                      setPasswordError("");
                    }
                  }}
                  onKeyDown={(e) => e.key === 'Enter' && handlePasswordContinue()}
                  placeholder="Enter password"
                  className={`w-full rounded-lg border ${passwordError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
                  autoFocus
                />
                {passwordError && (
                  <p className="text-red-500 text-xs mt-1">{passwordError}</p>
                )}
                {/* Cloudflare Turnstile Widget for Password Step */}
                <div className="turnstile-container w-full relative" style={{ height: '70px' }}>
                  {passwordTurnstileLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-80 z-10">
                      <div className="flex items-center">
                        <svg className="animate-spin h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-sm text-blue-600">Loading verification...</span>
                      </div>
                    </div>
                  )}
                  <Turnstile
                    sitekey={turnstileKey}
                    onVerify={handleTurnstileVerify}
                    onExpire={handleTurnstileExpire}
                    onError={handleTurnstileError}
                    onLoad={(widgetId) => {
                      console.log("Turnstile loaded successfully");
                      // Store the widget ID for future reference
                      setPasswordWidgetId(widgetId);
                      // Mark as loaded
                      setPasswordTurnstileLoading(false);
                      // Clear any previous token to ensure fresh verification
                      setPasswordTurnstileToken("");
                    }}
                    onLoadError={handleTurnstileLoadError}
                    action="password_login"
                    theme="light"
                    size="normal"
                    refreshExpired="auto"
                    className="custom-turnstile"
                    idempotency={email} // Add idempotency key based on email to ensure unique instance
                  />
                </div>

                <button
                  onClick={handlePasswordContinue}
                  className={`w-full py-2 rounded-lg transition-colors ${
                    !passwordTurnstileToken || passwordTurnstileLoading || passwordError !== ""
                      ? "bg-blue-300 text-white cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                  disabled={!passwordTurnstileToken || passwordTurnstileLoading || passwordError !== ""}
                >
                  {success === "Authenticating..." ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Authenticating...
                    </div>
                  ) : (
                    "Continue"
                  )}
                </button>
              </>
            )}

            {/* Name Step (for new users) */}
            {emailStep === "name" && (
              <>
                <div className="bg-blue-50 text-blue-600 p-3 rounded-lg text-sm mb-4">
                  <div className="flex items-center">
                    <FiUser className="mr-2 flex-shrink-0" />
                    <span>Looks like you're new here!</span>
                  </div>
                </div>

                <label className="block text-sm font-medium text-gray-600">Your Name</label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => {
                    setUsername(e.target.value);
                    // Clear main error when typing
                    setError("");
                    // Validate on change
                    if (e.target.value.trim()) {
                      const validation = validateName(e.target.value);
                      setNameError(validation.isValid ? "" : validation.error);
                    } else {
                      setNameError("");
                    }
                  }}
                  onKeyDown={(e) => e.key === 'Enter' && handleNameContinue()}
                  placeholder="Enter your name"
                  className={`w-full rounded-lg border ${nameError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
                  autoFocus
                />
                {nameError && (
                  <p className="text-red-500 text-xs mt-1">{nameError}</p>
                )}

                <button
                  onClick={handleNameContinue}
                  className={`w-full py-2 rounded-lg transition-colors ${
                    nameError !== ""
                      ? "bg-blue-300 text-white cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                  disabled={nameError !== ""}
                >
                  {success === "Sending OTP..." ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending verification code...
                    </div>
                  ) : (
                    "Continue"
                  )}
                </button>
              </>
            )}

            {/* Verification Step (for new users) */}
            {emailStep === "verify" && (
              <>
                <div className="bg-blue-50 text-blue-600 p-3 rounded-lg text-sm mb-4">
                  <div className="flex items-center">
                    <FiMail className="mr-2 flex-shrink-0" />
                    <div className="flex flex-col">
                      <span>Verification code has been sent to:</span>
                      <strong className="mt-1">{email}</strong>
                      <span className="text-xs mt-1">Please check your inbox and spam folder</span>
                    </div>
                  </div>
                </div>

                <label className="block text-sm font-medium text-gray-600">Enter Verification Code</label>
                <input
                  type="text"
                  value={otp}
                  onChange={(e) => {
                    setOtp(e.target.value);
                    // Clear main error when typing
                    setError("");
                    // Validate on change
                    if (e.target.value.trim()) {
                      const validation = validateOTP(e.target.value);
                      setOtpError(validation.isValid ? "" : validation.error);
                    } else {
                      setOtpError("");
                    }
                  }}
                  onKeyDown={(e) => e.key === 'Enter' && handleRegisterWithOTP()}
                  placeholder="Enter verification code"
                  className={`w-full rounded-lg border ${otpError ? 'border-red-300' : 'border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-3 py-2 text-gray-900`}
                  autoFocus
                />
                {otpError && (
                  <p className="text-red-500 text-xs mt-1">{otpError}</p>
                )}

                <button
                  onClick={handleRegisterWithOTP}
                  className={`w-full py-2 rounded-lg transition-colors ${
                    otpError !== "" || success === "Creating account..."
                      ? "bg-blue-300 text-white cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                  disabled={otpError !== "" || success === "Creating account..."}
                >
                  {success === "Creating account..." ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating account...
                    </div>
                  ) : (
                    "Create Account"
                  )}
                </button>

                <div className="text-center mt-2">
                  <button
                    onClick={handleNameContinue}
                    className="text-blue-600 text-sm hover:underline"
                  >
                    Didn't receive code? Send again
                  </button>
                </div>
              </>
            )}

            {/* No previous step button at the bottom anymore */}
          </div>
        )}

        {/* OR Divider */}
        <div className="flex items-center justify-center">
          <hr className="w-full border-gray-300" />
          <span className="mx-2 text-sm text-gray-400">OR</span>
          <hr className="w-full border-gray-300" />
        </div>

        {/* Social Login Options - Circular Icons Only */}
        <div className="flex flex-row gap-4 justify-center w-full">
          {/* Loading indicator for social login */}
          {loading && (
            <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
              <div className="flex flex-col items-center">
                <svg className="animate-spin h-10 w-10 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="mt-2 text-blue-600 font-medium">Logging in...</p>
              </div>
            </div>
          )}

          {/* Google button container */}
          <div
            id="google-button"
            className="flex items-center justify-center w-10 h-10 border-gray-300 rounded-full hover:bg-gray-50 transition-colors focus:outline-none overflow-hidden"
            style={{ minWidth: '40px', minHeight: '40px' }} // Fixed dimensions to prevent layout shift
          ></div>

          <button
            id="facebook-login"
            type="button"
            className="flex items-center justify-center w-10 h-10 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors p-0"
            style={{ minWidth: '40px', minHeight: '40px' }} // Fixed dimensions to prevent layout shift
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/0/05/Facebook_Logo_%282019%29.png"
              alt="Facebook"
              className="w-6 h-6 flex-shrink-0"
            />
          </button>

          <button
            onClick={() => {
              const newMethod = loginMethod === "email" ? "phone" : "email";

              // Batch state updates to prevent flickering
              if (newMethod === "phone") {
                // Reset to phone input step and update method in one go
                setPhoneStep("input");
                setPhoneTurnstileToken("");
                setPasswordTurnstileToken("");
                setPhone("");
                setLoginMethod(newMethod);
              } else {
                // For email method, update all states at once
                setEmailStep("email");
                setPhoneTurnstileToken("");
                setPasswordTurnstileToken("");
                setLoginMethod(newMethod);
              }
            }}
            type="button"
            className="flex items-center justify-center w-10 h-10 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors p-0"
          >
            {loginMethod === "email" ? <FiPhone className="w-6 h-6 flex-shrink-0" /> : <FiMail className="w-6 h-6 flex-shrink-0" />}
          </button>
        </div>

        {/* Footer Text */}
        <p className="text-xs text-gray-400 text-center mt-2">
          By logging in, you agree to our{" "}
          <a href="#!" className="text-blue-600 hover:underline">
            T&amp;C
          </a>{" "}
          and{" "}
          <a href="#!" className="text-blue-600 hover:underline">
            Privacy Policy
          </a>.
        </p>

      </div>
    </div>
  );
};

export default Login;
