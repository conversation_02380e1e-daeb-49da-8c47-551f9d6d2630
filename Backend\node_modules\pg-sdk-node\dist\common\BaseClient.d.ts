import { AxiosInstance } from 'axios';
import { Env } from '../Env';
import { CredentialConfig } from './configs/CredentialConfig';
import { TokenService } from './tokenhandler/TokenService';
import { HttpMethodType } from './http/HttpMethodType';
import { ClassType } from 'class-transformer-validator';
import { EventPublisher } from './events/publisher/EventPublisher';
export declare abstract class BaseClient {
    private readonly _env;
    private readonly _tokenService;
    private readonly _merchantConfig;
    private readonly _httpCommand;
    private readonly _httpClient;
    private readonly _shouldPublishEvents;
    private readonly _eventPublisherFactory;
    private readonly _eventPublisher;
    protected constructor(clientId: string, clientSecret: string, clientVersion: number, env: Env, shouldPublishEvents: boolean);
    protected requestViaAuthRefresh: <T>(method: HttpMethodType, url: string, responseType: ClassType<T>, headers: {
        [key: string]: string;
    }, data?: object, pathParams?: {
        [key: string]: string;
    } | undefined) => Promise<T>;
    private addAuthHeader;
    /**
     * Getters for BaseClient fields
     */
    get env(): Env;
    get httpClient(): AxiosInstance;
    get tokenService(): TokenService;
    get merchantConfig(): CredentialConfig;
    get shouldPublishEvents(): boolean;
    get eventPublisher(): EventPublisher;
}
