{"version": 3, "file": "formatPhoneNumberForMobileDialing.js", "names": ["REGION_CODE_FOR_NON_GEO_ENTITY", "number", "from_country", "with_formatting", "metadata", "<PERSON><PERSON><PERSON>", "hasCountry", "Error", "phone", "country", "number_type", "getNumberType", "undefined", "is_valid_number", "formatted_number", "is_fixed_line_or_mobile", "carrierCode", "formatNationalNumberWithPreferredCarrierCode", "getCountryCallingCode", "can_be_internationally_dialled", "checkNumberLength", "format", "diallable_chars", "DIALLABLE_CHARACTERS", "result", "i", "length", "character", "getPreferredDomesticCarrierCodeOrDefault", "formatNationalNumberWithCarrierCode"], "sources": ["../source/formatPhoneNumberForMobileDialing.js"], "sourcesContent": ["// This function is copy-pasted from\r\n// https://github.com/googlei18n/libphonenumber/blob/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n// It hasn't been tested. It's not currently exported.\r\n// Carriers codes aren't part of this library.\r\n// Send a PR if you want to add them.\r\n\r\nimport Metadata from './metadata.js'\r\nimport format from './format.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\nimport checkNumberLength from './helpers/checkNumberLength.js'\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\n\r\nconst REGION_CODE_FOR_NON_GEO_ENTITY = '001'\r\n\r\n/**\r\n * Returns a number formatted in such a way that it can be dialed from a mobile\r\n * phone in a specific region. If the number cannot be reached from the region\r\n * (e.g. some countries block toll-free numbers from being called outside of the\r\n * country), the method returns an empty string.\r\n *\r\n * @param {object} number - a `parse()`d phone number to be formatted.\r\n * @param {string} from_country - the region where the call is being placed.\r\n * @param {boolean} with_formatting - whether the number should be returned with\r\n *     formatting symbols, such as spaces and dashes.\r\n * @return {string}\r\n */\r\nexport default function(number, from_country, with_formatting, metadata) {\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// Validate `from_country`.\r\n\tif (!metadata.hasCountry(from_country)) {\r\n\t\tthrow new Error(`Unknown country: ${from_country}`)\r\n\t}\r\n\r\n\t// Not using the extension, as that part cannot normally be dialed\r\n\t// together with the main number.\r\n\tnumber = {\r\n\t\tphone: number.phone,\r\n\t\tcountry: number.country\r\n\t}\r\n\r\n\tconst number_type = getNumberType(number, undefined, metadata.metadata)\r\n\tconst is_valid_number = number_type === number\r\n\r\n\tlet formatted_number\r\n\r\n\tif (country === from_country) {\r\n\t\tconst is_fixed_line_or_mobile =\r\n\t\t\tnumber_type === 'FIXED_LINE' ||\r\n\t\t\tnumber_type === 'MOBILE' ||\r\n\t\t\tnumber_type === 'FIXED_LINE_OR_MOBILE'\r\n\r\n\t\t// Carrier codes may be needed in some countries. We handle this here.\r\n\t\tif (country == 'BR' && is_fixed_line_or_mobile) {\r\n\t\t\tformatted_number =\r\n\t\t\t\tcarrierCode ?\r\n\t\t\t\tformatNationalNumberWithPreferredCarrierCode(number) :\r\n\t\t\t\t// Brazilian fixed line and mobile numbers need to be dialed with a\r\n\t\t\t\t// carrier code when called within Brazil. Without that, most of the\r\n\t\t\t\t// carriers won't connect the call. Because of that, we return an\r\n\t\t\t\t// empty string here.\r\n\t\t\t\t''\r\n\t\t} else if (getCountryCallingCode(country, metadata.metadata) === '1') {\r\n\t\t\t// For NANPA countries, we output international format for numbers that\r\n\t\t\t// can be dialed internationally, since that always works, except for\r\n\t\t\t// numbers which might potentially be short numbers, which are always\r\n\t\t\t// dialled in national format.\r\n\r\n\t\t\t// Select country for `checkNumberLength()`.\r\n\t\t\tmetadata.country(country)\r\n\r\n\t\t\tif (can_be_internationally_dialled(number) &&\r\n\t\t\t\tcheckNumberLength(number.phone, metadata) !== 'TOO_SHORT') {\r\n\t\t\t\tformatted_number = format(number, 'INTERNATIONAL', metadata.metadata)\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tformatted_number = format(number, 'NATIONAL', metadata.metadata)\r\n\t\t\t}\r\n\t\t}\r\n\t\telse {\r\n\t\t\t// For non-geographic countries, Mexican and Chilean fixed line and\r\n\t\t\t// mobile numbers, we output international format for numbers that can be\r\n\t\t\t// dialed internationally, as that always works.\r\n\t\t\tif (\r\n\t\t\t\t(\r\n\t\t\t\t\tcountry === REGION_CODE_FOR_NON_GEO_ENTITY\r\n\t\t\t\t\t||\r\n\t\t\t\t\t// MX fixed line and mobile numbers should always be formatted in\r\n\t\t\t\t\t// international format, even when dialed within MX. For national\r\n\t\t\t\t\t// format to work, a carrier code needs to be used, and the correct\r\n\t\t\t\t\t// carrier code depends on if the caller and callee are from the\r\n\t\t\t\t\t// same local area. It is trickier to get that to work correctly than\r\n\t\t\t\t\t// using international format, which is tested to work fine on all\r\n\t\t\t\t\t// carriers.\r\n\t\t\t\t\t//\r\n\t\t\t\t\t// CL fixed line numbers need the national prefix when dialing in the\r\n\t\t\t\t\t// national format, but don't have it when used for display. The\r\n\t\t\t\t\t// reverse is true for mobile numbers. As a result, we output them in\r\n\t\t\t\t\t// the international format to make it work.\r\n\t\t\t\t\t//\r\n\t\t\t\t\t// UZ mobile and fixed-line numbers have to be formatted in\r\n\t\t\t\t\t// international format or prefixed with special codes like 03, 04\r\n\t\t\t\t\t// (for fixed-line) and 05 (for mobile) for dialling successfully\r\n\t\t\t\t\t// from mobile devices. As we do not have complete information on\r\n\t\t\t\t\t// special codes and to be consistent with formatting across all\r\n\t\t\t\t\t// phone types we return the number in international format here.\r\n\t\t\t\t\t//\r\n\t\t\t\t\t((country === 'MX' || country === 'CL' || country == 'UZ') && is_fixed_line_or_mobile)\r\n\t\t\t\t)\r\n\t\t\t\t&&\r\n\t\t\t\tcan_be_internationally_dialled(number)\r\n\t\t\t) {\r\n\t\t\t\tformatted_number = format(number, 'INTERNATIONAL')\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tformatted_number = format(number, 'NATIONAL')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\telse if (is_valid_number && can_be_internationally_dialled(number)) {\r\n\t\t// We assume that short numbers are not diallable from outside their region,\r\n\t\t// so if a number is not a valid regular length phone number, we treat it as\r\n\t\t// if it cannot be internationally dialled.\r\n\t\treturn with_formatting ?\r\n\t\t\tformat(number, 'INTERNATIONAL', metadata.metadata) :\r\n\t\t\tformat(number, 'E.164', metadata.metadata)\r\n\t}\r\n\r\n\tif (!with_formatting) {\r\n\t\treturn diallable_chars(formatted_number)\r\n\t}\r\n\r\n\treturn formatted_number\r\n}\r\n\r\nfunction can_be_internationally_dialled(number) {\r\n\treturn true\r\n}\r\n\r\n/**\r\n * A map that contains characters that are essential when dialling. That means\r\n * any of the characters in this map must not be removed from a number when\r\n * dialling, otherwise the call will not reach the intended destination.\r\n */\r\nconst DIALLABLE_CHARACTERS = {\r\n\t'0': '0',\r\n\t'1': '1',\r\n\t'2': '2',\r\n\t'3': '3',\r\n\t'4': '4',\r\n\t'5': '5',\r\n\t'6': '6',\r\n\t'7': '7',\r\n\t'8': '8',\r\n\t'9': '9',\r\n\t'+': '+',\r\n\t'*': '*',\r\n\t'#': '#'\r\n}\r\n\r\nfunction diallable_chars(formatted_number) {\r\n\tlet result = ''\r\n\r\n\tlet i = 0\r\n\twhile (i < formatted_number.length) {\r\n\t\tconst character = formatted_number[i]\r\n\t\tif (DIALLABLE_CHARACTERS[character]) {\r\n\t\t\tresult += character\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\r\n\treturn result\r\n}\r\n\r\nfunction getPreferredDomesticCarrierCodeOrDefault() {\r\n\tthrow new Error('carrier codes are not part of this library')\r\n}\r\n\r\nfunction formatNationalNumberWithCarrierCode() {\r\n\tthrow new Error('carrier codes are not part of this library')\r\n}\r\n\r\n/**\r\n * Formats a phone number in national format for dialing using the carrier as\r\n * specified in the preferred_domestic_carrier_code field of the PhoneNumber\r\n * object passed in. If that is missing, use the {@code fallbackCarrierCode}\r\n * passed in instead. If there is no {@code preferred_domestic_carrier_code},\r\n * and the {@code fallbackCarrierCode} contains an empty string, return the\r\n * number in national format without any carrier code.\r\n *\r\n * <p>Use {@link #formatNationalNumberWithCarrierCode} instead if the carrier\r\n * code passed in should take precedence over the number's\r\n * {@code preferred_domestic_carrier_code} when formatting.\r\n *\r\n * @param {i18n.phonenumbers.PhoneNumber} number the phone number to be\r\n *     formatted.\r\n * @param {string} fallbackCarrierCode the carrier selection code to be used, if\r\n *     none is found in the phone number itself.\r\n * @return {string} the formatted phone number in national format for dialing\r\n *     using the number's preferred_domestic_carrier_code, or the\r\n *     {@code fallbackCarrierCode} passed in if none is found.\r\n */\r\nfunction formatNationalNumberWithPreferredCarrierCode(number) {\r\n\treturn formatNationalNumberWithCarrierCode(\r\n\t\tnumber,\r\n\t\tcarrierCode\r\n\t);\r\n}"], "mappings": ";;;;;;;AAMA;;AACA;;AACA;;AACA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AAQA,IAAMA,8BAA8B,GAAG,KAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACe,kBAASC,MAAT,EAAiBC,YAAjB,EAA+BC,eAA/B,EAAgDC,QAAhD,EAA0D;EACxEA,QAAQ,GAAG,IAAIC,oBAAJ,CAAaD,QAAb,CAAX,CADwE,CAGxE;;EACA,IAAI,CAACA,QAAQ,CAACE,UAAT,CAAoBJ,YAApB,CAAL,EAAwC;IACvC,MAAM,IAAIK,KAAJ,4BAA8BL,YAA9B,EAAN;EACA,CANuE,CAQxE;EACA;;;EACAD,MAAM,GAAG;IACRO,KAAK,EAAEP,MAAM,CAACO,KADN;IAERC,OAAO,EAAER,MAAM,CAACQ;EAFR,CAAT;EAKA,IAAMC,WAAW,GAAG,IAAAC,yBAAA,EAAcV,MAAd,EAAsBW,SAAtB,EAAiCR,QAAQ,CAACA,QAA1C,CAApB;EACA,IAAMS,eAAe,GAAGH,WAAW,KAAKT,MAAxC;EAEA,IAAIa,gBAAJ;;EAEA,IAAIL,OAAO,KAAKP,YAAhB,EAA8B;IAC7B,IAAMa,uBAAuB,GAC5BL,WAAW,KAAK,YAAhB,IACAA,WAAW,KAAK,QADhB,IAEAA,WAAW,KAAK,sBAHjB,CAD6B,CAM7B;;IACA,IAAID,OAAO,IAAI,IAAX,IAAmBM,uBAAvB,EAAgD;MAC/CD,gBAAgB,GACfE,WAAW,GACXC,4CAA4C,CAAChB,MAAD,CADjC,GAEX;MACA;MACA;MACA;MACA,EAPD;IAQA,CATD,MASO,IAAI,IAAAiB,iCAAA,EAAsBT,OAAtB,EAA+BL,QAAQ,CAACA,QAAxC,MAAsD,GAA1D,EAA+D;MACrE;MACA;MACA;MACA;MAEA;MACAA,QAAQ,CAACK,OAAT,CAAiBA,OAAjB;;MAEA,IAAIU,8BAA8B,CAAClB,MAAD,CAA9B,IACH,IAAAmB,6BAAA,EAAkBnB,MAAM,CAACO,KAAzB,EAAgCJ,QAAhC,MAA8C,WAD/C,EAC4D;QAC3DU,gBAAgB,GAAG,IAAAO,kBAAA,EAAOpB,MAAP,EAAe,eAAf,EAAgCG,QAAQ,CAACA,QAAzC,CAAnB;MACA,CAHD,MAIK;QACJU,gBAAgB,GAAG,IAAAO,kBAAA,EAAOpB,MAAP,EAAe,UAAf,EAA2BG,QAAQ,CAACA,QAApC,CAAnB;MACA;IACD,CAhBM,MAiBF;MACJ;MACA;MACA;MACA,IACC,CACCK,OAAO,KAAKT,8BAAZ,IAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACC,CAACS,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,IAAhC,IAAwCA,OAAO,IAAI,IAApD,KAA6DM,uBAvB/D,KA0BAI,8BAA8B,CAAClB,MAAD,CA3B/B,EA4BE;QACDa,gBAAgB,GAAG,IAAAO,kBAAA,EAAOpB,MAAP,EAAe,eAAf,CAAnB;MACA,CA9BD,MA+BK;QACJa,gBAAgB,GAAG,IAAAO,kBAAA,EAAOpB,MAAP,EAAe,UAAf,CAAnB;MACA;IACD;EACD,CAxED,MAyEK,IAAIY,eAAe,IAAIM,8BAA8B,CAAClB,MAAD,CAArD,EAA+D;IACnE;IACA;IACA;IACA,OAAOE,eAAe,GACrB,IAAAkB,kBAAA,EAAOpB,MAAP,EAAe,eAAf,EAAgCG,QAAQ,CAACA,QAAzC,CADqB,GAErB,IAAAiB,kBAAA,EAAOpB,MAAP,EAAe,OAAf,EAAwBG,QAAQ,CAACA,QAAjC,CAFD;EAGA;;EAED,IAAI,CAACD,eAAL,EAAsB;IACrB,OAAOmB,eAAe,CAACR,gBAAD,CAAtB;EACA;;EAED,OAAOA,gBAAP;AACA;;AAED,SAASK,8BAAT,CAAwClB,MAAxC,EAAgD;EAC/C,OAAO,IAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,IAAMsB,oBAAoB,GAAG;EAC5B,KAAK,GADuB;EAE5B,KAAK,GAFuB;EAG5B,KAAK,GAHuB;EAI5B,KAAK,GAJuB;EAK5B,KAAK,GALuB;EAM5B,KAAK,GANuB;EAO5B,KAAK,GAPuB;EAQ5B,KAAK,GARuB;EAS5B,KAAK,GATuB;EAU5B,KAAK,GAVuB;EAW5B,KAAK,GAXuB;EAY5B,KAAK,GAZuB;EAa5B,KAAK;AAbuB,CAA7B;;AAgBA,SAASD,eAAT,CAAyBR,gBAAzB,EAA2C;EAC1C,IAAIU,MAAM,GAAG,EAAb;EAEA,IAAIC,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAGX,gBAAgB,CAACY,MAA5B,EAAoC;IACnC,IAAMC,SAAS,GAAGb,gBAAgB,CAACW,CAAD,CAAlC;;IACA,IAAIF,oBAAoB,CAACI,SAAD,CAAxB,EAAqC;MACpCH,MAAM,IAAIG,SAAV;IACA;;IACDF,CAAC;EACD;;EAED,OAAOD,MAAP;AACA;;AAED,SAASI,wCAAT,GAAoD;EACnD,MAAM,IAAIrB,KAAJ,CAAU,4CAAV,CAAN;AACA;;AAED,SAASsB,mCAAT,GAA+C;EAC9C,MAAM,IAAItB,KAAJ,CAAU,4CAAV,CAAN;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASU,4CAAT,CAAsDhB,MAAtD,EAA8D;EAC7D,OAAO4B,mCAAmC,CACzC5B,MADyC,EAEzCe,WAFyC,CAA1C;AAIA"}