import { CreateSdkOrderRequest, CustomCheckoutPayRequest, StandardCheckoutPayRequest } from '../../../payments/v2';
import { PaymentFlowType, RefundRequest } from '../../models';
import { BaseEvent } from '../models/BaseEvent';
import { EventState } from '../models/enums/EventState';
import { EventType } from '../models/enums/EventType';
export declare const buildInitClientEvent: (eventName: EventType, paymentFlow?: PaymentFlowType) => BaseEvent;
export declare const buildStandardCheckoutPayEvent: (eventState: EventState, eventName: EventType, standardCheckoutPayRequest: StandardCheckoutPayRequest, apiPath: string, exception?: unknown) => BaseEvent;
export declare const buildCustomCheckoutPayRequest: (eventState: EventState, eventName: EventType, request: CustomCheckoutPayRequest, apiPath: string, exception?: unknown) => BaseEvent;
export declare const buildOrderStatusEvent: (eventState: EventState, merchantOrderId: string, paymentFlow: PaymentFlowType, apiPath: string, eventName: EventType, exception?: unknown) => BaseEvent;
export declare const buildRefundEvent: (eventState: EventState, refundRequest: RefundRequest, apiPath: string, paymentFlow: PaymentFlowType, eventName: EventType, exception?: unknown) => BaseEvent;
export declare const buildRefundStatusEvent: (eventState: EventState, eventName: EventType, refundId: string, apiPath: string, paymentFlow: PaymentFlowType, exception?: unknown) => BaseEvent;
export declare const buildCreateSdkOrderEvent: (eventState: EventState, eventName: EventType, createSdkOrderRequest: CreateSdkOrderRequest, apiPath: string, paymentFlow: PaymentFlowType, exception?: unknown) => BaseEvent;
export declare const buildTransactionStatusEvent: (eventState: EventState, eventName: EventType, transactionId: string, apiPath: string, paymentFlow: PaymentFlowType, exception?: unknown) => BaseEvent;
export declare const buildOAuthEvent: (fetchAttemptTime: number, apiPath: string, eventName: EventType, exception: unknown, cachedTokenIssuedAt: number, cachedTokenExpiresAt: number) => BaseEvent;
export declare const buildCallbackSerializationFailedEvent: (eventState: EventState, paymentFlow: PaymentFlowType, eventName: EventType, exception?: unknown) => BaseEvent;
export declare const populateExceptionFields: (event: BaseEvent, exception: any) => BaseEvent;
