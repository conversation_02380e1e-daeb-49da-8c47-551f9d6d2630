{"version": 3, "file": "IsEnum.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsEnum.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,MAAW;IAChD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,MAAW;IAClC,OAAO,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;SAC1B,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAe,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,MAAc,EAAE,iBAAqC;IAC1E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAC9C,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,6DAA6D,EACxF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_ENUM = 'isEnum';\n\n/**\n * Checks if a given value is the member of the provided enum.\n */\nexport function isEnum(value: unknown, entity: any): boolean {\n  const enumValues = Object.keys(entity).map(k => entity[k]);\n  return enumValues.includes(value);\n}\n\n/**\n * Returns the possible values from an enum (both simple number indexed and string indexed enums).\n */\nfunction validEnumValues(entity: any): string[] {\n  return Object.entries(entity)\n    .filter(([key, value]) => isNaN(parseInt(key)))\n    .map(([key, value]) => value as string);\n}\n\n/**\n * Checks if a given value is the member of the provided enum.\n */\nexport function IsEnum(entity: object, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ENUM,\n      constraints: [entity, validEnumValues(entity)],\n      validator: {\n        validate: (value, args): boolean => isEnum(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be one of the following values: $constraint2',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}