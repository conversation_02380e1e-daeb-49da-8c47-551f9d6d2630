{"version": 3, "file": "parseIncompletePhoneNumber.test.js", "names": ["parseIncompletePhoneNumber", "parsePhoneNumberCharacter", "describe", "it", "should", "equal", "expect", "to", "be", "undefined", "stopped", "emit", "event"], "sources": ["../source/parseIncompletePhoneNumber.test.js"], "sourcesContent": ["import parseIncompletePhoneNumber, { parsePhoneNumberCharacter } from './parseIncompletePhoneNumber.js'\r\n\r\ndescribe('parseIncompletePhoneNumber', () => {\r\n\tit('should parse phone number character', () => {\r\n\t\t// Accepts leading `+`.\r\n\t\tparsePhoneNumberCharacter('+').should.equal('+')\r\n\r\n\t\t// Doesn't accept non-leading `+`.\r\n\t\texpect(parsePhoneNumberCharacter('+', '+')).to.be.undefined\r\n\r\n\t\t// Parses digits.\r\n\t\tparsePhoneNumberCharacter('1').should.equal('1')\r\n\r\n\t\t// Parses non-European digits.\r\n\t\tparsePhoneNumberCharacter('٤').should.equal('4')\r\n\r\n\t\t// Dismisses other characters.\r\n\t\texpect(parsePhoneNumberCharacter('-')).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse incomplete phone number', () => {\r\n\t\tparseIncompletePhoneNumber('').should.equal('')\r\n\r\n\t\t// Doesn't accept non-leading `+`.\r\n\t\tparseIncompletePhoneNumber('++').should.equal('+')\r\n\r\n\t\t// Accepts leading `+`.\r\n\t\tparseIncompletePhoneNumber('****** 555').should.equal('+7800555')\r\n\r\n\t\t// Parses digits.\r\n\t\tparseIncompletePhoneNumber('8 (800) 555').should.equal('8800555')\r\n\r\n\t\t// Parses non-European digits.\r\n\t\tparseIncompletePhoneNumber('+٤٤٢٣٢٣٢٣٤').should.equal('+442323234')\r\n\t})\r\n\r\n\tit('should work with a new `context` argument in `parsePhoneNumberCharacter()` function (international number)', () => {\r\n\t\tlet stopped = false\r\n\r\n\t\tconst emit = (event) => {\r\n\t\t\tswitch (event) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tstopped = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tparsePhoneNumberCharacter('+', undefined, emit).should.equal('+')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\tparsePhoneNumberCharacter('1', '+', emit).should.equal('1')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('+', '+1', emit)).to.equal(undefined)\r\n\t\texpect(stopped).to.equal(true)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('2', '+1', emit)).to.equal('2')\r\n\t\texpect(stopped).to.equal(true)\r\n\t})\r\n\r\n\tit('should work with a new `context` argument in `parsePhoneNumberCharacter()` function (national number)', () => {\r\n\t\tlet stopped = false\r\n\r\n\t\tconst emit = (event) => {\r\n\t\t\tswitch (event) {\r\n\t\t\t\tcase 'end':\r\n\t\t\t\t\tstopped = true\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tparsePhoneNumberCharacter('2', undefined, emit).should.equal('2')\r\n\t\texpect(stopped).to.equal(false)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('+', '2', emit)).to.equal(undefined)\r\n\t\texpect(stopped).to.equal(true)\r\n\r\n\t\texpect(parsePhoneNumberCharacter('1', '2', emit)).to.equal('1')\r\n\t\texpect(stopped).to.equal(true)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,0BAAP,IAAqCC,yBAArC,QAAsE,iCAAtE;AAEAC,QAAQ,CAAC,4BAAD,EAA+B,YAAM;EAC5CC,EAAE,CAAC,qCAAD,EAAwC,YAAM;IAC/C;IACAF,yBAAyB,CAAC,GAAD,CAAzB,CAA+BG,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAF+C,CAI/C;;IACAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,EAAM,GAAN,CAA1B,CAAN,CAA4CM,EAA5C,CAA+CC,EAA/C,CAAkDC,SAAlD,CAL+C,CAO/C;;IACAR,yBAAyB,CAAC,GAAD,CAAzB,CAA+BG,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAR+C,CAU/C;;IACAJ,yBAAyB,CAAC,GAAD,CAAzB,CAA+BG,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C,EAX+C,CAa/C;;IACAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,CAA1B,CAAN,CAAuCM,EAAvC,CAA0CC,EAA1C,CAA6CC,SAA7C;EACA,CAfC,CAAF;EAiBAN,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChDH,0BAA0B,CAAC,EAAD,CAA1B,CAA+BI,MAA/B,CAAsCC,KAAtC,CAA4C,EAA5C,EADgD,CAGhD;;IACAL,0BAA0B,CAAC,IAAD,CAA1B,CAAiCI,MAAjC,CAAwCC,KAAxC,CAA8C,GAA9C,EAJgD,CAMhD;;IACAL,0BAA0B,CAAC,YAAD,CAA1B,CAAyCI,MAAzC,CAAgDC,KAAhD,CAAsD,UAAtD,EAPgD,CAShD;;IACAL,0BAA0B,CAAC,aAAD,CAA1B,CAA0CI,MAA1C,CAAiDC,KAAjD,CAAuD,SAAvD,EAVgD,CAYhD;;IACAL,0BAA0B,CAAC,YAAD,CAA1B,CAAyCI,MAAzC,CAAgDC,KAAhD,CAAsD,YAAtD;EACA,CAdC,CAAF;EAgBAF,EAAE,CAAC,4GAAD,EAA+G,YAAM;IACtH,IAAIO,OAAO,GAAG,KAAd;;IAEA,IAAMC,IAAI,GAAG,SAAPA,IAAO,CAACC,KAAD,EAAW;MACvB,QAAQA,KAAR;QACC,KAAK,KAAL;UACCF,OAAO,GAAG,IAAV;UACA;MAHF;IAKA,CAND;;IAQAT,yBAAyB,CAAC,GAAD,EAAMQ,SAAN,EAAiBE,IAAjB,CAAzB,CAAgDP,MAAhD,CAAuDC,KAAvD,CAA6D,GAA7D;IACAC,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEAJ,yBAAyB,CAAC,GAAD,EAAM,GAAN,EAAWU,IAAX,CAAzB,CAA0CP,MAA1C,CAAiDC,KAAjD,CAAuD,GAAvD;IACAC,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,EAAM,IAAN,EAAYU,IAAZ,CAA1B,CAAN,CAAmDJ,EAAnD,CAAsDF,KAAtD,CAA4DI,SAA5D;IACAH,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;IAEAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,EAAM,IAAN,EAAYU,IAAZ,CAA1B,CAAN,CAAmDJ,EAAnD,CAAsDF,KAAtD,CAA4D,GAA5D;IACAC,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;EACA,CAtBC,CAAF;EAwBAF,EAAE,CAAC,uGAAD,EAA0G,YAAM;IACjH,IAAIO,OAAO,GAAG,KAAd;;IAEA,IAAMC,IAAI,GAAG,SAAPA,IAAO,CAACC,KAAD,EAAW;MACvB,QAAQA,KAAR;QACC,KAAK,KAAL;UACCF,OAAO,GAAG,IAAV;UACA;MAHF;IAKA,CAND;;IAQAT,yBAAyB,CAAC,GAAD,EAAMQ,SAAN,EAAiBE,IAAjB,CAAzB,CAAgDP,MAAhD,CAAuDC,KAAvD,CAA6D,GAA7D;IACAC,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,KAAzB;IAEAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,EAAM,GAAN,EAAWU,IAAX,CAA1B,CAAN,CAAkDJ,EAAlD,CAAqDF,KAArD,CAA2DI,SAA3D;IACAH,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;IAEAC,MAAM,CAACL,yBAAyB,CAAC,GAAD,EAAM,GAAN,EAAWU,IAAX,CAA1B,CAAN,CAAkDJ,EAAlD,CAAqDF,KAArD,CAA2D,GAA3D;IACAC,MAAM,CAACI,OAAD,CAAN,CAAgBH,EAAhB,CAAmBF,KAAnB,CAAyB,IAAzB;EACA,CAnBC,CAAF;AAoBA,CA9EO,CAAR"}