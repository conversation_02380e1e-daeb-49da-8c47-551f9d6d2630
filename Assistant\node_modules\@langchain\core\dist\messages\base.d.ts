import { Serializable, SerializedConstructor } from "../load/serializable.js";
import { StringWithAutocomplete } from "../utils/types/index.js";
export interface StoredMessageData {
    content: string;
    role: string | undefined;
    name: string | undefined;
    tool_call_id: string | undefined;
    additional_kwargs?: Record<string, any>;
    /** Response metadata. For example: response headers, logprobs, token counts. */
    response_metadata?: Record<string, any>;
    id?: string;
}
export interface StoredMessage {
    type: string;
    data: StoredMessageData;
}
export interface StoredGeneration {
    text: string;
    message?: StoredMessage;
}
export interface StoredMessageV1 {
    type: string;
    role: string | undefined;
    text: string;
}
export type MessageType = "human" | "ai" | "generic" | "developer" | "system" | "function" | "tool" | "remove";
export type ImageDetail = "auto" | "low" | "high";
export type MessageContentText = {
    type: "text";
    text: string;
};
export type MessageContentImageUrl = {
    type: "image_url";
    image_url: string | {
        url: string;
        detail?: ImageDetail;
    };
};
export type MessageContentComplex = MessageContentText | MessageContentImageUrl | (Record<string, any> & {
    type?: "text" | "image_url" | string;
}) | (Record<string, any> & {
    type?: never;
});
export type MessageContent = string | MessageContentComplex[];
export interface FunctionCall {
    /**
     * The arguments to call the function with, as generated by the model in JSON
     * format. Note that the model does not always generate valid JSON, and may
     * hallucinate parameters not defined by your function schema. Validate the
     * arguments in your code before calling your function.
     */
    arguments: string;
    /**
     * The name of the function to call.
     */
    name: string;
}
export type BaseMessageFields = {
    content: MessageContent;
    name?: string;
    additional_kwargs?: {
        /**
         * @deprecated Use "tool_calls" field on AIMessages instead
         */
        function_call?: FunctionCall;
        /**
         * @deprecated Use "tool_calls" field on AIMessages instead
         */
        tool_calls?: OpenAIToolCall[];
        [key: string]: unknown;
    };
    /** Response metadata. For example: response headers, logprobs, token counts. */
    response_metadata?: Record<string, any>;
    /**
     * An optional unique identifier for the message. This should ideally be
     * provided by the provider/model which created the message.
     */
    id?: string;
};
export declare function mergeContent(firstContent: MessageContent, secondContent: MessageContent): MessageContent;
/**
 * 'Merge' two statuses. If either value passed is 'error', it will return 'error'. Else
 * it will return 'success'.
 *
 * @param {"success" | "error" | undefined} left The existing value to 'merge' with the new value.
 * @param {"success" | "error" | undefined} right The new value to 'merge' with the existing value
 * @returns {"success" | "error"} The 'merged' value.
 */
export declare function _mergeStatus(left?: "success" | "error", right?: "success" | "error"): "success" | "error" | undefined;
/**
 * Base class for all types of messages in a conversation. It includes
 * properties like `content`, `name`, and `additional_kwargs`. It also
 * includes methods like `toDict()` and `_getType()`.
 */
export declare abstract class BaseMessage extends Serializable implements BaseMessageFields {
    lc_namespace: string[];
    lc_serializable: boolean;
    get lc_aliases(): Record<string, string>;
    /**
     * Get text content of the message.
     */
    get text(): string;
    /** The content of the message. */
    content: MessageContent;
    /** The name of the message sender in a multi-user chat. */
    name?: string;
    /** Additional keyword arguments */
    additional_kwargs: NonNullable<BaseMessageFields["additional_kwargs"]>;
    /** Response metadata. For example: response headers, logprobs, token counts. */
    response_metadata: NonNullable<BaseMessageFields["response_metadata"]>;
    /**
     * An optional unique identifier for the message. This should ideally be
     * provided by the provider/model which created the message.
     */
    id?: string;
    /**
     * @deprecated Use .getType() instead or import the proper typeguard.
     * For example:
     *
     * ```ts
     * import { isAIMessage } from "@langchain/core/messages";
     *
     * const message = new AIMessage("Hello!");
     * isAIMessage(message); // true
     * ```
     */
    abstract _getType(): MessageType;
    /** The type of the message. */
    getType(): MessageType;
    constructor(fields: string | BaseMessageFields, 
    /** @deprecated */
    kwargs?: Record<string, unknown>);
    toDict(): StoredMessage;
    static lc_name(): string;
    get _printableFields(): Record<string, unknown>;
    _updateId(value: string | undefined): void;
    get [Symbol.toStringTag](): any;
}
/**
 * @deprecated Use "tool_calls" field on AIMessages instead
 */
export type OpenAIToolCall = {
    /**
     * The ID of the tool call.
     */
    id: string;
    /**
     * The function that the model called.
     */
    function: FunctionCall;
    /**
     * The type of the tool. Currently, only `function` is supported.
     */
    type: "function";
    index?: number;
};
export declare function isOpenAIToolCallArray(value?: unknown): value is OpenAIToolCall[];
export declare function _mergeDicts(left: Record<string, any>, right: Record<string, any>): Record<string, any>;
export declare function _mergeLists(left?: any[], right?: any[]): any[] | undefined;
export declare function _mergeObj<T = any>(left: T | undefined, right: T | undefined): T;
/**
 * Represents a chunk of a message, which can be concatenated with other
 * message chunks. It includes a method `_merge_kwargs_dict()` for merging
 * additional keyword arguments from another `BaseMessageChunk` into this
 * one. It also overrides the `__add__()` method to support concatenation
 * of `BaseMessageChunk` instances.
 */
export declare abstract class BaseMessageChunk extends BaseMessage {
    abstract concat(chunk: BaseMessageChunk): BaseMessageChunk;
}
export type MessageFieldWithRole = {
    role: StringWithAutocomplete<"user" | "assistant" | MessageType>;
    content: MessageContent;
    name?: string;
} & Record<string, unknown>;
export declare function _isMessageFieldWithRole(x: BaseMessageLike): x is MessageFieldWithRole;
export type BaseMessageLike = BaseMessage | MessageFieldWithRole | [
    StringWithAutocomplete<MessageType | "user" | "assistant" | "placeholder">,
    MessageContent
] | string
/**
 * @deprecated Specifying "type" is deprecated and will be removed in 0.4.0.
 */
 | ({
    type: MessageType | "user" | "assistant" | "placeholder";
} & BaseMessageFields & Record<string, unknown>) | SerializedConstructor;
export declare function isBaseMessage(messageLike?: unknown): messageLike is BaseMessage;
export declare function isBaseMessageChunk(messageLike?: unknown): messageLike is BaseMessageChunk;
