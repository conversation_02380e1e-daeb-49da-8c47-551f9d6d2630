// CartController.js

export async function toggle_cart(req, res) {
  const { product_id, name, color, price, brand, imageurl } = req.body;
  const email = req.user?.email;

  // Enhanced validation and debugging
  console.log("Cart toggle request:", {
    email,
    product_id,
    name,
    color,
    price,
    brand,
    imageurl,
    body: req.body
  });

  if (!email || !product_id) {
    console.error("Missing required fields:", { email: !!email, product_id: !!product_id });
    return res.status(400).json({
      message: "Email and Product ID are required.",
      details: {
        email_provided: !!email,
        product_id_provided: !!product_id,
        received_body: req.body
      }
    });
  }
  try {
    const Products = req.app.locals.Products;
    const response = await Products.findOne({ email, product_id });
    if (response) {
      if (!response.cart && !response.wishlist) {
        response.cart = true;
        // Update other fields if provided
        if (name) response.name = name;
        if (color) response.color = color;
        if (price) response.price = price;
        if (brand) response.brand = brand;
        if (imageurl) response.imageurl = imageurl;
      } else if (response.cart && !response.wishlist) {
        await Products.deleteOne({ email, product_id });
        return res.status(200).json({ message: "Removed item from cart." });
      } else if (!response.cart && response.wishlist) {
        response.cart = true;
        // Update other fields if provided
        if (name) response.name = name;
        if (color) response.color = color;
        if (price) response.price = price;
        if (brand) response.brand = brand;
        if (imageurl) response.imageurl = imageurl;
      } else if (response.cart && response.wishlist) {
        response.cart = false;
      } else {
        return res.status(500).json({ message: "Unexpectedly something went wrong." });
      }
      await response.save();
      return res.status(200).json({ message: "Cart item updated.", response });
    } else {
      const newproducts = new Products({
        email,
        product_id,
        name: name,
        brand: brand,
        color: color,
        price: price,
        imageurl: imageurl || '',
        cart: true,
        wishlist: false
      });
      await newproducts.save();
      return res.status(201).json({ message: "Cart item created.", newproducts });
    }
  } catch (err) {
    console.error("Cart toggle error:", err);

    // Handle specific MongoDB errors
    if (err.code === 11000) {
      console.error("Duplicate key error details:", {
        keyPattern: err.keyPattern,
        keyValue: err.keyValue,
        errmsg: err.errmsg
      });

      return res.status(409).json({
        message: "Duplicate entry detected. This product may already be in your cart.",
        error: {
          type: "DUPLICATE_KEY_ERROR",
          details: err.errmsg
        }
      });
    }

    return res.status(500).json({
      message: "Something went wrong.",
      error: {
        message: err.message,
        code: err.code,
        name: err.name
      }
    });
  }
}
