"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.populateExceptionFields = exports.buildCallbackSerializationFailedEvent = exports.buildOAuthEvent = exports.buildTransactionStatusEvent = exports.buildCreateSdkOrderEvent = exports.buildRefundStatusEvent = exports.buildRefundEvent = exports.buildOrderStatusEvent = exports.buildCustomCheckoutPayRequest = exports.buildStandardCheckoutPayEvent = exports.buildInitClientEvent = void 0;
const models_1 = require("../../models");
const BaseEvent_1 = require("../models/BaseEvent");
const EventState_1 = require("../models/enums/EventState");
const FlowType_1 = require("../models/enums/FlowType");
const EventData_1 = require("../models/EventData");
const buildInitClientEvent = (eventName, paymentFlow) => {
    return BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setEventState(EventState_1.EventState.INITIATED)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .setPaymentFlow(paymentFlow)
        .build())
        .build();
};
exports.buildInitClientEvent = buildInitClientEvent;
const buildStandardCheckoutPayEvent = (eventState, eventName, standardCheckoutPayRequest, apiPath, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setMerchantOrderId(standardCheckoutPayRequest.merchantOrderId)
        .setData(EventData_1.EventData.builder()
        .setEventState(eventState)
        .setAmount(standardCheckoutPayRequest.amount)
        .setPaymentFlow(models_1.PaymentFlowType.PG_CHECKOUT)
        .setApiPath(apiPath)
        .setExpireAfter(standardCheckoutPayRequest.expireAfter)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildStandardCheckoutPayEvent = buildStandardCheckoutPayEvent;
const buildCustomCheckoutPayRequest = (eventState, eventName, request, apiPath, exception = null) => {
    const pgPaymentFlow = request.paymentFlow;
    const targetApp = pgPaymentFlow.paymentMode instanceof models_1.IntentPaymentV2Instrument
        ? pgPaymentFlow.paymentMode.targetApp
        : undefined;
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setMerchantOrderId(request.merchantOrderId)
        .setData(EventData_1.EventData.builder()
        .setEventState(eventState)
        .setAmount(request.amount)
        .setPaymentFlow(models_1.PaymentFlowType.PG)
        .setApiPath(apiPath)
        .setExpireAfter(request.expireAfter)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .setPaymentInstrument(pgPaymentFlow.paymentMode.type)
        .setTargetApp(targetApp)
        .setDeviceContext(request.deviceContext)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildCustomCheckoutPayRequest = buildCustomCheckoutPayRequest;
const buildOrderStatusEvent = (eventState, merchantOrderId, paymentFlow, apiPath, eventName, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setMerchantOrderId(merchantOrderId)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setEventState(eventState)
        .setPaymentFlow(paymentFlow)
        .setApiPath(apiPath)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildOrderStatusEvent = buildOrderStatusEvent;
const buildRefundEvent = (eventState, refundRequest, apiPath, paymentFlow, eventName, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setMerchantOrderId(refundRequest.originalMerchantOrderId)
        .setData(EventData_1.EventData.builder()
        .setMerchantRefundId(refundRequest.merchantRefundId)
        .setEventState(eventState)
        .setPaymentFlow(paymentFlow)
        .setApiPath(apiPath)
        .setAmount(refundRequest.amount)
        .setOriginalMerchantOrderId(refundRequest.originalMerchantOrderId)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildRefundEvent = buildRefundEvent;
const buildRefundStatusEvent = (eventState, eventName, refundId, apiPath, paymentFlow, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setMerchantRefundId(refundId)
        .setEventState(eventState)
        .setApiPath(apiPath)
        .setPaymentFlow(paymentFlow)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildRefundStatusEvent = buildRefundStatusEvent;
const buildCreateSdkOrderEvent = (eventState, eventName, createSdkOrderRequest, apiPath, paymentFlow, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setMerchantOrderId(createSdkOrderRequest.merchantOrderId)
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setAmount(createSdkOrderRequest.amount)
        .setApiPath(apiPath)
        .setEventState(eventState)
        .setExpireAfter(createSdkOrderRequest.expireAfter)
        .setPaymentFlow(paymentFlow)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildCreateSdkOrderEvent = buildCreateSdkOrderEvent;
const buildTransactionStatusEvent = (eventState, eventName, transactionId, apiPath, paymentFlow, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setEventState(eventState)
        .setTransactionId(transactionId)
        .setApiPath(apiPath)
        .setFlowType(FlowType_1.FlowType.B2B_PG)
        .setPaymentFlow(paymentFlow)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildTransactionStatusEvent = buildTransactionStatusEvent;
const buildOAuthEvent = (fetchAttemptTime, apiPath, eventName, exception, cachedTokenIssuedAt, cachedTokenExpiresAt) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setTokenFetchAttemptTimestamp(fetchAttemptTime)
        .setApiPath(apiPath)
        .setEventState(EventState_1.EventState.FAILED)
        .setCachedTokenExpiresAt(cachedTokenExpiresAt)
        .setCachedTokenIssuesAt(cachedTokenIssuedAt)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildOAuthEvent = buildOAuthEvent;
const buildCallbackSerializationFailedEvent = (eventState, paymentFlow, eventName, exception = null) => {
    const event = BaseEvent_1.BaseEvent.builder()
        .setEventName(eventName)
        .setEventTime(Math.floor(Date.now() / 1000))
        .setData(EventData_1.EventData.builder()
        .setEventState(eventState)
        .setPaymentFlow(paymentFlow)
        .build())
        .build();
    return (0, exports.populateExceptionFields)(event, exception);
};
exports.buildCallbackSerializationFailedEvent = buildCallbackSerializationFailedEvent;
const populateExceptionFields = (event, exception) => {
    var _a, _b, _c, _d, _e;
    if (exception == null)
        return event;
    event.data.exceptionClass = (_a = exception === null || exception === void 0 ? void 0 : exception.type) !== null && _a !== void 0 ? _a : null;
    event.data.exceptionMessage = (_b = exception === null || exception === void 0 ? void 0 : exception.message) !== null && _b !== void 0 ? _b : null;
    event.data.exceptionCode = (_c = exception === null || exception === void 0 ? void 0 : exception.code) !== null && _c !== void 0 ? _c : null;
    event.data.exceptionHttpStatusCode = (_d = exception === null || exception === void 0 ? void 0 : exception.httpStatusCode) !== null && _d !== void 0 ? _d : null;
    event.data.exceptionData = (_e = exception === null || exception === void 0 ? void 0 : exception.data) !== null && _e !== void 0 ? _e : null;
    return event;
};
exports.populateExceptionFields = populateExceptionFields;
