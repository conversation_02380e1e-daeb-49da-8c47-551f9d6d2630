{"version": 3, "file": "getCountries.test.js", "names": ["describe", "it", "expect", "getCountries", "metadata", "indexOf", "to", "be"], "sources": ["../source/getCountries.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nimport getCountries from './getCountries.js'\r\n\r\ndescribe('getCountries', () => {\r\n\tit('should get countries list', () => {\r\n\t\texpect(getCountries(metadata).indexOf('RU') > 0).to.be.true;\r\n\t})\r\n})"], "mappings": ";;AAAA;;AAEA;;;;AAEAA,QAAQ,CAAC,cAAD,EAAiB,YAAM;EAC9BC,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCC,MAAM,CAAC,IAAAC,wBAAA,EAAaC,uBAAb,EAAuBC,OAAvB,CAA+B,IAA/B,IAAuC,CAAxC,CAAN,CAAiDC,EAAjD,CAAoDC,EAApD;EACA,CAFC,CAAF;AAGA,CAJO,CAAR"}