"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardPayRequestBuilder = void 0;
const CustomCheckoutPayRequest_1 = require("../../../../payments/v2/models/request/CustomCheckoutPayRequest");
const CardPaymentV2Instrument_1 = require("./CardPaymentV2Instrument");
const Expiry_1 = require("./Expiry");
const NewCardDetails_1 = require("./NewCardDetails");
const PgPaymentFlow_1 = require("../../../../payments/v2/models/request/PgPaymentFlow");
class CardPayRequestBuilder {
    constructor() {
        /**
         * SETTERS
         */
        this.merchantOrderId = (merchantOrderId) => {
            this._merchantOrderId = merchantOrderId;
            return this;
        };
        this.amount = (amount) => {
            this._amount = amount;
            return this;
        };
        this.metaInfo = (metaInfo) => {
            this._metaInfo = metaInfo;
            return this;
        };
        this.constraints = (constraints) => {
            this._constraints = constraints;
            return this;
        };
        this.encryptedCardNumber = (encryptedCardNumber) => {
            this._encryptedCardNumber = encryptedCardNumber;
            return this;
        };
        this.authMode = (authMode) => {
            this._authMode = authMode;
            return this;
        };
        this.encryptionKeyId = (encryptionKeyId) => {
            this._encryptionKeyId = encryptionKeyId;
            return this;
        };
        this.encryptedCvv = (encryptedCvv) => {
            this._encryptedCvv = encryptedCvv;
            return this;
        };
        this.expiryMonth = (expiryMonth) => {
            this._expiryMonth = expiryMonth;
            return this;
        };
        this.expiryYear = (expiryYear) => {
            this._expiryYear = expiryYear;
            return this;
        };
        this.redirectUrl = (redirectUrl) => {
            this._redirectUrl = redirectUrl;
            return this;
        };
        this.cardHolderName = (cardHolderName) => {
            this._cardHolderName = cardHolderName;
            return this;
        };
        this.savedCard = (savedCard) => {
            this._savedCard = savedCard;
            return this;
        };
        this.merchantUserId = (merchantUserId) => {
            this._merchantUserId = merchantUserId;
            return this;
        };
        this.expireAfter = (expireAfter) => {
            this._expireAfter = expireAfter;
            return this;
        };
        this.build = () => {
            const newCardDetails = NewCardDetails_1.NewCardDetails.builder()
                .cardHolderName(this._cardHolderName)
                .expiry(Expiry_1.Expiry.builder()
                .expiryMonth(this._expiryMonth)
                .expiryYear(this._expiryYear)
                .build())
                .encryptionKeyId(this._encryptionKeyId)
                .encryptedCardNumber(this._encryptedCardNumber)
                .encryptedCvv(this._encryptedCvv)
                .build();
            const paymentFlow = PgPaymentFlow_1.PgPaymentFlow.builder()
                .paymentMode(CardPaymentV2Instrument_1.CardPaymentV2Instrument.builder()
                .cardDetails(newCardDetails)
                .authMode(this._authMode)
                .merchantUserId(this._merchantUserId)
                .savedCard(this._savedCard)
                .build())
                .redirectUrl(this._redirectUrl)
                .build();
            return new CustomCheckoutPayRequest_1.CustomCheckoutPayRequest(this._merchantOrderId, this._amount, paymentFlow, this._expireAfter, this._metaInfo, this._constraints);
        };
    }
}
exports.CardPayRequestBuilder = CardPayRequestBuilder;
