// useToggle.js

import { useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { toggleWishlist, toggleCart } from '../app/toggle/toggleSlice';

const useToggle = () => {
  const dispatch = useDispatch();
  // Local state for optimistic UI updates (for wishlist)
  const [optimistic, setOptimistic] = useState({});

  const toggle_wishlist = useCallback(async (product_id, name, color, price, brand, imageurl) => {
    // Check if product_id is defined
    if (!product_id) {
      throw new Error("Product ID is required");
    }

    const idStr = product_id.toString();

    // Get current state
    const currentState = optimistic[idStr] ?? false;

    // Immediately update the UI optimistically - use direct state update for speed
    setOptimistic({ ...optimistic, [idStr]: !currentState });

    try {
      // Make the API call in the background without waiting
      dispatch(toggleWishlist({ product_id, name, color, price, brand, imageurl })).unwrap()
        .catch(err => {
          // Revert optimistic update if API call fails
          setOptimistic({ ...optimistic, [idStr]: currentState });
          throw err;
        });
      return Promise.resolve(); // Return immediately for perceived speed
    } catch (err) {
      // Handle synchronous errors (rare)
      setOptimistic({ ...optimistic, [idStr]: currentState });
      throw err;
    }
  }, [dispatch, optimistic]);

  const toggleCartFunc = async (product) => {
    // Extract product details, handling different property naming conventions
    const product_id = product.product_id || product.Product_id;
    const name = product.name || product.username;
    const color = product.color || product.Color;
    const price = product.price || product.Price;
    const brand = product.brand || product.Brand;
    const imageurl = product.imageurl || product.Imageurl;

    console.log("useToggle - toggleCartFunc called with:", {
      product_id,
      name,
      color,
      price,
      brand,
      imageurl,
      original_product: product
    });

    if (!product_id) {
      console.error("useToggle - Missing product_id:", product);
      throw new Error("Product ID is required");
    }

    try {
      const cartData = {
        product_id: product_id,
        name: name || 'Unknown Product',
        color: color || 'Default',
        price: price || 0,
        brand: brand || 'Unknown Brand',
        imageurl: imageurl || ''
      };

      console.log("useToggle - Sending cart data:", cartData);

      const result = await dispatch(toggleCart(cartData)).unwrap();
      console.log("useToggle - Cart toggle successful:", result);
      return result;
    } catch (err) {
      console.error("Error in toggleCartFunc:", err);
      throw new Error(err.message || "Failed to toggle cart");
    }
  };

  return { toggleWishlist: toggle_wishlist, toggle_wishlist, toggleCart: toggleCartFunc, optimistic };
};

export default useToggle;
