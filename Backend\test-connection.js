// test-connection.js
// Simple script to test if the backend server is running and accessible

import axios from 'axios';
import { config } from 'dotenv';

// Load environment variables
config();

const PORT = process.env.PORT || 3000;
const BASE_URL = `http://localhost:${PORT}`;

async function testConnection() {
  console.log(`Testing backend connection at ${BASE_URL}`);
  console.log('='.repeat(50));

  try {
    // Test 1: Basic server health check
    console.log('1. Testing basic server connectivity...');
    const response = await axios.get(`${BASE_URL}/v1/products/counter`, {
      timeout: 5000
    });
    console.log('✅ Server is running and accessible');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Connection refused - Server is not running');
      console.log('   Please start the backend server with: npm start');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('❌ Connection timeout - Server might be starting up');
      console.log('   Please wait a moment and try again');
    } else {
      console.log('❌ Connection error:', error.message);
    }
    return false;
  }

  try {
    // Test 2: CORS check
    console.log('\n2. Testing CORS configuration...');
    const corsResponse = await axios.options(`${BASE_URL}/v1/products/counter`, {
      headers: {
        'Origin': 'http://localhost:5173',
        'Access-Control-Request-Method': 'GET'
      },
      timeout: 5000
    });
    console.log('✅ CORS is properly configured');
    console.log(`   Status: ${corsResponse.status}`);
  } catch (error) {
    console.log('⚠️  CORS test failed:', error.message);
  }

  console.log('\n3. Environment configuration:');
  console.log(`   PORT: ${PORT}`);
  console.log(`   BASE_URL: ${BASE_URL}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   FRONTEND_URL: ${process.env.FRONTEND_URL}`);

  return true;
}

// Run the test
testConnection()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Backend connectivity test passed!');
      process.exit(0);
    } else {
      console.log('\n💥 Backend connectivity test failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Test script error:', error);
    process.exit(1);
  });
