"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpCommand = void 0;
const HttpMethodType_1 = require("./HttpMethodType");
const axios_1 = __importDefault(require("axios"));
const Exceptions_1 = require("../exception/Exceptions");
const HTTP_CODE_TO_EXCEPTION_MAPPER = {
    400: Exceptions_1.BadRequest,
    401: Exceptions_1.UnauthorizedAccess,
    403: Exceptions_1.ForbiddenAccess,
    404: Exceptions_1.ResourceNotFound,
    409: Exceptions_1.ResourceConflict,
    410: Exceptions_1.ResourceGone,
    417: Exceptions_1.ExpectationFailed,
    422: Exceptions_1.ResourceInvalid,
    429: Exceptions_1.TooManyRequests,
};
class HttpCommand {
    constructor(hostUrl, httpClient) {
        this.getCompleteUrl = (url, params) => {
            const urlFormat = new URL(this.hostUrl + url);
            urlFormat.search = new URLSearchParams(params).toString();
            return urlFormat.toString();
        };
        this.request = (url, method, headers = {}, data = {}, params = {}) => __awaiter(this, void 0, void 0, function* () {
            try {
                const completeUrl = this.getCompleteUrl(url, params);
                const response = yield this.makeCall(completeUrl, method, headers, data, params);
                return response.data;
            }
            catch (error) {
                if (axios_1.default.isAxiosError(error) && error.response) {
                    return this.handleError(error.response);
                }
                throw error;
            }
        });
        this.makeCall = (url, method, headers = {}, data = {}, params = {}) => {
            if (HttpMethodType_1.HttpMethodType.GET == method) {
                return this.httpClient.get(url, { params, headers });
            }
            else if (HttpMethodType_1.HttpMethodType.POST == method) {
                return this.httpClient.post(url, data, { headers });
            }
            else {
                throw new Exceptions_1.PhonePeException('Method Not Supported', 405);
            }
        };
        this.handleError = (error) => {
            const responseCode = error.status;
            if (responseCode in HTTP_CODE_TO_EXCEPTION_MAPPER) {
                throw new HTTP_CODE_TO_EXCEPTION_MAPPER[error.status](error.statusText, error.status, error);
            }
            else if (responseCode >= 400 && responseCode <= 499) {
                throw new Exceptions_1.ClientError(error.statusText, error.status, error);
            }
            else if (responseCode >= 500 && responseCode <= 599) {
                throw new Exceptions_1.ServerError(error.statusText, error.status, error);
            }
            throw new Exceptions_1.PhonePeException(error.statusText, error.status, error);
        };
        this.hostUrl = hostUrl;
        this.httpClient = httpClient;
    }
}
exports.HttpCommand = HttpCommand;
