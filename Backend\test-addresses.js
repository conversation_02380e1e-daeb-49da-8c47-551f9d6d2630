// test-addresses.js
// Simple script to test address endpoints

import axios from 'axios';
import { config } from 'dotenv';

// Load environment variables
config();

const PORT = process.env.PORT || 3000;
const BASE_URL = `http://localhost:${PORT}`;

async function testAddresses() {
  console.log(`Testing address endpoints at ${BASE_URL}`);
  console.log('='.repeat(60));

  try {
    // Test 1: Get addresses without authentication (should fail)
    console.log('1. Testing GET addresses without authentication...');
    try {
      await axios.get(`${BASE_URL}/v1/addresses`, { timeout: 5000 });
      console.log('❌ Addresses endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Addresses endpoint correctly requires authentication');
      } else {
        console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 2: Add address without authentication (should fail)
    console.log('\n2. Testing POST address without authentication...');
    try {
      await axios.post(`${BASE_URL}/v1/addresses`, {
        street: "123 Test Street",
        city: "Test City",
        district: "Test District",
        state: "Test State",
        country: "India",
        pincode: "123456"
      }, { timeout: 5000 });
      console.log('❌ Add address endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Add address endpoint correctly requires authentication');
      } else {
        console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 3: Update address without authentication (should fail)
    console.log('\n3. Testing PUT address without authentication...');
    try {
      await axios.put(`${BASE_URL}/v1/addresses`, {
        addressId: "test123",
        street: "456 Updated Street",
        city: "Updated City",
        district: "Updated District",
        state: "Updated State",
        country: "India",
        pincode: "654321"
      }, { timeout: 5000 });
      console.log('❌ Update address endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Update address endpoint correctly requires authentication');
      } else {
        console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 4: Delete address without authentication (should fail)
    console.log('\n4. Testing DELETE address without authentication...');
    try {
      await axios.delete(`${BASE_URL}/v1/addresses/test123`, { timeout: 5000 });
      console.log('❌ Delete address endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Delete address endpoint correctly requires authentication');
      } else {
        console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    // Test 5: Set primary address without authentication (should fail)
    console.log('\n5. Testing POST primary address without authentication...');
    try {
      await axios.post(`${BASE_URL}/v1/addresses/primary`, {
        addressId: "test123"
      }, { timeout: 5000 });
      console.log('❌ Set primary address endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Set primary address endpoint correctly requires authentication');
      } else {
        console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.message}`);
      }
    }

    console.log('\n6. Environment configuration:');
    console.log(`   PORT: ${PORT}`);
    console.log(`   BASE_URL: ${BASE_URL}`);
    console.log(`   JWT_SECRET_KEY: ${process.env.JWT_SECRET_KEY ? 'Set' : 'Not set'}`);

  } catch (error) {
    console.error('\n💥 Test script error:', error);
    return false;
  }

  return true;
}

// Run the test
testAddresses()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Address endpoints test completed!');
      console.log('\nNote: These tests verify that address endpoints require authentication.');
      console.log('To test with valid authentication, use a real JWT token in HTTP-only cookies.');
      process.exit(0);
    } else {
      console.log('\n💥 Address endpoints test failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Test script error:', error);
    process.exit(1);
  });
