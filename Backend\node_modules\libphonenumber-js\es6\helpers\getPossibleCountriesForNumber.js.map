{"version": 3, "file": "getPossibleCountriesForNumber.js", "names": ["<PERSON><PERSON><PERSON>", "getPossibleCountriesForNumber", "callingCode", "nationalNumber", "metadata", "_metadata", "possibleCountries", "getCountryCodesForCallingCode", "filter", "country", "couldNationalNumberBelongToCountry", "selectNumberingPlan", "numberingPlan", "possibleLengths", "indexOf", "length"], "sources": ["../../source/helpers/getPossibleCountriesForNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\r\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\tlet possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn []\r\n\t}\r\n\treturn possibleCountries.filter((country) => {\r\n\t\treturn couldNationalNumberBelongToCountry(nationalNumber, country, metadata)\r\n\t})\r\n}\r\n\r\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\t_metadata.selectNumberingPlan(country)\r\n\tif (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\r\n\t\treturn true\r\n\t}\r\n\treturn false\r\n}"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,6BAAT,CAAuCC,WAAvC,EAAoDC,cAApD,EAAoEC,QAApE,EAA8E;EAC5F,IAAMC,SAAS,GAAG,IAAIL,QAAJ,CAAaI,QAAb,CAAlB;;EACA,IAAIE,iBAAiB,GAAGD,SAAS,CAACE,6BAAV,CAAwCL,WAAxC,CAAxB;;EACA,IAAI,CAACI,iBAAL,EAAwB;IACvB,OAAO,EAAP;EACA;;EACD,OAAOA,iBAAiB,CAACE,MAAlB,CAAyB,UAACC,OAAD,EAAa;IAC5C,OAAOC,kCAAkC,CAACP,cAAD,EAAiBM,OAAjB,EAA0BL,QAA1B,CAAzC;EACA,CAFM,CAAP;AAGA;;AAED,SAASM,kCAAT,CAA4CP,cAA5C,EAA4DM,OAA5D,EAAqEL,QAArE,EAA+E;EAC9E,IAAMC,SAAS,GAAG,IAAIL,QAAJ,CAAaI,QAAb,CAAlB;;EACAC,SAAS,CAACM,mBAAV,CAA8BF,OAA9B;;EACA,IAAIJ,SAAS,CAACO,aAAV,CAAwBC,eAAxB,GAA0CC,OAA1C,CAAkDX,cAAc,CAACY,MAAjE,KAA4E,CAAhF,EAAmF;IAClF,OAAO,IAAP;EACA;;EACD,OAAO,KAAP;AACA"}