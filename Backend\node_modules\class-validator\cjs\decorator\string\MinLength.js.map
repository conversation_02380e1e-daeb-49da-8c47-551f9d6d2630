{"version": 3, "file": "MinLength.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/MinLength.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAc,EAAE,GAAW;IACnD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;AACxE,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,GAAW,EAAE,iBAAqC;IAC1E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,mEAAmE,EAC9F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,8BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLengthValidator from 'validator/lib/isLength';\n\nexport const MIN_LENGTH = 'minLength';\n\n/**\n * Checks if the string's length is not less than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function minLength(value: unknown, min: number): boolean {\n  return typeof value === 'string' && isLengthValidator(value, { min });\n}\n\n/**\n * Checks if the string's length is not less than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function MinLength(min: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MIN_LENGTH,\n      constraints: [min],\n      validator: {\n        validate: (value, args): boolean => minLength(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be longer than or equal to $constraint1 characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}