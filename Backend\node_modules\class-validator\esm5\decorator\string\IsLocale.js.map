{"version": 3, "file": "IsLocale.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsLocale.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YACnD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,0BAA0B,EAAvC,CAAuC,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLocaleValidator from 'validator/lib/isLocale';\n\nexport const IS_LOCALE = 'isLocale';\n\n/**\n * Check if the string is a locale.\n * If given value is not a string, then it returns false.\n */\nexport function isLocale(value: unknown): boolean {\n  return typeof value === 'string' && isLocaleValidator(value);\n}\n\n/**\n * Check if the string is a locale.\n * If given value is not a string, then it returns false.\n */\nexport function IsLocale(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LOCALE,\n      validator: {\n        validate: (value, args): boolean => isLocale(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be locale', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}