// services/codPurchase.js

import { addData } from "../utils/access_crud.js";
import { config } from "dotenv";

// Load environment variables
config();

export const processCodPurchase = async ({ req, purchaseData, phone, price, transaction_id }) => {
  // Rename transaction_id to transactionId for consistency with the payment service
  const transactionId = transaction_id;

  if (!phone) {
    throw new Error("Please update your phone number in your profile to proceed with online payment");
  }

  if (!price) {
    throw new Error("Price is missing. Please try again or contact support.");
  }

  if (!transactionId) {
    throw new Error("Transaction ID is missing. Please try again or contact support.");
  }

  const PurchasesCod = req.app.locals.PurchasesCod;
  const queryParams = new URLSearchParams({ transaction_id, amount: price, payment_method: "COD" }).toString();

  const paymentRecord = await addData(PurchasesCod, [purchaseData]);
  if (paymentRecord) {
    // Use environment variable for frontend URL
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    return {
      message: "Purchase initiated for COD!",
      url: `${frontendUrl}/purchase/status?success=true&${queryParams}`
    };
  } else {
    throw new Error("COD purchase initiation failed.");
  }
};
