"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventType = void 0;
var EventType;
(function (EventType) {
    EventType["PAY_SUCCESS"] = "PAY_SUCCESS";
    EventType["PAY_FAILED"] = "PAY_FAILED";
    EventType["REFUND_SUCCESS"] = "REFUND_SUCCESS";
    EventType["REFUND_FAILED"] = "REFUND_FAILED";
    EventType["REFUND_STATUS_SUCCESS"] = "REFUND_STATUS_SUCCESS";
    EventType["REFUND_STATUS_FAILED"] = "REFUND_STATUS_FAILED";
    EventType["ORDER_STATUS_SUCCESS"] = "ORDER_STATUS_SUCCESS";
    EventType["ORDER_STATUS_FAILED"] = "ORDER_STATUS_FAILED";
    EventType["TRANSACTION_STATUS_SUCCESS"] = "TRANSACTION_STATUS_SUCCESS";
    EventType["TRANSACTION_STATUS_FAILED"] = "TRANSACTION_STATUS_FAILED";
    EventType["CREATE_SDK_ORDER_SUCCESS"] = "CREATE_SDK_ORDER_SUCCESS";
    EventType["CREATE_SDK_ORDER_FAILED"] = "CREATE_SDK_ORDER_FAILED";
    EventType["STANDARD_CHECKOUT_CLIENT_INITIALIZED"] = "STANDARD_CHECKOUT_CLIENT_INITIALIZED";
    EventType["CUSTOM_CHECKOUT_CLIENT_INITIALIZED"] = "CUSTOM_CHECKOUT_CLIENT_INITIALIZED";
    EventType["TOKEN_SERVICE_INITIALIZED"] = "TOKEN_SERVICE_INITIALIZED";
    EventType["OAUTH_FETCH_FAILED_USED_CACHED_TOKEN"] = "OAUTH_FETCH_FAILED_USED_CACHED_TOKEN";
    EventType["CALLBACK_SERIALIZATION_FAILED"] = "CALLBACK_SERIALIZATION_FAILED";
})(EventType = exports.EventType || (exports.EventType = {}));
