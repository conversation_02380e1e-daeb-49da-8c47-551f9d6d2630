{"version": 3, "file": "getCountries.js", "names": ["getCountries", "metadata", "<PERSON><PERSON><PERSON>"], "sources": ["../source/getCountries.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\n\r\nexport default function getCountries(metadata) {\r\n\treturn new Metadata(metadata).getCountries()\r\n}"], "mappings": ";;;;;;;AAAA;;;;AAEe,SAASA,YAAT,CAAsBC,QAAtB,EAAgC;EAC9C,OAAO,IAAIC,oBAAJ,CAAaD,QAAb,EAAuBD,YAAvB,EAAP;AACA"}