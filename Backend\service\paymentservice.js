// Access_payment.js

import axios from 'axios';
import crypto from 'crypto';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Configuration should be loaded from environment variables
// No default values for sensitive credentials
const config = {
  BASE_URL: process.env.BASE_URL || "http://localhost:3000",
  API_STATUS: process.env.API_STATUS || "UAT",
  MERCHANT_ID_LIVE: process.env.MERCHANT_ID_LIVE,
  MERCHANT_ID_UAT: process.env.MERCHANT_ID_UAT,
  SALT_KEY_LIVE: process.env.SALT_KEY_LIVE,
  SALT_KEY_UAT: process.env.SALT_KEY_UAT,
  SALT_INDEX: process.env.SALT_INDEX,
  REDIRECTURL: process.env.REDIRECTURL || "/v1/redirect/purchases/status",
  UAT_URL_PAY: process.env.UAT_URL_PAY || "https://api-preprod.phonepe.com/apis/hermes/pg/v1/pay",
  LIVE_URL_PAY: process.env.LIVE_URL_PAY || "https://api.phonepe.com/apis/hermes/pg/v1/pay"
};

// Validate required configuration
function validateConfig() {
  const isLive = config.API_STATUS === "LIVE";
  const requiredKeys = [
    isLive ? 'MERCHANT_ID_LIVE' : 'MERCHANT_ID_UAT',
    isLive ? 'SALT_KEY_LIVE' : 'SALT_KEY_UAT',
    'SALT_INDEX'
  ];

  const missingKeys = requiredKeys.filter(key => !config[key]);

  if (missingKeys.length > 0) {
    throw new Error(`Missing required environment variables: ${missingKeys.join(', ')}`);
  }
}

/*
 * Initiates a PhonePe payment.
 *
 * @param {Object} params
 * @param {string} params.phone
 * @param {number|string} params.price
 * @param {string} params.transactionId
 * @returns {Promise<Object>}
 * @throws {Error}
 */
export async function initiatePayment({ phone, price, transactionId }) {
  if (!phone) {
    throw new Error("Please update your phone number in your profile to proceed with online payment");
  }
  if (!price || !transactionId) {
    throw new Error("Missing required parameters: price or transactionId");
  }

  // Validate configuration before proceeding
  validateConfig();

  const isLive = config.API_STATUS === "LIVE";
  const merchantId = isLive ? config.MERCHANT_ID_LIVE : config.MERCHANT_ID_UAT;
  const saltKey = isLive ? config.SALT_KEY_LIVE : config.SALT_KEY_UAT;
  const saltIndex = config.SALT_INDEX;
  const url = isLive ? config.LIVE_URL_PAY : config.UAT_URL_PAY;

  const payload = {
    merchantId,
    merchantTransactionId: transactionId,
    merchantUserId: "M-" + Date.now(),
    amount: Number(price) * 100,
    redirectUrl: config.BASE_URL + config.REDIRECTURL,
    redirectMode: "POST",
    callbackUrl: config.BASE_URL + config.REDIRECTURL,
    mobileNumber: phone,
    paymentInstrument: { type: "PAY_PAGE" }
  };

  const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString('base64');
  const dataToHash = payloadBase64 + "/pg/v1/pay" + saltKey;
  const sha256Hash = crypto.createHash('sha256').update(dataToHash).digest('hex');
  const checksum = `${sha256Hash}###${saltIndex}`;

  try {
    console.log("PhonePe Payment Request Details:", {
      url,
      payload: JSON.stringify(payload, null, 2),
      payloadBase64,
      checksum,
      headers: {
        "Content-Type": "application/json",
        "X-VERIFY": checksum,
        "accept": "application/json"
      }
    });

    const response = await axios.post(
      url,
      { request: payloadBase64 },
      {
        headers: {
          "Content-Type": "application/json",
          "X-VERIFY": checksum,
          "accept": "application/json"
        },
        timeout: 30000
      }
    );

    console.log("PhonePe Payment Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("PhonePe Payment Error Details:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers
      }
    });

    // Provide more specific error messages based on status codes
    if (error.response?.status === 526) {
      throw new Error("PhonePe API configuration error. Please check merchant credentials and API endpoints.");
    } else if (error.response?.status === 400) {
      throw new Error("Invalid payment request. Please check the payment parameters.");
    } else if (error.response?.status === 401) {
      throw new Error("Authentication failed. Please check merchant ID and salt key.");
    } else if (error.response?.status === 500) {
      throw new Error("PhonePe server error. Please try again later.");
    }

    throw new Error("Error initiating PhonePe payment: " + error.message);
  }
}

/*
 * Verifies the status of a PhonePe payment.
 *
 * @param {Object} params
 * @param {string} params.transactionId
 * @returns {Promise<Object>}
 * @throws {Error}
 */
export async function verifyPayment({ transactionId }) {
  if (!transactionId) {
    throw new Error("Missing required parameter: transactionId");
  }

  // Validate configuration before proceeding
  validateConfig();

  const isLive = config.API_STATUS === "LIVE";
  const merchantId = isLive ? config.MERCHANT_ID_LIVE : config.MERCHANT_ID_UAT;
  const saltKey = isLive ? config.SALT_KEY_LIVE : config.SALT_KEY_UAT;
  const saltIndex = config.SALT_INDEX;

  // Assumed verify endpoint URLs
  const verifyUrl = isLive
    ? "https://api.phonepe.com/apis/hermes/pg/v1/status"
    : "https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/status";

  const payload = {
    merchantId,
    merchantTransactionId: transactionId
  };

  const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString("base64");
  // Here the path is assumed as '/pg/v1/status'
  const dataToHash = payloadBase64 + "/pg/v1/status" + saltKey;
  const sha256Hash = crypto.createHash("sha256").update(dataToHash).digest("hex");
  const checksum = `${sha256Hash}###${saltIndex}`;

  try {
    const response = await axios.post(
      verifyUrl,
      { request: payloadBase64 },
      {
        headers: {
          "Content-Type": "application/json",
          "X-VERIFY": checksum,
          "accept": "application/json"
        },
        timeout: 30000
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error verifying PhonePe payment:", error.message);
    throw new Error("Error verifying PhonePe payment: " + error.message);
  }
}
