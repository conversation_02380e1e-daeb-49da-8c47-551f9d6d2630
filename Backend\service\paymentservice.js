/**
 * Payment Service for PhonePe Integration
 *
 * Uses Official PhonePe Node.js SDK for payment processing
 * Supports both SANDBOX and PRODUCTION environments
 */

import dotenv from 'dotenv';
import { createRequire } from 'module';

// Load environment variables
dotenv.config();

// Create require function for CommonJS modules
const require = createRequire(import.meta.url);

// Import Official PhonePe SDK using CommonJS require
let StandardCheckoutClient = null;
let StandardCheckoutPayRequest = null;
let Env = null;
let phonePeClient = null;

try {
  // Import PhonePe SDK components
  const {
    StandardCheckoutClient: Client,
    StandardCheckoutPayRequest: PayRequest,
    Env: Environment
  } = require('phonepe-pg-sdk-node');

  StandardCheckoutClient = Client;
  StandardCheckoutPayRequest = PayRequest;
  Env = Environment;

  console.log('PhonePe SDK imported successfully');
} catch (error) {
  console.error('PhonePe SDK not available:', error.message);
  throw new Error('PhonePe SDK is required for payment processing. Please install it using: npm install https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-node/releases/v2/phonepe-pg-sdk-node.tgz');
}

// PhonePe SDK Configuration
const PHONEPE_CONFIG = {
  CLIENT_ID: process.env.PHONEPE_CLIENT_ID,
  CLIENT_SECRET: process.env.PHONEPE_CLIENT_SECRET,
  CLIENT_VERSION: process.env.PHONEPE_CLIENT_VERSION ? parseInt(process.env.PHONEPE_CLIENT_VERSION) : undefined,
  ENV: process.env.PHONEPE_ENV === "PRODUCTION" ? Env.PRODUCTION : Env.SANDBOX,
  BASE_URL: process.env.BASE_URL,
  REDIRECT_URL: process.env.REDIRECTURL
};

// Initialize PhonePe SDK Client
try {
  phonePeClient = StandardCheckoutClient.getInstance(
    PHONEPE_CONFIG.CLIENT_ID,
    PHONEPE_CONFIG.CLIENT_SECRET,
    PHONEPE_CONFIG.CLIENT_VERSION,
    PHONEPE_CONFIG.ENV
  );

  console.log('PhonePe SDK client initialized successfully', {
    clientId: PHONEPE_CONFIG.CLIENT_ID,
    environment: process.env.PHONEPE_ENV
  });
} catch (error) {
  console.error('Failed to initialize PhonePe SDK client:', error);
  throw new Error(`PhonePe SDK initialization failed: ${error.message}`);
}

/**
 * Validate required PhonePe SDK configuration
 */
function validateConfig() {
  if (!phonePeClient) {
    throw new Error('PhonePe SDK client is not initialized');
  }

  const requiredKeys = ['CLIENT_ID', 'CLIENT_SECRET', 'CLIENT_VERSION', 'BASE_URL'];
  const missingKeys = requiredKeys.filter(key => !PHONEPE_CONFIG[key]);

  if (missingKeys.length > 0) {
    const envVarNames = missingKeys.map(key => {
      switch(key) {
        case 'CLIENT_ID': return 'PHONEPE_CLIENT_ID';
        case 'CLIENT_SECRET': return 'PHONEPE_CLIENT_SECRET';
        case 'CLIENT_VERSION': return 'PHONEPE_CLIENT_VERSION';
        case 'BASE_URL': return 'BASE_URL';
        default: return key;
      }
    });
    throw new Error(`Missing required environment variables: ${envVarNames.join(', ')}. Please check your .env file.`);
  }
}

/**
 * Initiates a PhonePe payment using Official SDK
 *
 * @param {Object} params
 * @param {string} params.phone - Customer phone number
 * @param {number|string} params.price - Payment amount in rupees
 * @param {string} params.transactionId - Unique transaction identifier
 * @returns {Promise<Object>} Payment initiation response
 * @throws {Error} If validation fails or payment initiation fails
 */
export async function initiatePayment({ phone, price, transactionId }) {
  // Input validation
  if (!phone) {
    throw new Error("Please update your phone number in your profile to proceed with online payment");
  }
  if (!price || !transactionId) {
    throw new Error("Missing required parameters: price or transactionId");
  }

  // Validate configuration
  validateConfig();

  try {
    console.log('Initiating PhonePe payment', { transactionId, amount: price });

    // Create payment request using SDK builder pattern
    const request = StandardCheckoutPayRequest.builder()
      .merchantOrderId(transactionId)
      .amount(Number(price) * 100) // Convert rupees to paise
      .redirectUrl(PHONEPE_CONFIG.BASE_URL + PHONEPE_CONFIG.REDIRECT_URL)
      .build();

    // Initiate payment using SDK
    const response = await phonePeClient.pay(request);

    console.log('PhonePe payment initiated successfully', {
      transactionId,
      success: response.success,
      redirectUrl: response.redirectUrl
    });

    return response;
  } catch (error) {
    console.error('Error initiating PhonePe payment:', error);

    // Log more details about the error for debugging
    if (error.httpStatusCode) {
      console.error('HTTP Status Code:', error.httpStatusCode);
    }
    if (error.type) {
      console.error('Error Type:', error.type);
    }
    if (error.code) {
      console.error('Error Code:', error.code);
    }

    throw new Error(`Payment initiation failed: ${error.message || 'Unknown error'}`);
  }
}

/**
 * Verifies the status of a PhonePe payment using Official SDK
 *
 * @param {Object} params
 * @param {string} params.transactionId - Transaction ID to verify
 * @returns {Promise<Object>} Payment verification response
 * @throws {Error} If validation fails or verification fails
 */
export async function verifyPayment({ transactionId }) {
  if (!transactionId) {
    throw new Error("Missing required parameter: transactionId");
  }

  // Validate configuration
  validateConfig();

  try {
    console.log('Verifying PhonePe payment', { transactionId });

    // Use SDK to check payment status
    const response = await phonePeClient.getOrderStatus(transactionId);

    console.log('PhonePe payment verification completed', {
      transactionId,
      success: response.success,
      state: response.state
    });

    return response;
  } catch (error) {
    console.error('Error verifying PhonePe payment:', error);
    throw new Error(`Payment verification failed: ${error.message}`);
  }
}

/**
 * Verify callback from PhonePe using Official SDK
 *
 * @param {Object} params
 * @param {string} params.username - Merchant username
 * @param {string} params.password - Merchant password
 * @param {string} params.authorization - Authorization header from callback
 * @param {string} params.responseBody - Response body from callback
 * @returns {Object} Validated callback response
 * @throws {Error} If callback validation fails
 */
export function verifyCallback({ username, password, authorization, responseBody }) {
  try {
    validateConfig();

    // Use SDK to validate callback
    const callbackResponse = phonePeClient.validateCallback(
      username,
      password,
      authorization,
      responseBody
    );

    console.log('PhonePe callback validated successfully', {
      orderId: callbackResponse.payload?.orderId,
      state: callbackResponse.payload?.state
    });

    return callbackResponse;
  } catch (error) {
    console.error('Error verifying PhonePe callback:', error);
    throw new Error(`Callback verification failed: ${error.message}`);
  }
}

/**
 * Get SDK status and configuration info
 *
 * @returns {Object} SDK status information
 */
export function getSDKStatus() {
  return {
    sdkAvailable: !!StandardCheckoutClient,
    clientInitialized: !!phonePeClient,
    environment: process.env.PHONEPE_ENV,
    clientId: PHONEPE_CONFIG.CLIENT_ID,
    fallbackMode: false // No fallback mode in clean implementation
  };
}

/**
 * Legacy function for backward compatibility
 * Checks payment status using transaction ID
 *
 * @param {string} transactionId - Transaction ID to check
 * @returns {Promise<Object>} Payment status response
 */
export async function checkPaymentStatus(transactionId) {
  return await verifyPayment({ transactionId });
}

/**
 * Get payment configuration for debugging
 *
 * @returns {Object} Sanitized configuration object
 */
export function getPaymentConfig() {
  return {
    environment: process.env.PHONEPE_ENV,
    sdkStatus: getSDKStatus(),
    baseUrl: PHONEPE_CONFIG.BASE_URL,
    redirectUrl: PHONEPE_CONFIG.REDIRECT_URL,
    // Don't expose sensitive data
    clientId: PHONEPE_CONFIG.CLIENT_ID,
    clientSecretMasked: PHONEPE_CONFIG.CLIENT_SECRET ? "***MASKED***" : undefined
  };
}

/**
 * Health check for payment service
 *
 * @returns {Promise<Object>} Health status
 */
export async function healthCheck() {
  const sdkStatus = getSDKStatus();

  return {
    status: "healthy",
    timestamp: new Date().toISOString(),
    sdk: sdkStatus,
    configuration: {
      environment: process.env.PHONEPE_ENV,
      baseUrl: PHONEPE_CONFIG.BASE_URL,
      clientId: PHONEPE_CONFIG.CLIENT_ID
    }
  };
}
