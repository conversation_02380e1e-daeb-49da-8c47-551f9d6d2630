// server.js

import express from "express";
import { config } from "dotenv";
import cors from "cors";
import compression from "compression";
import cookieParser from "cookie-parser";
import helmet from "helmet";
import path from "path";
import crypto from "crypto";
import { fileURLToPath } from "url";

import { validateSecurityConfig } from "./utils/securityconfig.js";
import { connectDB1, connectDB2, connectDB3 } from "./utils/access_db.js";

import { configureHelmet, securityHeaders } from "./middleware/securityheaders.js";
import { csrfProtection, attachCsrfToken } from "./middleware/csrfprotection.js";
import { createRateLimiter, authRateLimiter, apiRateLimiter, heavyOperationLimiter } from "./middleware/ratelimiter.js";
import { authenticateToken, optionalAuth } from "./middleware/authenticate.js";
import { mongoSanitization, xssProtection, preventParameterPollution, sanitizeRequestData, secureJsonMiddleware } from "./middleware/sanitization.js";
// import { httpsEnforcer, httpsWarning, configureTLS } from "./middleware/httpsenforcer.js";
import { errorHandler, notFoundHandler, logger, accessLogger, requestIdMiddleware, accessLogMiddleware, setupErrorHandling } from "./middleware/errorhandler.js";
import { handleFileUploadErrors } from "./middleware/fileuploadsecurity.js";
import { cacheMiddleware, productCacheMiddleware, userCacheMiddleware, searchCacheMiddleware } from "./middleware/cachemiddleware.js";
import { initRedisClient, closeRedisConnection } from "./utils/redisclient.js";

// Import route files
import public_Auth from "./routes/public_auth.js";
import public_Status from "./routes/public_status.js";
import public_Search from "./routes/public_search.js";
import public_Product from "./routes/public_product.js";
import private_Profile from "./routes/private_profile.js";
import private_Product from "./routes/private_product.js";
import private_Orders from "./routes/private_orders.js";
import private_Purchase from "./routes/private_purchase.js";
import private_Address from "./routes/private_address.js";
import private_Assistant from "./routes/private_assistant.js";
import private_History from "./routes/private_history.js";
import { createMCPServer, mcpAuthMiddleware } from "./mcp/mcpserver.js";
import admin_Product from "./routes/admin_product.js";

// Import DB1 model factory functions
import { createCustomersModel } from "./models/user/customers.js";
import { createMetadataModel } from "./models/user/metadata.js";
import { createProductsModel } from "./models/user/products.js";
import { createWatchhistoryModel } from "./models/user/watchhistory.js";
import { createSearchHistoryModel } from "./models/user/searchhistory.js";

// Import DB2 model factory functions
import { createTopwearModel } from "./models/product/topwear.js";
import { createBottomwearModel } from "./models/product/bottomwear.js";

// Import DB3 model factory functions
import { createPurchasesCodModel } from "./models/purchase/purchasescod.js";
import { createPurchasesOnlineModel } from "./models/purchase/purchasesonline.js";

// Load environment variables
config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Base path for API routes
// When behind Nginx, this should match the location block in nginx.conf
// e.g., if Nginx has 'location /api/' that proxies to this server,
// then BASE_PATH should be '/api'
// We're using a single environment approach optimized for production behind Nginx
const BASE_PATH = process.env.API_BASE_PATH || '';

// Validate security configuration on startup
validateSecurityConfig();

// Get current file directory (ESM equivalent of __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Logs directory is now handled by the errorHandler module

// Trust proxy when behind Nginx
// We're using a single environment approach optimized for production behind Nginx
// Always trust the first proxy (Nginx)
app.set('trust proxy', 1);

app.use(requestIdMiddleware);
app.use(accessLogMiddleware);
// app.use(httpsEnforcer());
// app.use(httpsWarning());
app.use(compression());

// Body parsing with security limits
app.use(express.json({
  limit: '10kb',
  verify: (req, res, buf) => {
    // Store raw body for CSRF validation and other security checks
    req.rawBody = buf.toString();
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use(cookieParser(process.env.COOKIE_SECRET || crypto.randomBytes(32).toString('hex')));

// Security middleware with custom configuration for social login compatibility
const helmetConfig = {
  // Override Cross-Origin-Opener-Policy to allow Google Sign-In
  crossOriginOpenerPolicy: {
    policy: 'unsafe-none' // Required for Google Sign-In popups
  },
  // Allow cross-origin resources
  crossOriginResourcePolicy: {
    policy: 'cross-origin'
  },
  // Customize Content-Security-Policy
  contentSecurityPolicy: {
    directives: {
      // Default policy
      defaultSrc: ["'self'"],
      // Allow scripts from Google and Facebook
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Needed for some frameworks
        "'unsafe-eval'", // Needed for some frameworks
        "https://accounts.google.com",
        "https://*.googleusercontent.com",
        "https://apis.google.com",
        "https://www.gstatic.com",
        "https://connect.facebook.net",
        "https://challenges.cloudflare.com"
      ],
      // Allow connections to authentication providers
      connectSrc: [
        "'self'",
        "https://accounts.google.com",
        "https://*.google.com",
        "https://www.googleapis.com",
        "https://*.facebook.com",
        "https://challenges.cloudflare.com"
      ],
      // Allow frames from authentication providers
      frameSrc: [
        "'self'",
        "https://accounts.google.com",
        "https://www.facebook.com",
        "https://challenges.cloudflare.com"
      ],
      // Allow images from authentication providers
      imgSrc: [
        "'self'",
        "data:",
        "https://*.googleusercontent.com",
        "https://*.google.com",
        "https://*.fbcdn.net",
        "https://*.facebook.com"
      ],
      // Allow styles from authentication providers
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // Needed for some frameworks
        "https://accounts.google.com",
        "https://fonts.googleapis.com"
      ],
      // Allow fonts from Google
      fontSrc: [
        "'self'",
        "data:",
        "https://fonts.gstatic.com"
      ]
    }
  }
};

// Apply security middleware
app.use(configureHelmet(helmetConfig));
app.use(securityHeaders());
app.use(mongoSanitization());
app.use(xssProtection());
app.use(preventParameterPollution());
app.use(sanitizeRequestData());
app.use(secureJsonMiddleware()); // Protect against prototype pollution in JSON

app.use(cors({
  origin: (origin, callback) => {
    const allowedOrigins = [process.env.FRONTEND_URL, 'https://localhost'];
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-CSRF-Token", "X-Request-ID"],
  exposedHeaders: ["X-New-Token", "X-Request-ID"], // Expose headers for token refresh and request tracing
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// CSRF protection
app.use(attachCsrfToken());
app.use(csrfProtection());

// Global rate limiting
app.use(createRateLimiter({
  limit: process.env.RATE_LIMIT_MAX_REQUESTS ? parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) : 100,
  progressive: true
}));

app.use(handleFileUploadErrors());

// Initialize database connections and start server
(async () => {
  try {
    // Initialize Redis client
    logger.info('Initializing Redis client...');
    const redisClient = await initRedisClient();
    if (redisClient) {
      logger.info('Redis client initialized successfully');
    } else {
      logger.warn('Redis client initialization failed or disabled. Using in-memory fallbacks.');
    }

    // Connect to databases
    logger.info('Connecting to databases...');
    const db1 = await connectDB1();
    const db2 = await connectDB2();
    const db3 = await connectDB3();
    logger.info('Database connections established successfully');

    // DB1 models
    app.locals.Customers = createCustomersModel(db1);
    app.locals.Metadata = createMetadataModel(db1);
    app.locals.Products = createProductsModel(db1);
    app.locals.WatchHistory = createWatchhistoryModel(db1);
    app.locals.SearchHistory = createSearchHistoryModel(db1);

    // DB2 models
    app.locals.SportsBra = createTopwearModel(db2, "SportsBra");
    app.locals.Leggings = createBottomwearModel(db2, "Leggings");
    app.locals.ShortsMen = createBottomwearModel(db2, "ShortsMen");
    app.locals.ShortsKids = createBottomwearModel(db2, "ShortsKids");
    app.locals.ShortsUnisex = createBottomwearModel(db2, "ShortsUnisex");
    app.locals.JoggersUnisex = createBottomwearModel(db2, "JoggersUnisex");

    app.locals.HoodieFullKids = createTopwearModel(db2, "HoodieFullKids");
    app.locals.HoodieFullWomen = createTopwearModel(db2, "HoodieFullWomen");
    app.locals.HoodieFullUnisex = createTopwearModel(db2, "HoodieFullUnisex");

    app.locals.TshirtFullFoldMen = createTopwearModel(db2, "TshirtFullFoldMen");
    app.locals.TshirtFullCrewMen = createTopwearModel(db2, "TshirtFullCrewMen");
    app.locals.TshirtHalfCrewMen = createTopwearModel(db2, "TshirtHalfCrewMen");
    app.locals.TshirtHalfPoloMen = createTopwearModel(db2, "TshirtHalfPoloMen");
    app.locals.TshirtLessFoldMen = createTopwearModel(db2, "TshirtLessFoldMen");
    app.locals.TshirtHalfCrewBoy = createTopwearModel(db2, "TshirtHalfCrewBoy");
    app.locals.TshirtHalfCrewGirl = createTopwearModel(db2, "TshirtHalfCrewGirl");
    app.locals.TshirtHalfCrewKids = createTopwearModel(db2, "TshirtHalfCrewKids");
    app.locals.TshirtHalfFoldWomen = createTopwearModel(db2, "TshirtHalfFoldWomen");
    app.locals.Tshirt3by4FoldWomen = createTopwearModel(db2, "Tshirt3by4FoldWomen");
    app.locals.TshirtFullCrewUnisex = createTopwearModel(db2, "TshirtFullCrewUnisex");
    app.locals.TshirtHalfCrewUnisex = createTopwearModel(db2, "TshirtHalfCrewUnisex");
    app.locals.TshirtHalfFoldUnisex = createTopwearModel(db2, "TshirtHalfFoldUnisex");
    app.locals.TshirtHalfVshapeUnisex = createTopwearModel(db2, "TshirtHalfVshapeUnisex");

    app.locals.ShirtHalfCrewUnisex = createTopwearModel(db2, "ShirtHalfCrewUnisex");
    app.locals.TanktopLessFoldWomen = createTopwearModel(db2, "TanktopLessFoldWomen");
    app.locals.TanktopLessCrewWomen = createTopwearModel(db2, "TanktopLessCrewWomen");
    app.locals.CroptopHalfCrewWomen = createTopwearModel(db2, "CroptopHalfCrewWomen");
    app.locals.JacketFullLycraKids = createTopwearModel(db2, "JacketFullLycraKids");
    app.locals.JacketFullLycraWomen = createTopwearModel(db2, "JacketFullLycraWomen");
    app.locals.JacketFullLycraUnisex = createTopwearModel(db2, "JacketFullLycraUnisex");
    app.locals.JacketFullRibbedUnisex = createTopwearModel(db2, "JacketFullRibbedUnisex");

    app.locals.RompersHalfCrewKids = createTopwearModel(db2, "RompersHalfCrewKids");
    app.locals.MaternitydressHalfFold = createTopwearModel(db2, "MaternitydressHalfFold");
    app.locals.TshirtdressHalfCrew = createTopwearModel(db2, "TshirtdressHalfCrew");
    app.locals.TshirtdressHalfFold = createTopwearModel(db2, "TshirtdressHalfFold");
    app.locals.SweatshirtFullCrewKids = createTopwearModel(db2, "SweatshirtFullCrewKids");
    app.locals.SweatshirtFullCrewUnisex = createTopwearModel(db2, "SweatshirtFullCrewUnisex");

    // DB3 models
    app.locals.PurchasesCod = createPurchasesCodModel(db3);
    app.locals.PurchasesOnline = createPurchasesOnlineModel(db3);

    // Public routes
    app.use("/v1/auth", authRateLimiter(), public_Auth);
    app.use("/v1/search", apiRateLimiter(), searchCacheMiddleware(), public_Search);
    app.use("/v1/redirect", public_Status);
    app.use("/v1/products", apiRateLimiter(), productCacheMiddleware(), public_Product);

    // Admin routes
    app.use("/v1/admin/products", heavyOperationLimiter(), admin_Product);

    // Protected routes (authentication required)
    const protectedRoutes = express.Router();

    // Apply middleware selectively to product routes
    app.use("/v1/product", (req, res, next) => {
      if (req.method === 'GET') {
        // Fix: Properly chain middleware functions
        apiRateLimiter()(req, res, (err) => {
          if (err) return next(err);
          authenticateToken(req, res, (err) => {
            if (err) return next(err);
            userCacheMiddleware()(req, res, next);
          });
        });
      } else {
        next();
      }
    }, private_Product);

    app.use("/v1/profile", apiRateLimiter(), authenticateToken, userCacheMiddleware(), private_Profile);
    app.use("/v1/orders", apiRateLimiter(), authenticateToken, userCacheMiddleware(), private_Orders);
    app.use("/v1/purchases", apiRateLimiter(), authenticateToken, private_Purchase);
    app.use("/v1/addresses", apiRateLimiter(), authenticateToken, userCacheMiddleware(), private_Address);
    app.use("/v1/chat", apiRateLimiter(), authenticateToken, private_Assistant);
    app.use("/v1/history", apiRateLimiter(), authenticateToken, private_History);

    // MCP (Model Context Protocol) server for Lambda AI assistant
    app.use("/mcp", mcpAuthMiddleware, createMCPServer(app));

    setupErrorHandling(app);

    // Start HTTP server (Nginx will handle SSL termination)
    let server;
    server = app.listen(PORT, '0.0.0.0', () => {
      logger.info(`HTTP server running on http://localhost:${PORT} (behind Nginx reverse proxy)`);
    });

    // Handle graceful shutdown
    const gracefulShutdown = async () => {
      logger.info('Received shutdown signal, closing server and database connections...');

      // Close server
      server.close(() => {
        logger.info('HTTP server closed');
      });

      // Close database connections
      try {
        // Close MongoDB connections if they have a close method
        if (db1 && typeof db1.close === 'function') await db1.close();
        if (db2 && typeof db2.close === 'function') await db2.close();
        if (db3 && typeof db3.close === 'function') await db3.close();
        logger.info('Database connections closed');

        // Close Redis connection
        await closeRedisConnection();
        logger.info('Redis connection closed');
      } catch (err) {
        logger.error('Error closing connections:', err);
      }

      // Exit process
      process.exit(0);
    };

    // Listen for termination signals
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
})();

