{"version": 3, "file": "MaxLength.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/MaxLength.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAc,EAAE,GAAW;IACnD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChF,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,GAAW,EAAE,iBAAqC;IAC1E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oEAAoE,EAC/F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,8BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLengthValidator from 'validator/lib/isLength';\n\nexport const MAX_LENGTH = 'maxLength';\n\n/**\n * Checks if the string's length is not more than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function maxLength(value: unknown, max: number): boolean {\n  return typeof value === 'string' && isLengthValidator(value, { min: 0, max });\n}\n\n/**\n * Checks if the string's length is not more than given number. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function MaxLength(max: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MAX_LENGTH,\n      constraints: [max],\n      validator: {\n        validate: (value, args): boolean => maxLength(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be shorter than or equal to $constraint1 characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}