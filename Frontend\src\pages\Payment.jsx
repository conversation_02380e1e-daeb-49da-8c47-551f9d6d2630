// Payment.jsx

import React, { useState } from "react";
import { useLocation } from "react-router-dom";
import usePurchase from "../hooks/usePurchase";
import useProfile from "../hooks/useProfile";

const Payment = () => {
  const location = useLocation();
  const { profile, error: profileError, loading: profileLoading } = useProfile();
  const { purchaseProduct, errorMessage } = usePurchase();
  const [isLoading, setIsLoading] = useState(false);

  // Extract product details from URL search parameters
  const searchParams = new URLSearchParams(location.search);
  const productDetails = {
    size: searchParams.get("size"),
    color: searchParams.get("color"),
    quantity: searchParams.get("quantity"),
    payment_method: searchParams.get("payment_method"),
    price: searchParams.get("price"),
    product_id: searchParams.get("product_id"),
  };

  if (profileLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="p-6 bg-white rounded-xl shadow-lg border border-red-100">
          <div className="flex items-center space-x-3">
            <svg
              className="h-12 w-12 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <p className="text-red-600 font-medium text-lg">{profileError}</p>
          </div>
        </div>
      </div>
    );
  }

  // Extra check to ensure profile data is available
  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <p className="text-gray-600 font-medium">No profile data available.</p>
      </div>
    );
  }

  const handlePaymentRedirect = async () => {
    setIsLoading(true);

    // Check if profile has required data for online payment
    if (productDetails.payment_method === "Online" && !profile?.phone) {
      alert("Please update your phone number in your profile to proceed with online payment.");
      setIsLoading(false);
      return;
    }

    try {
      console.log("Payment request details:", {
        productDetails,
        profile: {
          email: profile?.email,
          phone: profile?.phone,
          hasPhone: !!profile?.phone
        }
      });

      const response = await purchaseProduct(productDetails);
      console.log("Payment response:", response);

      if (response && response.url) {
        console.log("Redirecting to:", response.url);
        window.location.href = response.url;
      } else {
        console.error("No redirect URL received from server", response);
        alert("Payment processing failed. Please try again or contact support.");
      }
    } catch (error) {
      console.error("Payment error:", error);

      // Show more specific error messages
      if (error.message?.includes("phone number")) {
        alert("Please update your phone number in your profile to proceed with online payment.");
      } else if (error.message?.includes("configuration")) {
        alert("Payment service is temporarily unavailable. Please try Cash on Delivery or contact support.");
      } else {
        // Suggest COD as alternative
        const useCOD = confirm("Online payment failed. Would you like to place this order with Cash on Delivery instead?");
        if (useCOD) {
          // Redirect to COD payment
          const codParams = new URLSearchParams({
            ...productDetails,
            payment_method: "COD"
          });
          window.location.href = `/payment?${codParams.toString()}`;
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full">
        <div className="text-center mb-8">
          <div className="bg-blue-100 w-20 h-20 rounded-full mx-auto flex items-center justify-center mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-10 w-10 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Checkout Details
          </h1>
          <p className="text-gray-500">
            Please review your order details below
          </p>
        </div>

        <div className="space-y-4 bg-gray-50 rounded-2xl p-6 mb-8">
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Product ID</span>
            <span className="font-medium text-gray-800">
              {productDetails.product_id}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Size</span>
            <span className="font-medium text-gray-800">
              {productDetails.size}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Color</span>
            <span className="font-medium text-gray-800">
              {productDetails.color}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Quantity</span>
            <span className="font-medium text-gray-800">
              {productDetails.quantity}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-500">Payment Method</span>
            <span className="font-medium text-gray-800">
              {productDetails.payment_method}
            </span>
          </div>
          <div className="pt-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-700">
                Total Amount
              </span>
              <span className="text-2xl font-bold text-blue-600">
                ₹{productDetails.price}
              </span>
            </div>
          </div>
        </div>

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
            <p className="font-medium">
              {errorMessage.includes("phone number")
                ? "Please update your phone number in your profile to proceed with online payment"
                : errorMessage.includes("Price information is missing")
                  ? "Price information is missing. Please try again."
                  : errorMessage.includes("Transaction ID is missing")
                    ? "There was an issue with your transaction. Please try again."
                    : errorMessage}
            </p>
            {errorMessage.includes("phone number") && (
              <div className="mt-2">
                <a
                  href="/profile"
                  className="inline-block bg-red-100 hover:bg-red-200 text-red-800 font-medium py-2 px-4 rounded transition-colors duration-200"
                >
                  Update Profile
                </a>
              </div>
            )}
            {(errorMessage.includes("Price information is missing") || errorMessage.includes("Transaction ID is missing")) && (
              <div className="mt-2">
                <button
                  onClick={() => window.location.reload()}
                  className="inline-block bg-red-100 hover:bg-red-200 text-red-800 font-medium py-2 px-4 rounded transition-colors duration-200"
                >
                  Try Again
                </button>
              </div>
            )}
          </div>
        )}

        <button
          onClick={handlePaymentRedirect}
          className={`w-full block text-center bg-blue-600 text-white py-2 rounded-xl hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl ${
            isLoading ? "opacity-50 cursor-not-allowed" : ""
          }`}
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Redirecting...
            </div>
          ) : (
            "Proceed to Pay"
          )}
        </button>
      </div>
    </div>
  );
};

export default Payment;
