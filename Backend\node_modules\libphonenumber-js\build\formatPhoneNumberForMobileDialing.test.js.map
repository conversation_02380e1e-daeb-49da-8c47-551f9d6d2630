{"version": 3, "file": "formatPhoneNumberForMobileDialing.test.js", "names": [], "sources": ["../source/formatPhoneNumberForMobileDialing.test.js"], "sourcesContent": ["// Google's tests:\r\n// https://github.com/googlei18n/libphonenumber/blob/597983dc4d56ed7e5337a8e74316dc7a3d02d794/javascript/i18n/phonenumbers/phonenumberutil_test.js\r\n\r\n// import metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\n// import formatPhoneNumberForMobileDialing from './formatPhoneNumberForMobileDialing.js'\r\n\r\n// describe('formatPhoneNumberForMobileDialing', () =>\r\n// {\r\n// \tit('should format for mobile dialing', () =>\r\n// \t{\r\n// \t\tformatPhoneNumberForMobileDialing({ phone: '8005553535', country: 'RU' }, 'US', true, metadata).should.equal('****** 555 3535')\r\n// \t\tformatPhoneNumberForMobileDialing({ phone: '8005553535', country: 'RU' }, 'US', false, metadata).should.equal('+78005553535')\r\n// \t\tformatPhoneNumberForMobileDialing({ phone: '8005553535', country: 'RU' }, 'RU', false, metadata).should.equal('8005553535')\r\n// \t})\r\n// })"], "mappings": "AAAA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}