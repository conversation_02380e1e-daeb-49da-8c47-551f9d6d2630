export interface SerializedFields {
    [key: string]: any;
}
export interface SerializedKeyAlias {
    [key: string]: string;
}
export declare function keyTo<PERSON>son(key: string, map?: SerializedKeyAlias): string;
export declare function keyFrom<PERSON>son(key: string, map?: SerializedKeyAlias): string;
export declare function mapKeys(fields: SerializedFields, mapper: typeof keyTo<PERSON>son, map?: SerializedKeyAlias): SerializedFields;
