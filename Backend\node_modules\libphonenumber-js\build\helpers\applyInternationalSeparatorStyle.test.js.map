{"version": 3, "file": "applyInternationalSeparatorStyle.test.js", "names": ["describe", "it", "applyInternationalSeparatorStyle", "should", "equal"], "sources": ["../../source/helpers/applyInternationalSeparatorStyle.test.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\ndescribe('applyInternationalSeparatorStyle', () => {\r\n\tit('should change Google\\'s international format style', () => {\r\n\t\tapplyInternationalSeparatorStyle('(xxx) xxx-xx-xx').should.equal('xxx xxx xx xx')\r\n\t\tapplyInternationalSeparatorStyle('(xxx)xxx').should.equal('xxx xxx')\r\n\t})\r\n})"], "mappings": ";;AAAA;;;;AAEAA,QAAQ,CAAC,kCAAD,EAAqC,YAAM;EAClDC,EAAE,CAAC,oDAAD,EAAuD,YAAM;IAC9D,IAAAC,4CAAA,EAAiC,iBAAjC,EAAoDC,MAApD,CAA2DC,KAA3D,CAAiE,eAAjE;IACA,IAAAF,4CAAA,EAAiC,UAAjC,EAA6CC,MAA7C,CAAoDC,KAApD,CAA0D,SAA1D;EACA,CAHC,CAAF;AAIA,CALO,CAAR"}