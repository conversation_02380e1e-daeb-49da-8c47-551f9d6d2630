// pages/Product.jsx

import React, { useState, useEffect, useCallback } from "react";
import { useNavigate, useLocation, useParams, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Cookies from "js-cookie";
import consentManager from "../utils/consentManager";
import { COOKIE_CATEGORIES } from "../components/CookieConsent";
import useProduct from "../hooks/useProduct";
import useToggle from "../hooks/useToggle";
import useStatus from "../hooks/useStatus";
import useHistory from "../hooks/useHistory";
import { selectToggleState } from "../app/toggle/toggleSelector";
import { selectWatchHistoryState } from "../app/history/historySelector";
import { selectUserProfile } from "../app/users/usersSelector";
import Navbar from "../components/Navbar";
import StickyNavbar from "../components/StickyNavbar";
import Login from "../components/Login";
import { FiH<PERSON>t, <PERSON><PERSON><PERSON>ck, FiShoppingBag, FiCheck, FiStar, FiHome } from "react-icons/fi";

const SingleProduct = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { product_id } = useParams(); // Extract product_id from URL
  const user = useSelector(selectUserProfile); // Get user from Redux state

  // Set default size to "L"
  const [payment, setPaymentMethod] = useState("");
  const [size, setSize] = useState("L");
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  // Check if product data is available in location state
  const [localProduct] = useState(location.state?.product || null);

  // Get color and price from URL query parameters with fallbacks
  const searchParams = new URLSearchParams(location.search);
  const colorFromUrl = searchParams.get('color') || (localProduct?.color || 'default');
  // Handle price as a number if needed
  const priceFromUrl = searchParams.get('price') || (localProduct?.price?.toString() || '0');

  // Use the custom hook for products detail
  const { products, loading, error, fetchProductDetails } = useProduct();

  // Memoize the fetch parameters to prevent unnecessary re-renders
  const fetchParams = useCallback(() => {
    if (!localProduct && product_id) {
      console.log(`Fetching product details for ID: ${product_id}, Color: ${colorFromUrl}, Price: ${priceFromUrl}`);

      // Call fetchProductDetails with the parameters from URL query parameters
      fetchProductDetails(product_id, colorFromUrl, priceFromUrl);
    }
  }, [product_id, colorFromUrl, priceFromUrl, localProduct, fetchProductDetails]);

  useEffect(() => {
    fetchParams();
  }, [fetchParams]);

  // Use the first product from the products array if no localProduct
  const fetchedProduct = products.length > 0 ? products[0] : null;
  const product = localProduct || fetchedProduct;

  // Monitor product data changes
  useEffect(() => {
    // Product data is available and ready to use
  }, [products, fetchedProduct, localProduct, product]);

  // Function to safely check if product has essential props
  const isValidProduct = (prod) => {
    return prod && (prod.username || prod.imageurl || prod.price);
  };

  // Cart toggle and global status
  const { toggleCart: toggleCartFunc } = useToggle();
  const { refreshData } = useStatus();
  const { toggleStatus, message, error: toggleError } = useSelector(selectToggleState);

  const handleCart = async () => {
    // Check if user is authenticated using Redux state
    if (!user) {
      // If not authenticated, show the login popup
      setShowLoginPopup(true);
      return;
    }

    try {
      // Debug: Log the product data structure
      console.log("Product data for cart:", product);

      // Extract product_id with fallbacks
      const productId = product?.product_id || product?.Product_id || product_id;
      console.log("Extracted product_id:", productId);

      // Make sure product has the required fields
      if (!product || !productId) {
        console.error("Product data is incomplete - missing product_id", {
          product,
          productId,
          product_id_from_url: product_id
        });
        return;
      }

      // Create a normalized product object with guaranteed product_id
      const normalizedProduct = {
        ...product,
        product_id: productId,
        // Ensure other required fields have fallbacks
        name: product.name || product.username || 'Unknown Product',
        color: product.color || colorFromUrl || 'Default',
        price: product.price || priceFromUrl || 0,
        brand: product.brand || 'Unknown Brand',
        imageurl: product.imageurl || ''
      };

      console.log("Normalized product for cart:", normalizedProduct);

      await toggleCartFunc(normalizedProduct);
      refreshData();
    } catch (err) {
      console.error("Toggle cart error:", err);
    }
  };

  const submitForm = () => {
    if (!size || !payment) {
      alert("Please select a size and payment method.");
      return;
    }

    // Check if user is authenticated using Redux state
    if (!user) {
      // If not authenticated, show the login popup
      setShowLoginPopup(true);
      return;
    }

    // Get the purchase URL and navigate to it
    const purchaseUrl = getPurchaseRedirectUrl();
    if (purchaseUrl) {
      navigate(purchaseUrl);
    }
  };

  useEffect(() => {
    console.log("Selected Size:", size);
  }, [size]);

  // Get the updateWatchHistoryInDB function from useHistory hook
  const { updateWatchHistoryInDB } = useHistory();

  // Debug log to see product data structure and store recently viewed products
  useEffect(() => {
    // Check if we have consent for functional cookies (watch history)
    if (!consentManager.hasConsent(COOKIE_CATEGORIES.FUNCTIONAL)) {
      console.log('Watch history disabled due to cookie preferences');
      return;
    }

    if (product && Object.keys(product).length > 0) {
      // Product data is available for watch history

      // Store this product in recently viewed products cookie
      const storeRecentlyViewedProduct = () => {
        // Get the product ID and other details
        const currentProductId = product.product_id || product.Product_id || product_id;
        const name = product.name || product.username || 'Unknown';
        const brand = product.brand || 'Unknown';
        const color = product.color || colorFromUrl || 'Default';
        const price = product.price || priceFromUrl || 0;
        const imageurl = product.imageurl || '';

        if (!currentProductId) return; // Skip if no valid ID

        // Get existing recently viewed products details from cookies
        const recentlyViewedDetailsString = Cookies.get('recentlyViewedDetails');
        let recentlyViewedDetails = [];

        if (recentlyViewedDetailsString) {
          try {
            recentlyViewedDetails = JSON.parse(recentlyViewedDetailsString);
            // Ensure it's an array
            if (!Array.isArray(recentlyViewedDetails)) {
              recentlyViewedDetails = [];
            }
          } catch (e) {
            console.error('Error parsing recently viewed products details cookie:', e);
            recentlyViewedDetails = [];
          }
        }

        // Remove the current product if it already exists in the array
        recentlyViewedDetails = recentlyViewedDetails.filter(item => item.product_id !== currentProductId);

        // Add the current product details to the beginning of the array
        recentlyViewedDetails.unshift({ product_id: currentProductId, name, brand, color, price, imageurl });

        // Store the updated array back in cookies (expires in 30 days)
        Cookies.set('recentlyViewedDetails', JSON.stringify(recentlyViewedDetails), { expires: 30 });

        // Recently viewed products details updated

        // If we have 10 products, send them to the backend
        if (recentlyViewedDetails.length >= 10) {
          // Only proceed if user is authenticated
          if (user) {
            // Sending recently viewed products to backend
            // Use a timeout to avoid blocking the UI
            setTimeout(() => {
              updateWatchHistoryInDB(recentlyViewedDetails)
                .then(success => {
                  if (success) {
                    // Successfully updated recently viewed products in database
                    // Note: The cookie is cleared in the hook on success
                    // dont print anything on console
                  } else {
                    // Failed to update recently viewed products, will retry later
                    // dont print anything on console
                  }
                })
                .catch(err => {
                  console.error('Error updating recently viewed products in database:', err);
                });
            }, 100); // Small delay to ensure UI responsiveness
          } else {
            // User not authenticated, skipping backend update
          }
        }

        // Also maintain the simple ID array for backward compatibility
        const recentlyViewedIds = recentlyViewedDetails.map(item => item.product_id);
        Cookies.set('recentlyViewed', JSON.stringify(recentlyViewedIds), { expires: 30 });
      };

      storeRecentlyViewedProduct();
    }
  }, [product, product_id, colorFromUrl, priceFromUrl, updateWatchHistoryInDB, user]);

  // Create the redirect URL for the purchase page
  const getPurchaseRedirectUrl = useCallback(() => {
    if (size && payment && product) {
      const productDetails = {
        size,
        color: product.color || colorFromUrl,
        quantity: 1,
        payment_method: payment,
        price: product.price || priceFromUrl,
        product_id: product.product_id || product.Product_id || product_id,
      };
      const queryParams = new URLSearchParams(productDetails).toString();
      return `/purchase?${queryParams}`;
    }
    return null;
  }, [size, payment, product, colorFromUrl, priceFromUrl, product_id]);

  // We don't need this effect anymore since we're passing the redirect URL directly to the Login component

  if (loading && !localProduct) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-900 border-t-transparent"></div>
      </div>
    );
  }

  if (error && !localProduct) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="p-4 bg-red-100 text-red-700 rounded-lg">{error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      {isValidProduct(product) ? (
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumbs */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm">
              <li className="flex items-center">
                <Link to="/" className="text-gray-500 hover:text-indigo-600 flex items-center">
                  <FiHome className="mr-1" size={14} />
                  Home
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li>
                <Link to="/category" className="text-gray-500 hover:text-indigo-600">
                  Clothing
                </Link>
              </li>
              <li>
                <span className="text-gray-400">/</span>
              </li>
              <li className="text-gray-900 font-medium truncate max-w-[200px]">{product.name || product.username}</li>
            </ol>
          </nav>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="aspect-w-3 aspect-h-4 rounded-lg bg-gray-50 overflow-hidden border border-gray-100 group">
                <img
                  src={product.imageurl}
                  alt={product.name || product.username}
                  className="w-full h-full object-cover object-center transition-transform duration-500 ease-in-out group-hover:scale-105"
                />
              </div>

              {/* Image thumbnails - can be expanded in the future */}
              <div className="hidden lg:flex space-x-2 justify-center">
                <div className="w-20 h-20 border-2 border-indigo-500 rounded-md overflow-hidden cursor-pointer">
                  <img src={product.imageurl} alt="Thumbnail" className="w-full h-full object-cover" />
                </div>
                {/* Placeholder thumbnails */}
                {[1, 2, 3].map((i) => (
                  <div key={i} className="w-20 h-20 border border-gray-200 rounded-md overflow-hidden cursor-pointer bg-gray-50">
                    <div className="w-full h-full flex items-center justify-center text-gray-300">+</div>
                  </div>
                ))}
              </div>

              {message && <p className="text-green-500 text-sm mt-2">{message}</p>}
              {toggleError && <p className="text-red-500 text-sm mt-2">{toggleError}</p>}
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              {/* Brand and Title */}
              <div>
                <div className="text-indigo-600 font-medium text-sm mb-1">{product.brand || "BrandX"}</div>
                <h1 className="text-2xl font-bold tracking-tight text-gray-900 mb-2 md:text-3xl">
                  {product.name || product.username}
                </h1>

                {/* Ratings */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center bg-green-50 text-green-700 px-2 py-1 rounded">
                    <span className="font-medium mr-1">{product.ratings || 4.5}</span>
                    <FiStar size={14} className="fill-current" />
                  </div>
                  <div className="text-gray-500 text-sm ml-2 border-l border-gray-200 pl-2">
                    {Math.floor(Math.random() * 100) + 10} Ratings
                  </div>
                </div>
              </div>

              {/* Price */}
              <div className="border-t border-b border-gray-100 py-4">
                <div className="flex items-center">
                  <span className="text-2xl font-bold text-gray-900 mr-2 md:text-3xl">₹{Math.round(product.price - (product.price * (product.discount / 100)))}</span>
                  {product.discount > 0 && (
                    <>
                      <span className="text-gray-500 line-through mr-2">₹{product.price}</span>
                      <span className="text-green-600 font-medium">{product.discount}% OFF</span>
                    </>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">inclusive of all taxes</p>

                {/* Special offers */}
                {product.discount >= 20 && (
                  <div className="mt-3 bg-orange-50 text-orange-700 text-sm px-3 py-2 rounded-md inline-block">
                    <span className="font-medium">SPECIAL OFFER</span> - Limited time deal!
                  </div>
                )}
              </div>

              {/* Size Selection */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium text-gray-900">Select Size</h3>
                  <button className="text-indigo-600 text-sm font-medium hover:text-indigo-800">SIZE GUIDE</button>
                </div>

                <div className="flex flex-wrap gap-3">
                  {(product.availableSizes || ["S", "M", "L", "XL", "XXL"]).map((sizeOption) => (
                    <button
                      key={sizeOption}
                      type="button"
                      onClick={() => setSize(sizeOption)}
                      className={`w-14 h-14 rounded-full flex items-center justify-center border transition-all duration-200 hover:-translate-y-1 ${size === sizeOption
                        ? 'border-indigo-600 bg-indigo-50 text-indigo-600 shadow-sm'
                        : 'border-gray-300 hover:border-gray-400'}`}
                    >
                      {sizeOption}
                    </button>
                  ))}
                </div>
              </div>

              {/* Delivery */}
              <div className="border-t border-gray-100 pt-4">
                <div className="flex items-center mb-3">
                  <FiTruck className="text-gray-500 mr-2" />
                  <h3 className="font-medium text-gray-900">Delivery Options</h3>
                </div>

                <div className="flex items-center border border-gray-200 rounded-md p-3 mb-3">
                  <input type="text" placeholder="Enter Pincode" className="flex-1 border-none focus:ring-0 text-sm focus:border-indigo-500 focus:ring-indigo-500" />
                  <button className="text-indigo-600 font-medium text-sm">CHECK</button>
                </div>

                <div className="flex items-center text-sm text-gray-600 mb-1">
                  <FiCheck className="text-green-500 mr-2" />
                  <span>Free delivery on orders above ₹499</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <FiCheck className="text-green-500 mr-2" />
                  <span>Cash on delivery available</span>
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select name="payment_method" required className="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500" onChange={(e) => setPaymentMethod(e.target.value)}>
                  <option hidden>-select-</option>
                  <option value="COD">Cash on Delivery</option>
                  <option value="Online">Online Payment</option>
                </select>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 pt-2">
                <button type="button" onClick={submitForm} className="flex-1 bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg font-medium flex items-center justify-center">
                  <FiShoppingBag className="mr-2" />BUY NOW</button>
                <button type="button" onClick={handleCart} className="flex-1 border-2 border-indigo-600 text-indigo-600 px-6 py-3 rounded-md hover:bg-indigo-50 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg font-medium flex items-center justify-center">ADD TO BAG</button>
              </div>

              {/* Product Highlights */}
              <div className="border-t border-gray-100 pt-6">
                <h3 className="font-medium text-gray-900 mb-4">Key Highlights</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Design</div>
                    <div className="text-gray-900 text-sm">{product.design || "Regular"}</div>
                  </div>
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Fit</div>
                    <div className="text-gray-900 text-sm">{product.fit || "Regular Fit"}</div>
                  </div>
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Neck</div>
                    <div className="text-gray-900 text-sm">{product.neckline || "Round Neck"}</div>
                  </div>
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Occasion</div>
                    <div className="text-gray-900 text-sm">{product.occasion || "Casual Wear"}</div>
                  </div>
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Sleeve</div>
                    <div className="text-gray-900 text-sm">{product.sleeveLength || "Full Sleeve"}</div>
                  </div>
                  <div className="flex items-start p-2 rounded-md transition-colors duration-200 hover:bg-gray-50">
                    <div className="text-gray-600 text-sm font-medium w-24">Wash Care</div>
                    <div className="text-gray-900 text-sm">{product.washCare || "Machine wash"}</div>
                  </div>
                </div>
              </div>

              {/* Product Description */}
              <div className="border-t border-gray-100 pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-medium text-gray-900">Product Description</h3>
                  <button className="text-indigo-600 text-sm">Read More</button>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {product.description || "A premium quality product designed for comfort and style. Made with high-quality materials that ensure durability and a great fit. Perfect for casual wear and everyday use."}
                </p>
              </div>
            </div>
          </div>

          {/* Additional Product Information */}
          <div className="mt-16 border-t border-gray-100 pt-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Product Specifications */}
              <div>
                <h3 className="font-medium text-gray-900 mb-4">Specifications</h3>
                <table className="w-full text-sm">
                  <tbody>
                    <tr className="border-b border-gray-100">
                      <td className="py-2 text-gray-600">Fabric</td>
                      <td className="py-2 text-gray-900">{product.fabric || "Cotton"}</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2 text-gray-600">Pattern</td>
                      <td className="py-2 text-gray-900">{product.pattern || "Plain"}</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2 text-gray-600">Fit</td>
                      <td className="py-2 text-gray-900">{product.fit || "Regular Fit"}</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2 text-gray-600">Neck</td>
                      <td className="py-2 text-gray-900">{product.neckline || "Round Neck"}</td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="py-2 text-gray-600">Sleeve</td>
                      <td className="py-2 text-gray-900">{product.sleeveLength || "Full Sleeve"}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Care Instructions */}
              <div>
                <h3 className="font-medium text-gray-900 mb-4">Care Instructions</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Machine wash as per instructions on the label
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Wash with similar colors
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Do not bleach
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Dry in shade
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Medium iron
                  </li>
                </ul>
              </div>

              {/* Shipping & Returns */}
              <div>
                <h3 className="font-medium text-gray-900 mb-4">Shipping & Returns</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Free shipping on orders above ₹499
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Delivery within 5-7 business days
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    Easy 15-day returns & exchanges
                  </li>
                  <li className="flex items-start">
                    <span className="text-indigo-600 mr-2">•</span>
                    100% authentic products
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      ) : (
        <div className="text-center py-16">
          <p className="text-gray-500">Product not found or loading...</p>
        </div>
      )}
      <StickyNavbar />

      {/* Login Popup */}
      <Login
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        redirectUrl={getPurchaseRedirectUrl() || "/"}
      />
    </div>
  );
};

export default SingleProduct;
