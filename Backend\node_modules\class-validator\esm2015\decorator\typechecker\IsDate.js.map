{"version": 3, "file": "IsDate.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsDate.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,KAAK,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,mCAAmC,EAAE,iBAAiB,CAAC;SAChH;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_DATE = 'isDate';\n\n/**\n * Checks if a given value is a date.\n */\nexport function isDate(value: unknown): value is Date {\n  return value instanceof Date && !isNaN(value.getTime());\n}\n\n/**\n * Checks if a value is a date.\n */\nexport function IsDate(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DATE,\n      validator: {\n        validate: (value, args): boolean => isDate(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a Date instance', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}