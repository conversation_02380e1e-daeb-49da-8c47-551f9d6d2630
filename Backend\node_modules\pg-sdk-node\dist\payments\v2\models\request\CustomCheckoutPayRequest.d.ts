import { UpiIntentPayRequestBuilder } from '../../../../common/models/request/instruments/UpiIntentPayRequestBuilder';
import { DeviceContext } from '../../../../common/models/request/DeviceContext';
import { InstrumentConstraint } from '../../../../common/models/request/InstrumentConstraint';
import { MetaInfo } from '../../../../common/models/MetaInfo';
import { PaymentFlow } from '../../../../common/models/PaymentFlow';
import { UpiCollectPayViaVpaRequestBuilder } from '../../../../common/models/request/instruments/UpiCollectPayViaVpaRequestBuilder';
import { UpiQrRequestBuilder } from '../../../../common/models/request/instruments/UpiQrRequestBuilder';
import { NetBankingPayRequestBuilder } from '../../../../common/models/request/instruments/NetBankingPayRequestBuilder';
import { TokenPayRequestBuilder } from '../../../../common/models/request/instruments/TokenPayRequestBuilder';
import { CardPayRequestBuilder } from '../../../../common/models/request/instruments/CardPayRequestBuilder';
/**
 * Creates a request based on different builder for different instruments.
 * 1. UpiIntentPayRequestBuilder -> Upi Intent
 * 2. UpiCollectPayViaVpaRequestBuilder -> Upi Collect
 * 3. UpiQrRequestBuilder -> Upi QR
 * 4. NetBankingPayRequestBuilder -> Netbanking
 * 5. TokenPayRequestBuilder -> Token
 * 6. CardPayRequestBuilder -> Card
 */
export declare class CustomCheckoutPayRequest {
    merchantOrderId: string;
    amount: number;
    paymentFlow: PaymentFlow;
    metaInfo?: MetaInfo;
    constraints?: InstrumentConstraint[];
    deviceContext?: DeviceContext;
    expireAfter?: number;
    constructor(merchantOrderId: string, amount: number, paymentFlow: PaymentFlow, expireAfter?: number, metaInfo?: MetaInfo, constrainsts?: InstrumentConstraint[], deviceContext?: DeviceContext);
    static UpiIntentPayRequestBuilder(): UpiIntentPayRequestBuilder;
    static UpiCollectPayViaVpaRequestBuilder(): UpiCollectPayViaVpaRequestBuilder;
    static UpiQrRequestBuilder(): UpiQrRequestBuilder;
    static NetBankingPayRequestBuilder(): NetBankingPayRequestBuilder;
    static TokenPayRequestBuilder(): TokenPayRequestBuilder;
    static CardPayRequestBuilder(): CardPayRequestBuilder;
}
