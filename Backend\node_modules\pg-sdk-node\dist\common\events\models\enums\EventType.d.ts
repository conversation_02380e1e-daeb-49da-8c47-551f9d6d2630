export declare enum EventType {
    PAY_SUCCESS = "PAY_SUCCESS",
    PAY_FAILED = "PAY_FAILED",
    REFUND_SUCCESS = "REFUND_SUCCESS",
    REFUND_FAILED = "REFUND_FAILED",
    REFUND_STATUS_SUCCESS = "REFUND_STATUS_SUCCESS",
    REFUND_STATUS_FAILED = "REFUND_STATUS_FAILED",
    ORDER_STATUS_SUCCESS = "ORDER_STATUS_SUCCESS",
    ORDER_STATUS_FAILED = "ORDER_STATUS_FAILED",
    TRANSACTION_STATUS_SUCCESS = "TRANSACTION_STATUS_SUCCESS",
    TRANSACTION_STATUS_FAILED = "TRANSACTION_STATUS_FAILED",
    CREATE_SDK_ORDER_SUCCESS = "CREATE_SDK_ORDER_SUCCESS",
    CREATE_SDK_ORDER_FAILED = "CREATE_SDK_ORDER_FAILED",
    STANDARD_CHECKOUT_CLIENT_INITIALIZED = "STANDARD_CHECKOUT_CLIENT_INITIALIZED",
    CUSTOM_CHECKOUT_CLIENT_INITIALIZED = "CUSTOM_CHECKOUT_CLIENT_INITIALIZED",
    TOKEN_SERVICE_INITIALIZED = "TOKEN_SERVICE_INITIALIZED",
    OAUTH_FETCH_FAILED_USED_CACHED_TOKEN = "OAUTH_FETCH_FAILED_USED_CACHED_TOKEN",
    CALLBACK_SERIALIZATION_FAILED = "CALLBACK_SERIALIZATION_FAILED"
}
