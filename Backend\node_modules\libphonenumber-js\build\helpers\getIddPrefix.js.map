{"version": 3, "file": "getIddPrefix.js", "names": ["SINGLE_IDD_PREFIX_REG_EXP", "getIddPrefix", "country", "callingCode", "metadata", "countryMetadata", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "defaultIDDPrefix", "test", "IDDPrefix"], "sources": ["../../source/helpers/getIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\r\nconst SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/\r\n\r\n// For regions that have multiple IDD prefixes\r\n// a preferred IDD prefix is returned.\r\nexport default function getIddPrefix(country, callingCode, metadata) {\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tif (countryMetadata.defaultIDDPrefix()) {\r\n\t\treturn countryMetadata.defaultIDDPrefix()\r\n\t}\r\n\tif (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\r\n\t\treturn countryMetadata.IDDPrefix()\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;AAAA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMA,yBAAyB,GAAG,wCAAlC,C,CAEA;AACA;;AACe,SAASC,YAAT,CAAsBC,OAAtB,EAA+BC,WAA/B,EAA4CC,QAA5C,EAAsD;EACpE,IAAMC,eAAe,GAAG,IAAIC,oBAAJ,CAAaF,QAAb,CAAxB;EACAC,eAAe,CAACE,mBAAhB,CAAoCL,OAApC,EAA6CC,WAA7C;;EACA,IAAIE,eAAe,CAACG,gBAAhB,EAAJ,EAAwC;IACvC,OAAOH,eAAe,CAACG,gBAAhB,EAAP;EACA;;EACD,IAAIR,yBAAyB,CAACS,IAA1B,CAA+BJ,eAAe,CAACK,SAAhB,EAA/B,CAAJ,EAAiE;IAChE,OAAOL,eAAe,CAACK,SAAhB,EAAP;EACA;AACD"}