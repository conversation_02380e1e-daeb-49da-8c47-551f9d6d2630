// products.js

import { Schema } from "mongoose";
import mongooseSequence from "mongoose-sequence";

export const createProductsModel = (connection) => {

  const AutoIncrement = mongooseSequence(connection);
  const productsSchema = new Schema({
    email: { type: String, required: true },
    product_id: { type: String, required: true },
    name: { type: String, default: 'Unknown' },
    brand: { type: String, default: 'Unknown' },
    color: { type: String, default: 'Default' },
    price: { type: Number, default: 0 },
    imageurl: { type: String, default: '' },
    wishlist: { type: Boolean, default: false },
    cart: { type: Boolean, default: false }
  });

  // Create a compound unique index on email and product_id to prevent duplicates
  productsSchema.index({ email: 1, product_id: 1 }, { unique: true });

  productsSchema.plugin(AutoIncrement, { inc_field: "sl_no" });
  return connection.model("products", productsSchema);
};
