// fix-products-schema.js
// Script to fix the products collection schema and remove duplicate key errors

import { config } from 'dotenv';
import mongoose from 'mongoose';

// Load environment variables
config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/customer_db';

async function fixProductsSchema() {
  console.log('🔧 Starting products schema fix...');
  
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    const collection = db.collection('products');

    // Check current indexes
    console.log('\n📋 Current indexes:');
    const indexes = await collection.indexes();
    indexes.forEach(index => {
      console.log(`  - ${JSON.stringify(index.key)} (${index.name})`);
    });

    // Drop the problematic index if it exists
    try {
      await collection.dropIndex('Product_id_1');
      console.log('✅ Dropped problematic Product_id_1 index');
    } catch (error) {
      if (error.code === 27) {
        console.log('ℹ️  Product_id_1 index does not exist (already fixed)');
      } else {
        console.log('⚠️  Error dropping Product_id_1 index:', error.message);
      }
    }

    // Remove documents with null product_id
    const deleteResult = await collection.deleteMany({ 
      $or: [
        { product_id: null },
        { product_id: { $exists: false } },
        { product_id: "" }
      ]
    });
    console.log(`🗑️  Removed ${deleteResult.deletedCount} documents with null/missing product_id`);

    // Find and remove duplicates (keep the most recent one)
    console.log('\n🔍 Looking for duplicates...');
    const duplicates = await collection.aggregate([
      {
        $group: {
          _id: { email: "$email", product_id: "$product_id" },
          count: { $sum: 1 },
          docs: { $push: { _id: "$_id", createdAt: "$createdAt" } }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      }
    ]).toArray();

    if (duplicates.length > 0) {
      console.log(`Found ${duplicates.length} duplicate groups`);
      
      for (const duplicate of duplicates) {
        // Sort by createdAt (keep the newest) or by _id if no createdAt
        const sortedDocs = duplicate.docs.sort((a, b) => {
          if (a.createdAt && b.createdAt) {
            return new Date(b.createdAt) - new Date(a.createdAt);
          }
          return b._id.toString().localeCompare(a._id.toString());
        });

        // Keep the first (newest) document, delete the rest
        const toDelete = sortedDocs.slice(1).map(doc => doc._id);
        
        if (toDelete.length > 0) {
          await collection.deleteMany({ _id: { $in: toDelete } });
          console.log(`  Removed ${toDelete.length} duplicate(s) for email: ${duplicate._id.email}, product_id: ${duplicate._id.product_id}`);
        }
      }
    } else {
      console.log('✅ No duplicates found');
    }

    // Create the correct compound unique index
    try {
      await collection.createIndex(
        { email: 1, product_id: 1 }, 
        { unique: true, name: 'email_1_product_id_1' }
      );
      console.log('✅ Created compound unique index on email + product_id');
    } catch (error) {
      if (error.code === 85) {
        console.log('ℹ️  Compound index already exists');
      } else {
        console.log('⚠️  Error creating compound index:', error.message);
      }
    }

    // Verify the fix
    console.log('\n🔍 Final verification:');
    const finalIndexes = await collection.indexes();
    console.log('Final indexes:');
    finalIndexes.forEach(index => {
      console.log(`  - ${JSON.stringify(index.key)} (${index.name})`);
    });

    const totalDocs = await collection.countDocuments();
    console.log(`📊 Total documents in collection: ${totalDocs}`);

    console.log('\n🎉 Products schema fix completed successfully!');

  } catch (error) {
    console.error('💥 Error fixing products schema:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixProductsSchema()
  .then(() => {
    console.log('\n✨ All done! The products collection should now work correctly.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error);
    process.exit(1);
  });
