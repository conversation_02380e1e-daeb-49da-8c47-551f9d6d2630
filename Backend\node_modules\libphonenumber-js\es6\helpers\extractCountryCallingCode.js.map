{"version": 3, "file": "extractCountryCallingCode.js", "names": ["stripIddPrefix", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "<PERSON><PERSON><PERSON>", "MAX_LENGTH_COUNTRY_CODE", "extractCountryCallingCode", "number", "country", "callingCode", "metadata", "isNumberWithIddPrefix", "numberWithoutIDD", "countryCallingCode", "shorterNumber", "countryCallingCodeSource", "i", "length", "slice", "hasCallingCode", "selectNumberingPlan"], "sources": ["../../source/helpers/extractCountryCallingCode.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js'\r\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js'\r\nimport Metadata from '../metadata.js'\r\nimport { MAX_LENGTH_COUNTRY_CODE } from '../constants.js'\r\n\r\n/**\r\n * Converts a phone number digits (possibly with a `+`)\r\n * into a calling code and the rest phone number digits.\r\n * The \"rest phone number digits\" could include\r\n * a national prefix, carrier code, and national\r\n * (significant) number.\r\n * @param  {string} number — Phone number digits (possibly with a `+`).\r\n * @param  {string} [country] — Default country.\r\n * @param  {string} [callingCode] — Default calling code (some phone numbering plans are non-geographic).\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCodeSource: string?, countryCallingCode: string?, number: string }`\r\n * @example\r\n * // Returns `{ countryCallingCode: \"1\", number: \"2133734253\" }`.\r\n * extractCountryCallingCode('2133734253', 'US', null, metadata)\r\n * extractCountryCallingCode('2133734253', null, '1', metadata)\r\n * extractCountryCallingCode('+12133734253', null, null, metadata)\r\n * extractCountryCallingCode('+12133734253', 'RU', null, metadata)\r\n */\r\nexport default function extractCountryCallingCode(\r\n\tnumber,\r\n\tcountry,\r\n\tcallingCode,\r\n\tmetadata\r\n) {\r\n\tif (!number) {\r\n\t\treturn {}\r\n\t}\r\n\r\n\tlet isNumberWithIddPrefix\r\n\r\n\t// If this is not an international phone number,\r\n\t// then either extract an \"IDD\" prefix, or extract a\r\n\t// country calling code from a number by autocorrecting it\r\n\t// by prepending a leading `+` in cases when it starts\r\n\t// with the country calling code.\r\n\t// https://wikitravel.org/en/International_dialling_prefix\r\n\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\tif (number[0] !== '+') {\r\n\t\t// Convert an \"out-of-country\" dialing phone number\r\n\t\t// to a proper international phone number.\r\n\t\tconst numberWithoutIDD = stripIddPrefix(number, country, callingCode, metadata)\r\n\t\t// If an IDD prefix was stripped then\r\n\t\t// convert the number to international one\r\n\t\t// for subsequent parsing.\r\n\t\tif (numberWithoutIDD && numberWithoutIDD !== number) {\r\n\t\t\tisNumberWithIddPrefix = true\r\n\t\t\tnumber = '+' + numberWithoutIDD\r\n\t\t} else {\r\n\t\t\t// Check to see if the number starts with the country calling code\r\n\t\t\t// for the default country. If so, we remove the country calling code,\r\n\t\t\t// and do some checks on the validity of the number before and after.\r\n\t\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\t\tif (country || callingCode) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcountryCallingCode,\r\n\t\t\t\t\tnumber: shorterNumber\r\n\t\t\t\t} = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\t\t\t\t\tnumber,\r\n\t\t\t\t\tcountry,\r\n\t\t\t\t\tcallingCode,\r\n\t\t\t\t\tmetadata\r\n\t\t\t\t)\r\n\t\t\t\tif (countryCallingCode) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tcountryCallingCodeSource: 'FROM_NUMBER_WITHOUT_PLUS_SIGN',\r\n\t\t\t\t\t\tcountryCallingCode,\r\n\t\t\t\t\t\tnumber: shorterNumber\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\t// No need to set it to `UNSPECIFIED`. It can be just `undefined`.\r\n\t\t\t\t// countryCallingCodeSource: 'UNSPECIFIED',\r\n\t\t\t\tnumber\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Fast abortion: country codes do not begin with a '0'\r\n\tif (number[1] === '0') {\r\n\t\treturn {}\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// The thing with country phone codes\r\n\t// is that they are orthogonal to each other\r\n\t// i.e. there's no such country phone code A\r\n\t// for which country phone code B exists\r\n\t// where B starts with A.\r\n\t// Therefore, while scanning digits,\r\n\t// if a valid country code is found,\r\n\t// that means that it is the country code.\r\n\t//\r\n\tlet i = 2\r\n\twhile (i - 1 <= MAX_LENGTH_COUNTRY_CODE && i <= number.length) {\r\n\t\tconst countryCallingCode = number.slice(1, i)\r\n\t\tif (metadata.hasCallingCode(countryCallingCode)) {\r\n\t\t\tmetadata.selectNumberingPlan(countryCallingCode)\r\n\t\t\treturn {\r\n\t\t\t\tcountryCallingCodeSource: isNumberWithIddPrefix ? 'FROM_NUMBER_WITH_IDD' : 'FROM_NUMBER_WITH_PLUS_SIGN',\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\tnumber: number.slice(i)\r\n\t\t\t}\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\r\n\treturn {}\r\n}\r\n\r\n// The possible values for the returned `countryCallingCodeSource` are:\r\n//\r\n// Copy-pasted from:\r\n// https://github.com/google/libphonenumber/blob/master/resources/phonenumber.proto\r\n//\r\n// // The source from which the country_code is derived. This is not set in the\r\n// // general parsing method, but in the method that parses and keeps raw_input.\r\n// // New fields could be added upon request.\r\n// enum CountryCodeSource {\r\n//  // Default value returned if this is not set, because the phone number was\r\n//  // created using parse, not parseAndKeepRawInput. hasCountryCodeSource will\r\n//  // return false if this is the case.\r\n//  UNSPECIFIED = 0;\r\n//\r\n//  // The country_code is derived based on a phone number with a leading \"+\",\r\n//  // e.g. the French number \"+33 1 42 68 53 00\".\r\n//  FROM_NUMBER_WITH_PLUS_SIGN = 1;\r\n//\r\n//  // The country_code is derived based on a phone number with a leading IDD,\r\n//  // e.g. the French number \"011 33 1 42 68 53 00\", as it is dialled from US.\r\n//  FROM_NUMBER_WITH_IDD = 5;\r\n//\r\n//  // The country_code is derived based on a phone number without a leading\r\n//  // \"+\", e.g. the French number \"33 1 42 68 53 00\" when defaultCountry is\r\n//  // supplied as France.\r\n//  FROM_NUMBER_WITHOUT_PLUS_SIGN = 10;\r\n//\r\n//  // The country_code is derived NOT based on the phone number itself, but\r\n//  // from the defaultCountry parameter provided in the parsing function by the\r\n//  // clients. This happens mostly for numbers written in the national format\r\n//  // (without country code). For example, this would be set when parsing the\r\n//  // French number \"01 42 68 53 00\", when defaultCountry is supplied as\r\n//  // France.\r\n//  FROM_DEFAULT_COUNTRY = 20;\r\n// }"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,qBAA3B;AACA,OAAOC,+DAAP,MAA4E,sEAA5E;AACA,OAAOC,QAAP,MAAqB,gBAArB;AACA,SAASC,uBAAT,QAAwC,iBAAxC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,yBAAT,CACdC,MADc,EAEdC,OAFc,EAGdC,WAHc,EAIdC,QAJc,EAKb;EACD,IAAI,CAACH,MAAL,EAAa;IACZ,OAAO,EAAP;EACA;;EAED,IAAII,qBAAJ,CALC,CAOD;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIJ,MAAM,CAAC,CAAD,CAAN,KAAc,GAAlB,EAAuB;IACtB;IACA;IACA,IAAMK,gBAAgB,GAAGV,cAAc,CAACK,MAAD,EAASC,OAAT,EAAkBC,WAAlB,EAA+BC,QAA/B,CAAvC,CAHsB,CAItB;IACA;IACA;;IACA,IAAIE,gBAAgB,IAAIA,gBAAgB,KAAKL,MAA7C,EAAqD;MACpDI,qBAAqB,GAAG,IAAxB;MACAJ,MAAM,GAAG,MAAMK,gBAAf;IACA,CAHD,MAGO;MACN;MACA;MACA;MACA;MACA,IAAIJ,OAAO,IAAIC,WAAf,EAA4B;QAC3B,4BAGIN,+DAA+D,CAClEI,MADkE,EAElEC,OAFkE,EAGlEC,WAHkE,EAIlEC,QAJkE,CAHnE;QAAA,IACCG,kBADD,yBACCA,kBADD;QAAA,IAESC,aAFT,yBAECP,MAFD;;QASA,IAAIM,kBAAJ,EAAwB;UACvB,OAAO;YACNE,wBAAwB,EAAE,+BADpB;YAENF,kBAAkB,EAAlBA,kBAFM;YAGNN,MAAM,EAAEO;UAHF,CAAP;QAKA;MACD;;MACD,OAAO;QACN;QACA;QACAP,MAAM,EAANA;MAHM,CAAP;IAKA;EACD,CArDA,CAuDD;;;EACA,IAAIA,MAAM,CAAC,CAAD,CAAN,KAAc,GAAlB,EAAuB;IACtB,OAAO,EAAP;EACA;;EAEDG,QAAQ,GAAG,IAAIN,QAAJ,CAAaM,QAAb,CAAX,CA5DC,CA8DD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIM,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAG,CAAJ,IAASX,uBAAT,IAAoCW,CAAC,IAAIT,MAAM,CAACU,MAAvD,EAA+D;IAC9D,IAAMJ,mBAAkB,GAAGN,MAAM,CAACW,KAAP,CAAa,CAAb,EAAgBF,CAAhB,CAA3B;;IACA,IAAIN,QAAQ,CAACS,cAAT,CAAwBN,mBAAxB,CAAJ,EAAiD;MAChDH,QAAQ,CAACU,mBAAT,CAA6BP,mBAA7B;MACA,OAAO;QACNE,wBAAwB,EAAEJ,qBAAqB,GAAG,sBAAH,GAA4B,4BADrE;QAENE,kBAAkB,EAAlBA,mBAFM;QAGNN,MAAM,EAAEA,MAAM,CAACW,KAAP,CAAaF,CAAb;MAHF,CAAP;IAKA;;IACDA,CAAC;EACD;;EAED,OAAO,EAAP;AACA,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}