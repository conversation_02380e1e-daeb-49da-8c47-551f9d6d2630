# Backend Startup Guide

This guide will help you start the Belilly backend server correctly on port 3000.

## Quick Start

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Start the server**:
   ```bash
   npm start
   ```

3. **Test the connection**:
   ```bash
   npm run test-connection
   ```

## Port Configuration

The backend server is configured to run on **port 3000** by default. This is controlled by the `PORT` environment variable in the `.env` file:

```env
PORT=3000
```

## Troubleshooting Connection Issues

If you're getting `ERR_CONNECTION_REFUSED` errors:

### 1. Check if the server is running
```bash
# Run the connection test
npm run test-connection

# Or manually check
curl http://localhost:3000/v1/products/counter
```

### 2. Verify environment configuration
Make sure your `.env` file has the correct settings:
```env
# Server configuration
PORT=3000
BASE_URL=http://localhost:3000

# Frontend URL for CORS
FRONTEND_URL=http://localhost:5173
```

### 3. Check for port conflicts
```bash
# On Windows
netstat -ano | findstr :3000

# On macOS/Linux
lsof -i :3000
```

### 4. Restart the server
```bash
# Stop the server (Ctrl+C)
# Then restart
npm start
```

## Environment Variables

Key environment variables that affect connectivity:

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | 3000 | Server port |
| `BASE_URL` | http://localhost:3000 | Base URL for API |
| `FRONTEND_URL` | http://localhost:5173 | Frontend URL for CORS |
| `NODE_ENV` | production | Environment mode |

## CORS Configuration

The server is configured to accept requests from:
- `http://localhost:5173` (Frontend development server)
- `https://localhost` (HTTPS development)

If you're running the frontend on a different port, update the `FRONTEND_URL` in your `.env` file.

## Health Check Endpoints

Test these endpoints to verify the server is working:

- **Products Counter**: `GET /v1/products/counter`
- **MCP Health**: `GET /mcp/health`

## Common Issues

### Issue: "Connection refused"
**Solution**: The server is not running. Start it with `npm start`.

### Issue: "CORS error"
**Solution**: Check that `FRONTEND_URL` in `.env` matches your frontend URL.

### Issue: "Port already in use"
**Solution**: Another process is using port 3000. Either stop that process or change the port in `.env`.

### Issue: "Database connection failed"
**Solution**: Check your MongoDB connection strings in the `.env` file.

## Development vs Production

### Development (Local)
- Server runs on `http://localhost:3000`
- CORS allows `http://localhost:5173`
- Detailed error messages

### Production (Behind Nginx)
- Nginx proxies requests to `localhost:3000`
- CORS configured for production domain
- Secure cookies enabled

## Next Steps

Once the backend is running successfully:

1. Start the frontend development server
2. Test the API endpoints
3. Verify authentication flows
4. Check wishlist and cart functionality

For more detailed API documentation, see [README.md](./README.md).
