{"version": 3, "file": "IsUppercase.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsUppercase.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,IAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,WAAW,CAAC,KAAK,CAAC,EAAlB,CAAkB;YACtD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,6BAA6B,EAA1C,CAA0C,EAAE,iBAAiB,CAAC;SAC1G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isUppercaseValidator from 'validator/lib/isUppercase';\n\nexport const IS_UPPERCASE = 'isUppercase';\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function isUppercase(value: unknown): boolean {\n  return typeof value === 'string' && isUppercaseValidator(value);\n}\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function IsUppercase(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_UPPERCASE,\n      validator: {\n        validate: (value, args): boolean => isUppercase(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be uppercase', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}