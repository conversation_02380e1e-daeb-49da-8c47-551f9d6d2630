"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenService = void 0;
const OAuthResponse_1 = require("./OAuthResponse");
const Headers_1 = require("../constants/Headers");
const TokenConstants_1 = require("./TokenConstants");
const HttpCommand_1 = require("../http/HttpCommand");
const HttpMethodType_1 = require("../http/HttpMethodType");
const class_transformer_1 = require("class-transformer");
const EnvConfig_1 = require("../../EnvConfig");
const EventBuillder_1 = require("../events/builders/EventBuillder");
const EventType_1 = require("../events/models/enums/EventType");
const async_mutex_1 = require("async-mutex");
class TokenService {
    set oAuthResponse(oAuthResponse) {
        TokenService.oAuthResponse = oAuthResponse;
    }
    constructor(httpClient, credentialConfig, env, eventPublisher) {
        this.mutex = new async_mutex_1.Mutex();
        this.prepareRequestHeaders = () => {
            return {
                [Headers_1.Headers.CONTENT_TYPE]: Headers_1.Headers.APPLICATION_FORM_URLENCODED,
                [Headers_1.Headers.ACCEPT]: Headers_1.Headers.APPLICATION_JSON,
            };
        };
        this.formatCachedToken = () => {
            var _a, _b;
            return `${(_a = TokenService.oAuthResponse) === null || _a === void 0 ? void 0 : _a.tokenType} ${(_b = TokenService.oAuthResponse) === null || _b === void 0 ? void 0 : _b.accessToken}`;
        };
        this.getCurrentTime = () => {
            return Math.floor(Date.now() / 1000);
        };
        this.getOAuthToken = () => __awaiter(this, void 0, void 0, function* () {
            if (this.isCachedTokenValid()) {
                return this.formatCachedToken();
            }
            try {
                yield this.fetchTokenFromPhonePe();
            }
            catch (error) {
                if (TokenService.oAuthResponse == null) {
                    console.warn('No cached token, error occurred while fetching new token', error);
                    throw error;
                }
                console.warn('Returning cached token, error occurred while fetching new token', error);
                this.eventPublisher.send((0, EventBuillder_1.buildOAuthEvent)(this.getCurrentTime(), TokenConstants_1.TokenConstants.OAUTH_GET_TOKEN, EventType_1.EventType.OAUTH_FETCH_FAILED_USED_CACHED_TOKEN, error, TokenService.oAuthResponse.issuedAt, TokenService.oAuthResponse.expiresAt));
            }
            return this.formatCachedToken();
        });
        this.fetchTokenFromPhonePe = (forceRefresh = false) => __awaiter(this, void 0, void 0, function* () {
            yield this.mutex.runExclusive(() => __awaiter(this, void 0, void 0, function* () {
                // if multiple operations call the fetchTokenFromPhonePe, first operations will enter and fetch the token, and the subsequent operations will use the fetched token, avoiding unnecessary api call
                if (forceRefresh != true && this.isCachedTokenValid()) {
                    return;
                }
                const formBody = this.prepareFormBody();
                const url = TokenConstants_1.TokenConstants.OAUTH_GET_TOKEN;
                const response = yield this.httpCommand.request(url, HttpMethodType_1.HttpMethodType.POST, this.prepareRequestHeaders(), formBody);
                TokenService.oAuthResponse = (0, class_transformer_1.plainToClass)(OAuthResponse_1.OAuthResponse, response);
            }));
        });
        this.forceRefreshToken = () => __awaiter(this, void 0, void 0, function* () {
            yield this.fetchTokenFromPhonePe(true);
        });
        this.isCachedTokenValid = () => {
            var _a, _b;
            if (TokenService.oAuthResponse == null) {
                return false;
            }
            const issuedAt = (_a = TokenService.oAuthResponse) === null || _a === void 0 ? void 0 : _a.issuedAt;
            const expireAt = (_b = TokenService.oAuthResponse) === null || _b === void 0 ? void 0 : _b.expiresAt;
            const currentTime = this.getCurrentTime();
            const reloadTime = Math.floor(issuedAt + (expireAt - issuedAt) / 2);
            return currentTime < reloadTime;
        };
        this.prepareFormBody = () => {
            const formBody = {
                client_id: this.credentialConfig.clientId,
                client_secret: this.credentialConfig.clientSecret,
                client_version: this.credentialConfig.clientVersion.toString(),
                grant_type: TokenConstants_1.TokenConstants.OAUTH_GRANT_TYPE,
            };
            return formBody;
        };
        this.credentialConfig = credentialConfig;
        this.httpCommand = new HttpCommand_1.HttpCommand(EnvConfig_1.EnvConfig.getBaseUrls(env).oAuthHostUrl, httpClient);
        this.eventPublisher = eventPublisher;
        this.eventPublisher.send((0, EventBuillder_1.buildInitClientEvent)(EventType_1.EventType.TOKEN_SERVICE_INITIALIZED));
    }
}
exports.TokenService = TokenService;
