{"version": 3, "file": "IsMilitaryTime.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMilitaryTime.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAExC,QAAA,gBAAgB,GAAG,gBAAgB,CAAC;AAEjD;;;GAGG;AACH,SAAgB,cAAc,CAAC,KAAc;IAC3C,MAAM,iBAAiB,GAAG,8BAA8B,CAAC;IACzD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;AACjF,CAAC;AAHD,wCAGC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,iBAAqC;IAClE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,wBAAgB;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC;YACzD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+EAA+E,EAC1G,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,wCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport matchesValidator from 'validator/lib/matches';\n\nexport const IS_MILITARY_TIME = 'isMilitaryTime';\n\n/**\n * Checks if the string represents a time without a given timezone in the format HH:MM (military)\n * If the given value does not match the pattern HH:MM, then it returns false.\n */\nexport function isMilitaryTime(value: unknown): boolean {\n  const militaryTimeRegex = /^([01]\\d|2[0-3]):?([0-5]\\d)$/;\n  return typeof value === 'string' && matchesValidator(value, militaryTimeRegex);\n}\n\n/**\n * Checks if the string represents a time without a given timezone in the format HH:MM (military)\n * If the given value does not match the pattern HH:MM, then it returns false.\n */\nexport function IsMilitaryTime(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MILITARY_TIME,\n      validator: {\n        validate: (value, args): boolean => isMilitaryTime(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid representation of military time in the format HH:MM',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}