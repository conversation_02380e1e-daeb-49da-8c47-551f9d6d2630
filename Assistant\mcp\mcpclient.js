import axios from 'axios';

export class MCPClient {
  constructor(serverUrl, apiKey = null) {
    this.serverUrl = serverUrl;
    this.apiKey = apiKey || process.env.MCP_API_KEY;
    this.timeout = 30000; // 30 seconds timeout
  }

  /**
   * Make a request to the MCP server
   * @param {string} tool - The tool name to call
   * @param {Object} params - Parameters for the tool
   * @param {Object} user - User context (if authenticated)
   * @returns {Promise<Object>} - Tool response
   */
  async callTool(tool, params = {}, user = null) {
    try {
      console.log(`MCP Client calling tool: ${tool}`, { params, user: user?.email });

      const requestData = {
        tool,
        params,
        user,
        timestamp: new Date().toISOString()
      };

      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Belilly-AI-Assistant-Lambda/1.0'
      };

      // Add API key if available
      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      const response = await axios.post(
        `${this.serverUrl}/mcp/tools`,
        requestData,
        {
          headers,
          timeout: this.timeout,
          validateStatus: (status) => status < 500 // Don't throw on 4xx errors
        }
      );

      if (response.status >= 400) {
        console.error(`MCP tool call failed.`);
        return {
          success: false,
          error: response.data?.error || `HTTP ${response.status}`,
          data: null
        };
      }

      console.log(`MCP tool ${tool} completed successfully`);
      return {
        success: true,
        error: null,
        data: response.data
      };

    } catch (error) {
      console.error(`MCP Client error calling tool ${tool}`);
      
      // Handle different types of errors
      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'MCP server is not available',
          data: null
        };
      } else if (error.code === 'ENOTFOUND') {
        return {
          success: false,
          error: 'MCP server URL is invalid',
          data: null
        };
      } else if (error.code === 'ETIMEDOUT') {
        return {
          success: false,
          error: 'MCP server request timed out',
          data: null
        };
      } else {
        return {
          success: false,
          error: error.message || 'Unknown MCP error',
          data: null
        };
      }
    }
  }

  /**
   * Get product information via MCP
   * @param {string} query - Product query
   * @returns {Promise<string>} - Product information
   */
  async getProductInfo(query) {
    const result = await this.callTool('product_info', { query });
    
    if (result.success) {
      return result.data?.productInfo || 'No product information available.';
    } else {
      console.error('Failed to get product info via MCP');
      return 'I apologize, but I cannot access product information at the moment. Please try again later.';
    }
  }

  /**
   * Get order information via MCP
   * @param {string} query - Order query
   * @param {Object} user - User context
   * @returns {Promise<string>} - Order information
   */
  async getOrderInfo(query, user) {
    if (!user || !user.email) {
      return 'You need to be logged in to access your order information.';
    }

    const result = await this.callTool('order_info', { query }, user);
    
    if (result.success) {
      return result.data?.orderInfo || 'No order information found.';
    } else {
      console.error('Failed to get order info via MCP');
      return 'I apologize, but I cannot access your order information at the moment. Please try again later.';
    }
  }

  /**
   * Get user information via MCP
   * @param {string} query - User query
   * @param {Object} user - User context
   * @returns {Promise<string>} - User information
   */
  async getUserInfo(query, user) {
    if (!user || !user.email) {
      return 'You need to be logged in to access your account information.';
    }

    const result = await this.callTool('user_info', { query }, user);
    
    if (result.success) {
      return result.data?.userInfo || 'No user information found.';
    } else {
      console.error('Failed to get user info via MCP');
      return 'I apologize, but I cannot access your account information at the moment. Please try again later.';
    }
  }

  /**
   * Get FAQ information via MCP
   * @param {string} query - FAQ query
   * @returns {Promise<string>} - FAQ information
   */
  async getFAQInfo(query) {
    const result = await this.callTool('faq_info', { query });
    
    if (result.success) {
      return result.data?.faqInfo || 'No FAQ information available.';
    } else {
      console.error('Failed to get FAQ info via MCP');
      return 'I apologize, but I cannot access FAQ information at the moment. Please try again later.';
    }
  }

  /**
   * Health check for MCP server
   * @returns {Promise<boolean>} - Whether MCP server is healthy
   */
  async healthCheck() {
    try {
      const response = await axios.get(`${this.serverUrl}/mcp/health`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Belilly-AI-Assistant-Lambda/1.0'
        }
      });
      
      return response.status === 200;
    } catch (error) {
      console.error('MCP health check failed');
      return false;
    }
  }
}

export default MCPClient;
