import { BaseClient } from '../../common/BaseClient';
import { Env } from '../../Env';
import { StandardCheckoutPayRequest } from './models/request/StandardCheckoutPayRequest';
import { OrderStatusResponse } from '../../common/models/response/OrderStatusResponse';
import { StandardCheckoutPayResponse } from './models/response/StandardCheckoutPayResponse';
import { RefundRequest } from '../../common/models/request/RefundRequest';
import { RefundResponse } from '../../common/models/response/RefundResponse';
import { RefundStatusResponse } from '../../common/models/response/RefundStatusResponse';
import { CallbackResponse } from '../../common/models/response/CallbackResponse';
import { CreateSdkOrderRequest } from './models/request/CreateSdkOrderRequest';
import { CreateSdkOrderResponse } from './models/response/CreateSdkOrderResponse';
export declare class StandardCheckoutClient extends BaseClient {
    private static _client;
    private headers;
    private constructor();
    /**
     * Generates a StandardCheckout Client for interacting with the PhonePe APIs
     *
     * @param clientId      Unique clientId assigned to merchant by PhonePe
     * @param clientSecret  Secret provided by PhonePe
     * @param clientVersion The client version used for secure transactions
     * @param env           Set to `Env.SANDBOX` for the SANDBOX environment  or `Env.PRODUCTION` for the production
     *                      environment.
     * @param shouldPublishEvents When true, events are sent to PhonePe providing smoother experience
     * @return StandardCheckoutClient object for interacting with the PhonePe APIs
     */
    static getInstance: (clientId: string, clientSecret: string, clientVersion: number, env: Env, shouldPublishEvents?: boolean) => StandardCheckoutClient;
    /**
     * Initiate a pay order for Standard Checkout
     *
     * @param payRequest Request required to initiate the order. It is build using StandardCheckoutPayRequest.builder()
     * @return Promise<StandardCheckoutPayResponse> which contains the url for payment gateway
     */
    pay: (payRequest: StandardCheckoutPayRequest) => Promise<StandardCheckoutPayResponse>;
    /**
     * Gets the status of the order
     *
     * @param merchantOrderId Generated by the merchant at the time of initiating the order
     * @param details         true -> order status has all attempt details under paymentDetails list
     * false -> order status has only latest attempt details under paymentDetails list
     * @return Promise<OrderStatusResponse> which contains the details about the order
     */
    getOrderStatus: (merchantOrderId: string, details?: boolean) => Promise<OrderStatusResponse>;
    /**
     * Initiate a refund of an order which is in completed state
     *
     * @param refundRequest Request required to initiate the order. It is build using RefundRequest.builder()
     * @return Promise<RefundResponse> which contains the details about the refund
     */
    refund: (refundRequest: RefundRequest) => Promise<RefundResponse>;
    /**
     * Gets the status of refund
     *
     * @param refundId Generated by merchant at the time of initiating the refund
     * @return Promise<RefundStatusResponse> which contains the status about the refund
     */
    getRefundStatus: (refundId: string) => Promise<RefundStatusResponse>;
    /**
     * Gets the status of a transaction attempted
     *
     * @param transactionId Transaction attempt id generated by PhonePe
     * @return Promise<OrderStatusResponse> which contains the details about that specific transactionId
     */
    getTransactionStatus: (transactionId: string) => Promise<OrderStatusResponse>;
    /**
     * Create order token for SDK integrated order requests
     *
     * @param sdkRequest Request object build using CreateSdkOrderRequest.builder()
     * @return Promise<CreateSdkOrderResponse> which contains token details to be consumed by the UI
     */
    createSdkOrder: (sdkRequest: CreateSdkOrderRequest) => Promise<CreateSdkOrderResponse>;
    /**
     * Validate if the callback is valid
     *
     * @param username      username set by the merchant on the dashboard
     * @param password      password set by the merchant on the dashboard
     * @param authorization String data under `authorization` key of response headers
     * @param responseBody  Callback response body
     * @return CallbackResponse Deserialized callback body
     * @throws PhonePeException when callback is not valid
     */
    validateCallback: (username: string, password: string, authorization: string, responseBody: string) => CallbackResponse;
    prepareHeaders: () => {
        [x: string]: string;
    };
}
