{"version": 3, "file": "IsIdentityCard.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsIdentityCard.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kFAAmE;AAGtD,QAAA,gBAAgB,GAAG,gBAAgB,CAAC;AAEjD;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,KAAc,EAAE,MAAsC;IACnF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,wBAAuB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC;AAFD,wCAEC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,MAAuC,EACvC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,wBAAgB;QACtB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0CAA0C,EACrE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,wCAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIdentityCardValidator from 'validator/lib/isIdentityCard';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_IDENTITY_CARD = 'isIdentityCard';\n\n/**\n * Check if the string is a valid identity card code.\n * locale is one of ['ES', 'zh-TW', 'he-IL', 'ar-TN'] OR 'any'. If 'any' is used, function will check if any of the locals match.\n * Defaults to 'any'.\n * If given value is not a string, then it returns false.\n */\nexport function isIdentityCard(value: unknown, locale: ValidatorJS.IdentityCardLocale): boolean {\n  return typeof value === 'string' && isIdentityCardValidator(value, locale);\n}\n\n/**\n * Check if the string is a valid identity card code.\n * locale is one of ['ES', 'zh-TW', 'he-IL', 'ar-TN'] OR 'any'. If 'any' is used, function will check if any of the locals match.\n * Defaults to 'any'.\n * If given value is not a string, then it returns false.\n */\nexport function IsIdentityCard(\n  locale?: ValidatorJS.IdentityCardLocale,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_IDENTITY_CARD,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isIdentityCard(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a identity card number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}