{"version": 3, "file": "IsIP.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsIP.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,aAAa,MAAM,oBAAoB,CAAC;AAI/C,MAAM,CAAC,IAAM,KAAK,GAAG,MAAM,CAAC;AAE5B;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,KAAc,EAAE,OAAqB;IACxD,+EAA+E;IAC/E,IAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAE,UAAG,OAAO,CAAgB,CAAC,CAAC,CAAC,SAAS,CAAC;IACrE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,aAAa,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACvE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,OAAqB,EAAE,iBAAqC;IAC/E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,KAAK;QACX,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,IAAI,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAjC,CAAiC;YACrE,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,iCAAiC,EAA9C,CAA8C,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIPValidator from 'validator/lib/isIP';\n\nexport type IsIpVersion = '4' | '6' | 4 | 6;\n\nexport const IS_IP = 'isIp';\n\n/**\n * Checks if the string is an IP (version 4 or 6).\n * If given value is not a string, then it returns false.\n */\nexport function isIP(value: unknown, version?: IsIpVersion): boolean {\n  /* eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion */\n  const versionStr = version ? (`${version}` as '4' | '6') : undefined;\n  return typeof value === 'string' && isIPValidator(value, versionStr);\n}\n\n/**\n * Checks if the string is an IP (version 4 or 6).\n * If given value is not a string, then it returns false.\n */\nexport function IsIP(version?: IsIpVersion, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_IP,\n      constraints: [version],\n      validator: {\n        validate: (value, args): boolean => isIP(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an ip address', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}