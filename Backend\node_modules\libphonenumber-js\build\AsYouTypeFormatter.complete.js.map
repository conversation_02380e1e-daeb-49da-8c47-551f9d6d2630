{"version": 3, "file": "AsYouTypeFormatter.complete.js", "names": ["formatCompleteNumber", "state", "format", "metadata", "shouldTryNationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "matcher", "RegExp", "pattern", "test", "nationalSignificantNumber", "formatNationalNumberWithAndWithoutNationalPrefixFormattingRule", "canFormatCompleteNumber", "checkNumberLength", "international", "nationalPrefix", "carrierCode", "formattedNumber", "formatNationalNumber", "useNationalPrefixFormattingRule", "formattedNationalNumber", "formatNationalNumberUsingFormat", "useInternationalFormat", "withNationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "isValidFormattedNationalNumber", "parseDigits", "getNationalDigits"], "sources": ["../source/AsYouTypeFormatter.complete.js"], "sourcesContent": ["import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\n\r\nexport default function formatCompleteNumber(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tconst matcher = new RegExp(`^(?:${format.pattern()})$`)\r\n\tif (matcher.test(state.nationalSignificantNumber)) {\r\n\t\treturn formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(\r\n\t\t\tstate,\r\n\t\t\tformat,\r\n\t\t\t{\r\n\t\t\t\tmetadata,\r\n\t\t\t\tshouldTryNationalPrefixFormattingRule,\r\n\t\t\t\tgetSeparatorAfterNationalPrefix\r\n\t\t\t}\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\r\n\treturn checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE'\r\n}\r\n\r\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\t// `format` has already been checked for `nationalPrefix` requirement.\r\n\r\n\tconst {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode\r\n\t} = state\r\n\r\n\t// Format the number with using `national_prefix_formatting_rule`.\r\n\t// If the resulting formatted number is a valid formatted number, then return it.\r\n\t//\r\n\t// Google's AsYouType formatter is different in a way that it doesn't try\r\n\t// to format using the \"national prefix formatting rule\", and instead it\r\n\t// simply prepends a national prefix followed by a \" \" character.\r\n\t// This code does that too, but as a fallback.\r\n\t// The reason is that \"national prefix formatting rule\" may use parentheses,\r\n\t// which wouldn't be included has it used the simpler Google's way.\r\n\t//\r\n\tif (shouldTryNationalPrefixFormattingRule(format)) {\r\n\t\tconst formattedNumber = formatNationalNumber(state, format, {\r\n\t\t\tuseNationalPrefixFormattingRule: true,\r\n\t\t\tgetSeparatorAfterNationalPrefix,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\tif (formattedNumber) {\r\n\t\t\treturn formattedNumber\r\n\t\t}\r\n\t}\r\n\r\n\t// Format the number without using `national_prefix_formatting_rule`.\r\n\treturn formatNationalNumber(state, format, {\r\n\t\tuseNationalPrefixFormattingRule: false,\r\n\t\tgetSeparatorAfterNationalPrefix,\r\n\t\tmetadata\r\n\t})\r\n}\r\n\r\nfunction formatNationalNumber(state, format, {\r\n\tmetadata,\r\n\tuseNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tlet formattedNationalNumber = formatNationalNumberUsingFormat(\r\n\t\tstate.nationalSignificantNumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tcarrierCode: state.carrierCode,\r\n\t\t\tuseInternationalFormat: state.international,\r\n\t\t\twithNationalPrefix: useNationalPrefixFormattingRule,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n\tif (!useNationalPrefixFormattingRule) {\r\n\t\tif (state.nationalPrefix) {\r\n\t\t\t// If a national prefix was extracted, then just prepend it,\r\n\t\t\t// followed by a \" \" character.\r\n\t\t\tformattedNationalNumber = state.nationalPrefix +\r\n\t\t\t\tgetSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t} else if (state.complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\tformattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber +\r\n\t\t\t\t' ' +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t}\r\n\t}\r\n\tif (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n}\r\n\r\n// Check that the formatted phone number contains exactly\r\n// the same digits that have been input by the user.\r\n// For example, when \"0111523456789\" is input for `AR` country,\r\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\r\n// which means that the national part of `this.digits` isn't simply equal to\r\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\r\n//\r\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\r\n// being formatted via `metadata[country].national_prefix_transform_rule`.\r\n// For example, for `VI` country, it prepends `340` to the national number,\r\n// and if this check hasn't been implemented, then there would be a bug\r\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n//\r\n// So, all these \"gotchas\" are filtered out.\r\n//\r\n// In the original Google's code, the comments say:\r\n// \"Check that we didn't remove nor add any extra digits when we matched\r\n// this formatting pattern. This usually happens after we entered the last\r\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\r\n// formatted but AYTF should retain all the number entered and not change\r\n// in order to match a format (of same leading digits and length) display\r\n// in that way.\"\r\n// \"If it's the same (i.e entered number and format is same), then it's\r\n// safe to return this in formatted number as nothing is lost / added.\"\r\n// Otherwise, don't use this format.\r\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\r\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\r\n//\r\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\r\n\treturn parseDigits(formattedNationalNumber) === state.getNationalDigits()\r\n}"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;;;AAEe,SAASA,oBAAT,CAA8BC,KAA9B,EAAqCC,MAArC,QAIZ;EAAA,IAHFC,QAGE,QAHFA,QAGE;EAAA,IAFFC,qCAEE,QAFFA,qCAEE;EAAA,IADFC,+BACE,QADFA,+BACE;EACF,IAAMC,OAAO,GAAG,IAAIC,MAAJ,eAAkBL,MAAM,CAACM,OAAP,EAAlB,QAAhB;;EACA,IAAIF,OAAO,CAACG,IAAR,CAAaR,KAAK,CAACS,yBAAnB,CAAJ,EAAmD;IAClD,OAAOC,8DAA8D,CACpEV,KADoE,EAEpEC,MAFoE,EAGpE;MACCC,QAAQ,EAARA,QADD;MAECC,qCAAqC,EAArCA,qCAFD;MAGCC,+BAA+B,EAA/BA;IAHD,CAHoE,CAArE;EASA;AACD;;AAEM,SAASO,uBAAT,CAAiCF,yBAAjC,EAA4DP,QAA5D,EAAsE;EAC5E,OAAO,IAAAU,6BAAA,EAAkBH,yBAAlB,EAA6CP,QAA7C,MAA2D,aAAlE;AACA;;AAED,SAASQ,8DAAT,CAAwEV,KAAxE,EAA+EC,MAA/E,SAIG;EAAA,IAHFC,QAGE,SAHFA,QAGE;EAAA,IAFFC,qCAEE,SAFFA,qCAEE;EAAA,IADFC,+BACE,SADFA,+BACE;EACF;EAEA,IACCK,yBADD,GAKIT,KALJ,CACCS,yBADD;EAAA,IAECI,aAFD,GAKIb,KALJ,CAECa,aAFD;EAAA,IAGCC,cAHD,GAKId,KALJ,CAGCc,cAHD;EAAA,IAICC,WAJD,GAKIf,KALJ,CAICe,WAJD,CAHE,CAUF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIZ,qCAAqC,CAACF,MAAD,CAAzC,EAAmD;IAClD,IAAMe,eAAe,GAAGC,oBAAoB,CAACjB,KAAD,EAAQC,MAAR,EAAgB;MAC3DiB,+BAA+B,EAAE,IAD0B;MAE3Dd,+BAA+B,EAA/BA,+BAF2D;MAG3DF,QAAQ,EAARA;IAH2D,CAAhB,CAA5C;;IAKA,IAAIc,eAAJ,EAAqB;MACpB,OAAOA,eAAP;IACA;EACD,CA7BC,CA+BF;;;EACA,OAAOC,oBAAoB,CAACjB,KAAD,EAAQC,MAAR,EAAgB;IAC1CiB,+BAA+B,EAAE,KADS;IAE1Cd,+BAA+B,EAA/BA,+BAF0C;IAG1CF,QAAQ,EAARA;EAH0C,CAAhB,CAA3B;AAKA;;AAED,SAASe,oBAAT,CAA8BjB,KAA9B,EAAqCC,MAArC,SAIG;EAAA,IAHFC,QAGE,SAHFA,QAGE;EAAA,IAFFgB,+BAEE,SAFFA,+BAEE;EAAA,IADFd,+BACE,SADFA,+BACE;EACF,IAAIe,uBAAuB,GAAG,IAAAC,2CAAA,EAC7BpB,KAAK,CAACS,yBADuB,EAE7BR,MAF6B,EAG7B;IACCc,WAAW,EAAEf,KAAK,CAACe,WADpB;IAECM,sBAAsB,EAAErB,KAAK,CAACa,aAF/B;IAGCS,kBAAkB,EAAEJ,+BAHrB;IAIChB,QAAQ,EAARA;EAJD,CAH6B,CAA9B;;EAUA,IAAI,CAACgB,+BAAL,EAAsC;IACrC,IAAIlB,KAAK,CAACc,cAAV,EAA0B;MACzB;MACA;MACAK,uBAAuB,GAAGnB,KAAK,CAACc,cAAN,GACzBV,+BAA+B,CAACH,MAAD,CADN,GAEzBkB,uBAFD;IAGA,CAND,MAMO,IAAInB,KAAK,CAACuB,4CAAV,EAAwD;MAC9DJ,uBAAuB,GAAGnB,KAAK,CAACuB,4CAAN,GACzB,GADyB,GAEzBJ,uBAFD;IAGA;EACD;;EACD,IAAIK,8BAA8B,CAACL,uBAAD,EAA0BnB,KAA1B,CAAlC,EAAoE;IACnE,OAAOmB,uBAAP;EACA;AACD,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,8BAAT,CAAwCL,uBAAxC,EAAiEnB,KAAjE,EAAwE;EACvE,OAAO,IAAAyB,uBAAA,EAAYN,uBAAZ,MAAyCnB,KAAK,CAAC0B,iBAAN,EAAhD;AACA"}