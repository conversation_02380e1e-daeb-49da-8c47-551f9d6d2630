{"data":{},"environment":"production","errorType":"UNKNOWN_ERROR","ip":"::1","isOperational":false,"level":"error","message":"Not allowed by CORS","method":"POST","path":"/v1/redirect/purchases/status","requestId":"63deb9d9-a6c3-4f6c-b9c0-35e85219268d","service":"belilly-api","stack":"Error: Not allowed by CORS\n    at origin (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/server.js:186:16)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:212:5","statusCode":500,"timestamp":"2025-06-21 13:45:10.223","userId":"anonymous"}
{"environment":"production","level":"error","message":"CRITICAL ERROR - Consider restarting the process","service":"belilly-api","timestamp":"2025-06-21 13:45:10.453"}
{"data":{},"environment":"production","errorType":"UNKNOWN_ERROR","ip":"::1","isOperational":false,"level":"error","message":"Not allowed by CORS","method":"OPTIONS","path":"/.well-known/appspecific/com.chrome.devtools.json","requestId":"9dafaa13-46cb-49b8-9b13-b69e3855a9bd","service":"belilly-api","stack":"Error: Not allowed by CORS\n    at origin (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/server.js:186:16)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\Belilly (mern)\\Backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:212:5","statusCode":500,"timestamp":"2025-06-21 13:45:12.270","userId":"anonymous"}
{"environment":"production","level":"error","message":"CRITICAL ERROR - Consider restarting the process","service":"belilly-api","timestamp":"2025-06-21 13:45:12.284"}
