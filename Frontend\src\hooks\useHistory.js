// useHistory.js

import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { updateWatchHistory, updateSearchHistory, clearSearchHistory, fetchSearchHistory } from '../app/history/historySlice';
import { selectSearchHistoryData } from '../app/history/historySelector';
import { selectUserProfile } from '../app/users/usersSelector';
import Cookies from 'js-cookie';
import secureStorage from '../utils/secureStorage';
import consentManager from '../utils/consentManager';
import { COOKIE_CATEGORIES } from '../components/CookieConsent';

const MAX_SEARCH_HISTORY = 5;
// Storage keys for search history
const SEARCH_HISTORY_KEY = 'searchHistory';
const SYNCED_HISTORY_KEY = 'syncedSearchHistory';
const WATCH_HISTORY_COOKIE = 'recentlyViewedDetails';

// Helper function to check if two arrays have the same items (regardless of order)
const arraysHaveSameItems = (array1, array2) => {
  if (!array1 || !array2) return false;
  if (array1.length !== array2.length) return false;

  // Create a copy of array2 to modify
  const array2Copy = [...array2];

  // Check if every item in array1 exists in array2Copy
  return array1.every(item => {
    const index = array2Copy.findIndex(i => i === item);
    if (index === -1) return false;

    // Remove the item from array2Copy to handle duplicates correctly
    array2Copy.splice(index, 1);
    return true;
  });
};

const useHistory = () => {
  const dispatch = useDispatch();
  const user = useSelector(selectUserProfile);

  // Watch History Functions
  const updateWatchHistoryInDB = useCallback(async (productDetails) => {
    // Check if we have consent for functional cookies (watch history)
    if (!consentManager.hasConsent(COOKIE_CATEGORIES.FUNCTIONAL)) {
      // Watch history disabled due to cookie preferences
      return false;
    }

    if (!productDetails || !Array.isArray(productDetails) || productDetails.length === 0) {
      console.warn("Product details array is empty or invalid");
      return false;
    }

    try {
      // Check if user is authenticated using Redux state
      if (!user) {
        // User not authenticated, skipping watch history update
        return false;
      }

      // Dispatch the action to update watch history in the database
      await dispatch(updateWatchHistory(productDetails)).unwrap();
      // Watch history updated in database

      // Clear the cookie after successful update
      Cookies.set(WATCH_HISTORY_COOKIE, JSON.stringify([]), { expires: 30 });

      return true;
    } catch (err) {
      // Handle different types of errors
      if (err.name === 'AbortError') {
        console.warn("Request to update watch history was aborted");
      } else if (err.message && err.message.includes('Redis')) {
        console.warn("Redis service unavailable, will try again later");
      } else {
        console.error("Error updating watch history:", err);
      }

      // Don't clear the cookie on error so we can retry later
      return false;
    }
  }, [dispatch]);

  // Search History Functions
  const searchHistoryFromRedux = useSelector(selectSearchHistoryData);

  const getSearchHistory = useCallback(async () => {
    // Check if we have consent for functional cookies (search history)
    if (!consentManager.hasConsent(COOKIE_CATEGORIES.FUNCTIONAL)) {
      // Search history disabled due to cookie preferences
      return [];
    }

    // First check if we have data in Redux
    if (searchHistoryFromRedux && searchHistoryFromRedux.length > 0) {
      return searchHistoryFromRedux;
    }

    // Otherwise, check secure storage
    try {
      const savedHistory = secureStorage.getTemporary(SEARCH_HISTORY_KEY);
      if (savedHistory && savedHistory.length > 0) {
        return savedHistory;
      }

      // If cookies are empty and user is logged in, fetch from database
      if (user) {
        try {
          // Dispatch the action to fetch search history from the database
          const result = await dispatch(fetchSearchHistory()).unwrap();
          console.log("Fetched search history from database:", result);

          // If we got data back, update secure storage and return the data
          if (result.data && result.data.searches) {
            const keywords = result.data.searches.map(item => item.keyword);
            // Only update storage if we actually got keywords
            if (keywords.length > 0) {
              secureStorage.setTemporary(SEARCH_HISTORY_KEY, keywords);
              return keywords;
            }
          }
        } catch (err) {
          console.error("Error fetching search history from database:", err);
        }
      }

      // If all else fails, return empty array
      return [];
    } catch (error) {
      console.error('Error loading search history from storage:', error);
      return [];
    }
  }, [searchHistoryFromRedux, dispatch]);

  // Get the synced search history (the last set of keywords that was synced with the backend)
  const getSyncedHistory = useCallback(() => {
    try {
      const syncedHistory = secureStorage.getTemporary(SYNCED_HISTORY_KEY, []);
      return syncedHistory;
    } catch (error) {
      console.error('Error loading synced search history from storage:', error);
      return [];
    }
  }, []);

  const saveSearchTerm = useCallback(async (term) => {
    if (!term || !term.trim()) return [];

    // Check if we have consent for functional cookies (search history)
    if (!consentManager.hasConsent(COOKIE_CATEGORIES.FUNCTIONAL)) {
      // Search history storage disabled due to cookie preferences
      return [];
    }

    try {
      // Get current search history from secure storage, Redux, or database
      const currentHistory = await getSearchHistory();

      // Create a new array with the new term at the beginning
      // Remove any duplicates of the term
      let updatedHistory = [term, ...currentHistory.filter(item => item !== term)];

      // Limit to MAX_SEARCH_HISTORY items
      if (updatedHistory.length > MAX_SEARCH_HISTORY) {
        updatedHistory = updatedHistory.slice(0, MAX_SEARCH_HISTORY);
      }

      // Save to secure storage
      secureStorage.setTemporary(SEARCH_HISTORY_KEY, updatedHistory);

      // If user is logged in and we have MAX_SEARCH_HISTORY terms, check if we need to sync with database
      if (user && updatedHistory.length >= MAX_SEARCH_HISTORY) {
        // Get the previously synced history
        const syncedHistory = getSyncedHistory();

        // Check if the current history is different from the synced history
        const needsSync = !arraysHaveSameItems(updatedHistory, syncedHistory);

        if (needsSync) {
          try {
            // Syncing search history with database

            // Dispatch the Redux action to update search history in the database
            const searchTerms = updatedHistory.map(term => term.trim());
            await dispatch(updateSearchHistory(searchTerms)).unwrap();

            // Save the current history as the synced history
            secureStorage.setTemporary(SYNCED_HISTORY_KEY, updatedHistory);

            // Search history synced with database
          } catch (error) {
            console.error('Error syncing search history with database:', error);
          }
        } else {
          // Search history already synced with database. Skipping sync.
        }
      }

      return updatedHistory;
    } catch (error) {
      console.error('Error saving search history to storage:', error);
      return null;
    }
  }, [getSearchHistory]);

  // Clear search history function
  const clearSearchTerms = useCallback(async () => {
    try {
      // Clear secure storage first
      secureStorage.setTemporary(SEARCH_HISTORY_KEY, []);
      secureStorage.setTemporary(SYNCED_HISTORY_KEY, []);

      // If user is logged in, clear from database using Redux
      if (user) {
        try {
          await dispatch(clearSearchHistory()).unwrap();
          // Search history cleared from database
        } catch (err) {
          console.error('Error clearing search history from database:', err);
          // Even if the database clear fails, we still want to clear the local storage
        }
      }

      return [];
    } catch (error) {
      console.error('Error clearing search history:', error);
      return [];
    }
  }, [dispatch]);

  return {
    // Watch history functions
    updateWatchHistoryInDB,

    // Search history functions
    getSearchHistory,
    getSyncedHistory,
    saveSearchTerm,
    clearSearchTerms
  };
};

export default useHistory;
