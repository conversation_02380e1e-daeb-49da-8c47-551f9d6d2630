import { ValidationOptions } from '../ValidationOptions';
export declare const MAX = "max";
/**
 * Checks if the first number is less than or equal to the second.
 */
export declare function max(num: unknown, max: number): boolean;
/**
 * Checks if the value is less than or equal to the allowed maximum value.
 */
export declare function Max(maxValue: number, validationOptions?: ValidationOptions): PropertyDecorator;
