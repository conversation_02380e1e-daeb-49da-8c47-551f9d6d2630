{"version": 3, "file": "parse.test.js", "names": ["metadata", "type", "_parseNumber", "<PERSON><PERSON><PERSON>", "parseNumber", "parameters", "push", "apply", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "describe", "it", "should", "deep", "equal", "defaultCountry", "country", "phone", "extended", "countryCallingCode", "carrierCode", "undefined", "ext", "valid", "possible", "thrower", "defaultCallingCode", "expect", "to", "be", "v2"], "sources": ["../../source/legacy/parse.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport _parseNumber from './parse.js'\r\nimport Metadata from '../metadata.js'\r\n\r\nfunction parseNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _parseNumber.apply(this, parameters)\r\n}\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\ndescribe('parse', () => {\r\n\tit('should not parse invalid phone numbers', () => {\r\n\t\t// Too short.\r\n\t\tparseNumber('+7 (800) 55-35-35').should.deep.equal({})\r\n\t\t// Too long.\r\n\t\tparseNumber('+7 (800) 55-35-35-55').should.deep.equal({})\r\n\r\n\t\tparseNumber('+7 (800) 55-35-35', 'US').should.deep.equal({})\r\n\t\tparseNumber('(800) 55 35 35', { defaultCountry: 'RU' }).should.deep.equal({})\r\n\t\tparseNumber('****** 215 5230', 'US').should.deep.equal({})\r\n\r\n\t\tparseNumber('911231231', 'BE').should.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse valid phone numbers', () => {\r\n\t\t// Instant loans\r\n\t\t// https://www.youtube.com/watch?v=6e1pMrYH5jI\r\n\t\t//\r\n\t\t// Restrict to RU\r\n\t\tparseNumber('Phone: 8 (800) 555 35 35.', 'RU').should.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// International format\r\n\t\tparseNumber('Phone: +7 (800) 555-35-35.').should.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// // Restrict to US, but not a US country phone code supplied\r\n\t\t// parseNumber('+7 (800) 555-35-35', 'US').should.deep.equal({})\r\n\t\t// Restrict to RU\r\n\t\tparseNumber('(800) 555 35 35', 'RU').should.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// Default to RU\r\n\t\tparseNumber('8 (800) 555 35 35', { defaultCountry: 'RU' }).should.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\r\n\t\t// Gangster partyline\r\n\t\tparseNumber('******-373-4253').should.deep.equal({ country: 'US', phone: '2133734253' })\r\n\r\n\t\t// Switzerland (just in case)\r\n\t\tparseNumber('044 668 18 00', 'CH').should.deep.equal({ country: 'CH', phone: '446681800' })\r\n\r\n\t\t// China, Beijing\r\n\t\tparseNumber('010-852644821', 'CN').should.deep.equal({ country: 'CN', phone: '10852644821' })\r\n\r\n\t\t// France\r\n\t\tparseNumber('+33169454850').should.deep.equal({ country: 'FR', phone: '169454850' })\r\n\r\n\t\t// UK (Jersey)\r\n\t\tparseNumber('+44 7700 300000').should.deep.equal({ country: 'JE', phone: '7700300000' })\r\n\r\n\t\t// KZ\r\n\t\tparseNumber('****** 211 1111').should.deep.equal({ country: 'KZ', phone: '7022111111' })\r\n\r\n\t\t// Brazil\r\n\t\tparseNumber('11987654321', 'BR').should.deep.equal({ country: 'BR', phone: '11987654321' })\r\n\r\n\t\t// Long country phone code.\r\n\t\tparseNumber('+212659777777').should.deep.equal({ country: 'MA', phone: '659777777' })\r\n\r\n\t\t// No country could be derived.\r\n\t\t// parseNumber('+212569887076').should.deep.equal({ countryPhoneCode: '212', phone: '569887076' })\r\n\r\n\t\t// GB. Moible numbers starting 07624* are Isle of Man.\r\n\t\tparseNumber('07624369230', 'GB').should.deep.equal({ country: 'IM', phone: '7624369230' })\r\n\t})\r\n\r\n\tit('should parse possible numbers', () => {\r\n\t\t// Invalid phone number for a given country.\r\n\t\tparseNumber('1112223344', 'RU', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : 'RU',\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '1112223344',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// International phone number.\r\n\t\t// Several countries with the same country phone code.\r\n\t\tparseNumber('+71112223344').should.deep.equal({})\r\n\t\tparseNumber('+71112223344', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : undefined,\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '1112223344',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// International phone number.\r\n\t\t// Single country with the given country phone code.\r\n\t\tparseNumber('+33011222333', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : 'FR',\r\n\t\t\tcountryCallingCode : '33',\r\n\t\t\tphone              : '011222333',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// Too short.\r\n\t\t// Won't strip national prefix `8` because otherwise the number would be too short.\r\n\t\tparseNumber('+7 (800) 55-35-35', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : 'RU',\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '800553535',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : false\r\n\t\t})\r\n\r\n\t\t// Too long.\r\n\t\tparseNumber('****** 37342530', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : undefined,\r\n\t\t\tcountryCallingCode : '1',\r\n\t\t\tphone              : '21337342530',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : false\r\n\t\t})\r\n\r\n\t\t// No national number to be parsed.\r\n\t\tparseNumber('+996', { extended: true }).should.deep.equal({\r\n\t\t\t// countryCallingCode : '996'\r\n\t\t})\r\n\r\n\t\t// Valid number.\r\n\t\tparseNumber('+78005553535', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : 'RU',\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '8005553535',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : true,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/211\r\n\t\tparseNumber('+966', { extended: true }).should.deep.equal({})\r\n\t\tparseNumber('+9664', { extended: true }).should.deep.equal({})\r\n\t\tparseNumber('+96645', { extended: true }).should.deep.equal({\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\tphone              : '45',\r\n\t\t\text                : undefined,\r\n\t\t\tcountry            : 'SA',\r\n\t\t\tcountryCallingCode : '966',\r\n\t\t\tpossible           : false,\r\n\t\t\tvalid              : false\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse non-European digits', () => {\r\n\t\tparseNumber('+١٢١٢٢٣٢٣٢٣٢').should.deep.equal({ country: 'US', phone: '2122323232' })\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\tparseNumber('').should.deep.equal({})\r\n\r\n\t\t// No country phone code\r\n\t\tparseNumber('+').should.deep.equal({})\r\n\r\n\t\t// No country at all (non international number and no explicit country code)\r\n\t\tparseNumber('123').should.deep.equal({})\r\n\r\n\t\t// No country metadata for this `require` country code\r\n\t\tthrower = () => parseNumber('123', 'ZZ')\r\n\t\tthrower.should.throw('Unknown country')\r\n\r\n\t\t// No country metadata for this `default` country code\r\n\t\tthrower = () => parseNumber('123', { defaultCountry: 'ZZ' })\r\n\t\tthrower.should.throw('Unknown country')\r\n\r\n\t\t// Invalid country phone code\r\n\t\tparseNumber('+210').should.deep.equal({})\r\n\r\n\t\t// Invalid country phone code (extended parsing mode)\r\n\t\tparseNumber('+210', { extended: true }).should.deep.equal({})\r\n\r\n\t\t// Too short of a number.\r\n\t\tparseNumber('1', 'US', { extended: true }).should.deep.equal({})\r\n\r\n\t\t// Too long of a number.\r\n\t\tparseNumber('1111111111111111111', 'RU', { extended: true }).should.deep.equal({})\r\n\r\n\t\t// Not a number.\r\n\t\tparseNumber('abcdefg', 'US', { extended: true }).should.deep.equal({})\r\n\r\n\t\t// Country phone code beginning with a '0'\r\n\t\tparseNumber('+0123').should.deep.equal({})\r\n\r\n\t\t// Barbados NANPA phone number\r\n\t\tparseNumber('+12460000000').should.deep.equal({ country: 'BB', phone: '2460000000' })\r\n\r\n\t\t// // A case when country (restricted to) is not equal\r\n\t\t// // to the one parsed out of an international number.\r\n\t\t// parseNumber('******-373-4253', 'RU').should.deep.equal({})\r\n\r\n\t\t// National (significant) number too short\r\n\t\tparseNumber('2', 'US').should.deep.equal({})\r\n\r\n\t\t// National (significant) number too long\r\n\t\tparseNumber('222222222222222222', 'US').should.deep.equal({})\r\n\r\n\t\t// No `national_prefix_for_parsing`\r\n\t\tparseNumber('41111', 'AC').should.deep.equal({ country: 'AC', phone: '41111'})\r\n\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/235\r\n\t\t// `matchesEntirely()` bug fix.\r\n\t\tparseNumber('+4915784846111‬').should.deep.equal({ country: 'DE', phone: '15784846111' })\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => _parseNumber('')\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => parseNumber(2141111111, 'US')\r\n\t\tthrower.should.throw('A text for parsing must be a string.')\r\n\r\n\t\t// Input string too long.\r\n\t\tparseNumber('8005553535                                                                                                                                                                                                                                                 ', 'RU').should.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse phone number extensions', () => {\r\n\t\t// \"ext\"\r\n\t\tparseNumber('2134567890 ext 123', 'US').should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// \"ext.\"\r\n\t\tparseNumber('+12134567890 ext. 12345', 'US').should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '12345'\r\n\t\t})\r\n\r\n\t\t// \"доб.\"\r\n\t\tparseNumber('+78005553535 доб. 1234', 'RU').should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// \"#\"\r\n\t\tparseNumber('+12134567890#1234').should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// \"x\"\r\n\t\tparseNumber('+78005553535 x1234').should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// Not a valid extension\r\n\t\tparseNumber('2134567890 ext. abc', 'US').should.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse RFC 3966 phone numbers', () => {\r\n\t\tparseNumber('tel:+78005553535;ext=123').should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// Should parse \"visual separators\".\r\n\t\tparseNumber('tel:+7(800)555-35.35;ext=123').should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// Invalid number.\r\n\t\tparseNumber('tel:+7x8005553535;ext=123').should.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse invalid international numbers even if they are invalid', () => {\r\n\t\tparseNumber('+7(8)8005553535', 'RU').should.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse carrier codes', () => {\r\n\t\tparseNumber('0 15 21 5555-5555', 'BR', { extended: true }).should.deep.equal({\r\n\t\t\tcountry            : 'BR',\r\n\t\t\tcountryCallingCode : '55',\r\n\t\t\tphone              : '2155555555',\r\n\t\t\tcarrierCode        : '15',\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : true,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse IDD prefixes', () => {\r\n\t\tparseNumber('011 61 2 3456 7890', 'US').should.deep.equal({\r\n\t\t\tphone   : '234567890',\r\n\t\t\tcountry : 'AU'\r\n\t\t})\r\n\r\n\t\tparseNumber('011 61 2 3456 7890', 'FR').should.deep.equal({})\r\n\r\n\t\tparseNumber('00 61 2 3456 7890', 'US').should.deep.equal({})\r\n\r\n\t\tparseNumber('810 61 2 3456 7890', 'RU').should.deep.equal({\r\n\t\t\tphone   : '234567890',\r\n\t\t\tcountry : 'AU'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should work with v2 API', () => {\r\n\t\tparseNumber('+99989160151539')\r\n\t})\r\n\r\n\tit('should work with Argentina numbers', () => {\r\n\t\t// The same mobile number is written differently\r\n\t\t// in different formats in Argentina:\r\n\t\t// `9` gets prepended in international format.\r\n\t\tparseNumber('+54 9 3435 55 1212').should.deep.equal({\r\n\t\t\tcountry: 'AR',\r\n\t\t\tphone: '93435551212'\r\n\t\t})\r\n\t\tparseNumber('0343 15-555-1212', 'AR').should.deep.equal({\r\n\t\t\tcountry: 'AR',\r\n\t\t\tphone: '93435551212'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\t// Fixed line.\r\n\t\tparseNumber('+52 ************').should.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '4499780001'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// parseNumber('01 (449)978-0001', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '4499780001'\r\n\t\t// })\r\n\t\tparseNumber('(449)978-0001', 'MX').should.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '4499780001'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// // Mobile.\r\n\t\t// // `1` is prepended before area code to mobile numbers in international format.\r\n\t\t// parseNumber('+52 1 33 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t\tparseNumber('+52 33 1234-5678', 'MX').should.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '3312345678'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// parseNumber('044 (33) 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t\t// parseNumber('045 33 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers', () => {\r\n\t\tparseNumber('+870773111632').should.deep.equal(\r\n\t\t\tUSE_NON_GEOGRAPHIC_COUNTRY_CODE ?\r\n\t\t\t{\r\n\t\t\t\tcountry: '001',\r\n\t\t\t\tphone: '773111632'\r\n\t\t\t} :\r\n\t\t\t{}\r\n\t\t)\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (default country code)', () => {\r\n\t\tparseNumber('773111632', { defaultCallingCode: '870' }).should.deep.equal(\r\n\t\t\tUSE_NON_GEOGRAPHIC_COUNTRY_CODE ?\r\n\t\t\t{\r\n\t\t\t\tcountry: '001',\r\n\t\t\t\tphone: '773111632'\r\n\t\t\t} :\r\n\t\t\t{}\r\n\t\t)\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (extended)', () => {\r\n\t\tparseNumber('+870773111632', { extended: true }).should.deep.equal({\r\n\t\t\tcountry: USE_NON_GEOGRAPHIC_COUNTRY_CODE ? '001' : undefined,\r\n\t\t\tcountryCallingCode: '870',\r\n\t\t\tphone: '773111632',\r\n\t\t\tcarrierCode: undefined,\r\n\t\t\text: undefined,\r\n\t\t\tpossible: true,\r\n\t\t\tvalid: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (default country code) (extended)', () => {\r\n\t\tparseNumber('773111632', { defaultCallingCode: '870', extended: true }).should.deep.equal({\r\n\t\t\tcountry: USE_NON_GEOGRAPHIC_COUNTRY_CODE ? '001' : undefined,\r\n\t\t\tcountryCallingCode: '870',\r\n\t\t\tphone: '773111632',\r\n\t\t\tcarrierCode: undefined,\r\n\t\t\text: undefined,\r\n\t\t\tpossible: true,\r\n\t\t\tvalid: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('shouldn\\'t crash when invalid `defaultCallingCode` is passed', () => {\r\n\t\texpect(() => parseNumber('773111632', { defaultCallingCode: '999' }))\r\n\t\t\t.to.throw('Unknown calling code')\r\n\t})\r\n\r\n\tit('shouldn\\'t set `country` when there\\'s no `defaultCountry` and `defaultCallingCode` is not of a \"non-geographic entity\"', () => {\r\n\t\tparseNumber('88005553535', { defaultCallingCode: '7' }).should.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphone: '8005553535'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should correctly parse numbers starting with the same digit as the national prefix', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/373\r\n\t\t// `BY`'s `national_prefix` is `8`.\r\n\t\tparseNumber('+37582004910060').should.deep.equal({\r\n\t\t\tcountry: 'BY',\r\n\t\t\tphone: '82004910060'\r\n\t\t});\r\n\t})\r\n\r\n\tit('should autocorrect numbers without a leading +', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\tparseNumber('375447521111', 'BY').should.deep.equal({\r\n\t\t\tcountry: 'BY',\r\n\t\t\tphone: '447521111'\r\n\t\t});\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tparseNumber('33612902554', 'FR').should.deep.equal({\r\n\t\t\tcountry: 'FR',\r\n\t\t\tphone: '612902554'\r\n\t\t});\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\tparseNumber('61438331999', 'AU').should.deep.equal({\r\n\t\t\tcountry: 'AU',\r\n\t\t\tphone: '438331999'\r\n\t\t});\r\n\t\t// A case when `49` is a country calling code of a number without a leading `+`.\r\n\t\tparseNumber('4930123456', 'DE').should.deep.equal({\r\n\t\t\tcountry: 'DE',\r\n\t\t\tphone: '30123456'\r\n\t\t});\r\n\t\t// A case when `49` is a valid area code.\r\n\t\tparseNumber('4951234567890', 'DE').should.deep.equal({\r\n\t\t\tcountry: 'DE',\r\n\t\t\tphone: '4951234567890'\r\n\t\t});\r\n\t})\r\n\r\n\tit('should parse extensions (long extensions with explicitl abels)', () => {\r\n\t\t// Test lower and upper limits of extension lengths for each type of label.\r\n\r\n\t\t// Firstly, when in RFC format: PhoneNumberUtil.extLimitAfterExplicitLabel\r\n\t\tparseNumber('33316005 ext 0', 'NZ').ext.should.equal('0')\r\n\t\tparseNumber('33316005 ext 01234567890123456789', 'NZ').ext.should.equal('01234567890123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('33316005 ext 012345678901234567890', 'NZ').ext).to.be.undefined\r\n\r\n\t\t// Explicit extension label.\r\n\t\tparseNumber('03 3316005ext:1', 'NZ').ext.should.equal('1')\r\n\t\tparseNumber('03 3316005 xtn:12345678901234567890', 'NZ').ext.should.equal('12345678901234567890')\r\n\t\tparseNumber('03 3316005 extension\\t12345678901234567890', 'NZ').ext.should.equal('12345678901234567890')\r\n\t\tparseNumber('03 3316005 xtensio:12345678901234567890', 'NZ').ext.should.equal('12345678901234567890')\r\n\t\tparseNumber('03 3316005 xtensión, 12345678901234567890#', 'NZ').ext.should.equal('12345678901234567890')\r\n\t\tparseNumber('03 3316005extension.12345678901234567890', 'NZ').ext.should.equal('12345678901234567890')\r\n\t\tparseNumber('03 3316005 доб:12345678901234567890', 'NZ').ext.should.equal('12345678901234567890')\r\n\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('03 3316005 extension 123456789012345678901', 'NZ').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (long extensions with auto dialling labels)', () => {\r\n\t\tparseNumber('+12679000000,,123456789012345#').ext.should.equal('123456789012345')\r\n\t\tparseNumber('+12679000000;123456789012345#').ext.should.equal('123456789012345')\r\n\t\tparseNumber('+442034000000,,123456789#').ext.should.equal('123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('+12679000000,,1234567890123456#').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (short extensions with ambiguous characters)', () => {\r\n\t\tparseNumber('03 3316005 x 123456789', 'NZ').ext.should.equal('123456789')\r\n\t\tparseNumber('03 3316005 x. 123456789', 'NZ').ext.should.equal('123456789')\r\n\t\tparseNumber('03 3316005 #123456789#', 'NZ').ext.should.equal('123456789')\r\n\t\tparseNumber('03 3316005 ~ 123456789', 'NZ').ext.should.equal('123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('03 3316005 ~ 1234567890', 'NZ').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (short extensions when not sure of label)', () => {\r\n\t\tparseNumber('+1123-456-7890 666666#', { v2: true }).ext.should.equal('666666')\r\n\t\tparseNumber('+11234567890-6#', { v2: true }).ext.should.equal('6')\r\n\t\t// Extension too long.\r\n\t\texpect(() => parseNumber('+1123-456-7890 7777777#', { v2: true })).to.throw('NOT_A_NUMBER')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,YAAP,MAAyB,YAAzB;AACA,OAAOC,QAAP,MAAqB,gBAArB;;AAEA,SAASC,WAAT,GAAoC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACnCA,UAAU,CAACC,IAAX,CAAgBN,QAAhB;EACA,OAAOE,YAAY,CAACK,KAAb,CAAmB,IAAnB,EAAyBF,UAAzB,CAAP;AACA;;AAED,IAAMG,+BAA+B,GAAG,KAAxC;AAEAC,QAAQ,CAAC,OAAD,EAAU,YAAM;EACvBC,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClD;IACAN,WAAW,CAAC,mBAAD,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD,EAAnD,EAFkD,CAGlD;;IACAT,WAAW,CAAC,sBAAD,CAAX,CAAoCO,MAApC,CAA2CC,IAA3C,CAAgDC,KAAhD,CAAsD,EAAtD;IAEAT,WAAW,CAAC,mBAAD,EAAsB,IAAtB,CAAX,CAAuCO,MAAvC,CAA8CC,IAA9C,CAAmDC,KAAnD,CAAyD,EAAzD;IACAT,WAAW,CAAC,gBAAD,EAAmB;MAAEU,cAAc,EAAE;IAAlB,CAAnB,CAAX,CAAwDH,MAAxD,CAA+DC,IAA/D,CAAoEC,KAApE,CAA0E,EAA1E;IACAT,WAAW,CAAC,iBAAD,EAAoB,IAApB,CAAX,CAAqCO,MAArC,CAA4CC,IAA5C,CAAiDC,KAAjD,CAAuD,EAAvD;IAEAT,WAAW,CAAC,WAAD,EAAc,IAAd,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD,EAAjD;EACA,CAXC,CAAF;EAaAH,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C;IACA;IACA;IACA;IACAN,WAAW,CAAC,2BAAD,EAA8B,IAA9B,CAAX,CAA+CO,MAA/C,CAAsDC,IAAtD,CAA2DC,KAA3D,CAAiE;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAjE,EAL4C,CAM5C;;IACAZ,WAAW,CAAC,4BAAD,CAAX,CAA0CO,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA5D,EAP4C,CAQ5C;IACA;IACA;;IACAZ,WAAW,CAAC,iBAAD,EAAoB,IAApB,CAAX,CAAqCO,MAArC,CAA4CC,IAA5C,CAAiDC,KAAjD,CAAuD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAvD,EAX4C,CAY5C;;IACAZ,WAAW,CAAC,mBAAD,EAAsB;MAAEU,cAAc,EAAE;IAAlB,CAAtB,CAAX,CAA2DH,MAA3D,CAAkEC,IAAlE,CAAuEC,KAAvE,CAA6E;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA7E,EAb4C,CAe5C;;IACAZ,WAAW,CAAC,iBAAD,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAjD,EAhB4C,CAkB5C;;IACAZ,WAAW,CAAC,eAAD,EAAkB,IAAlB,CAAX,CAAmCO,MAAnC,CAA0CC,IAA1C,CAA+CC,KAA/C,CAAqD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAArD,EAnB4C,CAqB5C;;IACAZ,WAAW,CAAC,eAAD,EAAkB,IAAlB,CAAX,CAAmCO,MAAnC,CAA0CC,IAA1C,CAA+CC,KAA/C,CAAqD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAArD,EAtB4C,CAwB5C;;IACAZ,WAAW,CAAC,cAAD,CAAX,CAA4BO,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA9C,EAzB4C,CA2B5C;;IACAZ,WAAW,CAAC,iBAAD,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAjD,EA5B4C,CA8B5C;;IACAZ,WAAW,CAAC,iBAAD,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAjD,EA/B4C,CAiC5C;;IACAZ,WAAW,CAAC,aAAD,EAAgB,IAAhB,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAnD,EAlC4C,CAoC5C;;IACAZ,WAAW,CAAC,eAAD,CAAX,CAA6BO,MAA7B,CAAoCC,IAApC,CAAyCC,KAAzC,CAA+C;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA/C,EArC4C,CAuC5C;IACA;IAEA;;IACAZ,WAAW,CAAC,aAAD,EAAgB,IAAhB,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAnD;EACA,CA5CC,CAAF;EA8CAN,EAAE,CAAC,+BAAD,EAAkC,YAAM;IACzC;IACAN,WAAW,CAAC,YAAD,EAAe,IAAf,EAAqB;MAAEa,QAAQ,EAAE;IAAZ,CAArB,CAAX,CAAoDN,MAApD,CAA2DC,IAA3D,CAAgEC,KAAhE,CAAsE;MACrEE,OAAO,EAAc,IADgD;MAErEG,kBAAkB,EAAG,GAFgD;MAGrEF,KAAK,EAAgB,YAHgD;MAIrEG,WAAW,EAAUC,SAJgD;MAKrEC,GAAG,EAAkBD,SALgD;MAMrEE,KAAK,EAAgB,KANgD;MAOrEC,QAAQ,EAAa;IAPgD,CAAtE,EAFyC,CAYzC;IACA;;IACAnB,WAAW,CAAC,cAAD,CAAX,CAA4BO,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C,EAA9C;IACAT,WAAW,CAAC,cAAD,EAAiB;MAAEa,QAAQ,EAAE;IAAZ,CAAjB,CAAX,CAAgDN,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE;MACjEE,OAAO,EAAcK,SAD4C;MAEjEF,kBAAkB,EAAG,GAF4C;MAGjEF,KAAK,EAAgB,YAH4C;MAIjEG,WAAW,EAAUC,SAJ4C;MAKjEC,GAAG,EAAkBD,SAL4C;MAMjEE,KAAK,EAAgB,KAN4C;MAOjEC,QAAQ,EAAa;IAP4C,CAAlE,EAfyC,CAyBzC;IACA;;IACAnB,WAAW,CAAC,cAAD,EAAiB;MAAEa,QAAQ,EAAE;IAAZ,CAAjB,CAAX,CAAgDN,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE;MACjEE,OAAO,EAAc,IAD4C;MAEjEG,kBAAkB,EAAG,IAF4C;MAGjEF,KAAK,EAAgB,WAH4C;MAIjEG,WAAW,EAAUC,SAJ4C;MAKjEC,GAAG,EAAkBD,SAL4C;MAMjEE,KAAK,EAAgB,KAN4C;MAOjEC,QAAQ,EAAa;IAP4C,CAAlE,EA3ByC,CAqCzC;IACA;;IACAnB,WAAW,CAAC,mBAAD,EAAsB;MAAEa,QAAQ,EAAE;IAAZ,CAAtB,CAAX,CAAqDN,MAArD,CAA4DC,IAA5D,CAAiEC,KAAjE,CAAuE;MACtEE,OAAO,EAAc,IADiD;MAEtEG,kBAAkB,EAAG,GAFiD;MAGtEF,KAAK,EAAgB,WAHiD;MAItEG,WAAW,EAAUC,SAJiD;MAKtEC,GAAG,EAAkBD,SALiD;MAMtEE,KAAK,EAAgB,KANiD;MAOtEC,QAAQ,EAAa;IAPiD,CAAvE,EAvCyC,CAiDzC;;IACAnB,WAAW,CAAC,iBAAD,EAAoB;MAAEa,QAAQ,EAAE;IAAZ,CAApB,CAAX,CAAmDN,MAAnD,CAA0DC,IAA1D,CAA+DC,KAA/D,CAAqE;MACpEE,OAAO,EAAcK,SAD+C;MAEpEF,kBAAkB,EAAG,GAF+C;MAGpEF,KAAK,EAAgB,aAH+C;MAIpEG,WAAW,EAAUC,SAJ+C;MAKpEC,GAAG,EAAkBD,SAL+C;MAMpEE,KAAK,EAAgB,KAN+C;MAOpEC,QAAQ,EAAa;IAP+C,CAArE,EAlDyC,CA4DzC;;IACAnB,WAAW,CAAC,MAAD,EAAS;MAAEa,QAAQ,EAAE;IAAZ,CAAT,CAAX,CAAwCN,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D,CACzD;IADyD,CAA1D,EA7DyC,CAiEzC;;IACAT,WAAW,CAAC,cAAD,EAAiB;MAAEa,QAAQ,EAAE;IAAZ,CAAjB,CAAX,CAAgDN,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE;MACjEE,OAAO,EAAc,IAD4C;MAEjEG,kBAAkB,EAAG,GAF4C;MAGjEF,KAAK,EAAgB,YAH4C;MAIjEG,WAAW,EAAUC,SAJ4C;MAKjEC,GAAG,EAAkBD,SAL4C;MAMjEE,KAAK,EAAgB,IAN4C;MAOjEC,QAAQ,EAAa;IAP4C,CAAlE,EAlEyC,CA4EzC;;IACAnB,WAAW,CAAC,MAAD,EAAS;MAAEa,QAAQ,EAAE;IAAZ,CAAT,CAAX,CAAwCN,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D,EAA1D;IACAT,WAAW,CAAC,OAAD,EAAU;MAAEa,QAAQ,EAAE;IAAZ,CAAV,CAAX,CAAyCN,MAAzC,CAAgDC,IAAhD,CAAqDC,KAArD,CAA2D,EAA3D;IACAT,WAAW,CAAC,QAAD,EAAW;MAAEa,QAAQ,EAAE;IAAZ,CAAX,CAAX,CAA0CN,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D;MAC3DM,WAAW,EAAUC,SADsC;MAE3DJ,KAAK,EAAgB,IAFsC;MAG3DK,GAAG,EAAkBD,SAHsC;MAI3DL,OAAO,EAAc,IAJsC;MAK3DG,kBAAkB,EAAG,KALsC;MAM3DK,QAAQ,EAAa,KANsC;MAO3DD,KAAK,EAAgB;IAPsC,CAA5D;EASA,CAxFC,CAAF;EA0FAZ,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5CN,WAAW,CAAC,cAAD,CAAX,CAA4BO,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA9C;EACA,CAFC,CAAF;EAIAN,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIc,OAAJ,CADqC,CAGrC;;IACApB,WAAW,CAAC,EAAD,CAAX,CAAgBO,MAAhB,CAAuBC,IAAvB,CAA4BC,KAA5B,CAAkC,EAAlC,EAJqC,CAMrC;;IACAT,WAAW,CAAC,GAAD,CAAX,CAAiBO,MAAjB,CAAwBC,IAAxB,CAA6BC,KAA7B,CAAmC,EAAnC,EAPqC,CASrC;;IACAT,WAAW,CAAC,KAAD,CAAX,CAAmBO,MAAnB,CAA0BC,IAA1B,CAA+BC,KAA/B,CAAqC,EAArC,EAVqC,CAYrC;;IACAW,OAAO,GAAG;MAAA,OAAMpB,WAAW,CAAC,KAAD,EAAQ,IAAR,CAAjB;IAAA,CAAV;;IACAoB,OAAO,CAACb,MAAR,UAAqB,iBAArB,EAdqC,CAgBrC;;IACAa,OAAO,GAAG;MAAA,OAAMpB,WAAW,CAAC,KAAD,EAAQ;QAAEU,cAAc,EAAE;MAAlB,CAAR,CAAjB;IAAA,CAAV;;IACAU,OAAO,CAACb,MAAR,UAAqB,iBAArB,EAlBqC,CAoBrC;;IACAP,WAAW,CAAC,MAAD,CAAX,CAAoBO,MAApB,CAA2BC,IAA3B,CAAgCC,KAAhC,CAAsC,EAAtC,EArBqC,CAuBrC;;IACAT,WAAW,CAAC,MAAD,EAAS;MAAEa,QAAQ,EAAE;IAAZ,CAAT,CAAX,CAAwCN,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D,EAA1D,EAxBqC,CA0BrC;;IACAT,WAAW,CAAC,GAAD,EAAM,IAAN,EAAY;MAAEa,QAAQ,EAAE;IAAZ,CAAZ,CAAX,CAA2CN,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EA3BqC,CA6BrC;;IACAT,WAAW,CAAC,qBAAD,EAAwB,IAAxB,EAA8B;MAAEa,QAAQ,EAAE;IAAZ,CAA9B,CAAX,CAA6DN,MAA7D,CAAoEC,IAApE,CAAyEC,KAAzE,CAA+E,EAA/E,EA9BqC,CAgCrC;;IACAT,WAAW,CAAC,SAAD,EAAY,IAAZ,EAAkB;MAAEa,QAAQ,EAAE;IAAZ,CAAlB,CAAX,CAAiDN,MAAjD,CAAwDC,IAAxD,CAA6DC,KAA7D,CAAmE,EAAnE,EAjCqC,CAmCrC;;IACAT,WAAW,CAAC,OAAD,CAAX,CAAqBO,MAArB,CAA4BC,IAA5B,CAAiCC,KAAjC,CAAuC,EAAvC,EApCqC,CAsCrC;;IACAT,WAAW,CAAC,cAAD,CAAX,CAA4BO,MAA5B,CAAmCC,IAAnC,CAAwCC,KAAxC,CAA8C;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA9C,EAvCqC,CAyCrC;IACA;IACA;IAEA;;IACAZ,WAAW,CAAC,GAAD,EAAM,IAAN,CAAX,CAAuBO,MAAvB,CAA8BC,IAA9B,CAAmCC,KAAnC,CAAyC,EAAzC,EA9CqC,CAgDrC;;IACAT,WAAW,CAAC,oBAAD,EAAuB,IAAvB,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D,EAA1D,EAjDqC,CAmDrC;;IACAT,WAAW,CAAC,OAAD,EAAU,IAAV,CAAX,CAA2BO,MAA3B,CAAkCC,IAAlC,CAAuCC,KAAvC,CAA6C;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAA7C,EApDqC,CAsDrC;IACA;;IACAZ,WAAW,CAAC,iBAAD,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD;MAAEE,OAAO,EAAE,IAAX;MAAiBC,KAAK,EAAE;IAAxB,CAAjD,EAxDqC,CA0DrC;;IACAQ,OAAO,GAAG;MAAA,OAAMtB,YAAY,CAAC,EAAD,CAAlB;IAAA,CAAV;;IACAsB,OAAO,CAACb,MAAR,UAAqB,gCAArB,EA5DqC,CA8DrC;;IACAa,OAAO,GAAG;MAAA,OAAMpB,WAAW,CAAC,UAAD,EAAa,IAAb,CAAjB;IAAA,CAAV;;IACAoB,OAAO,CAACb,MAAR,UAAqB,sCAArB,EAhEqC,CAkErC;;IACAP,WAAW,CAAC,6PAAD,EAAgQ,IAAhQ,CAAX,CAAiRO,MAAjR,CAAwRC,IAAxR,CAA6RC,KAA7R,CAAmS,EAAnS;EACA,CApEC,CAAF;EAsEAH,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD;IACAN,WAAW,CAAC,oBAAD,EAAuB,IAAvB,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D;MACzDE,OAAO,EAAG,IAD+C;MAEzDC,KAAK,EAAK,YAF+C;MAGzDK,GAAG,EAAO;IAH+C,CAA1D,EAFgD,CAQhD;;IACAjB,WAAW,CAAC,yBAAD,EAA4B,IAA5B,CAAX,CAA6CO,MAA7C,CAAoDC,IAApD,CAAyDC,KAAzD,CAA+D;MAC9DE,OAAO,EAAG,IADoD;MAE9DC,KAAK,EAAK,YAFoD;MAG9DK,GAAG,EAAO;IAHoD,CAA/D,EATgD,CAehD;;IACAjB,WAAW,CAAC,wBAAD,EAA2B,IAA3B,CAAX,CAA4CO,MAA5C,CAAmDC,IAAnD,CAAwDC,KAAxD,CAA8D;MAC7DE,OAAO,EAAG,IADmD;MAE7DC,KAAK,EAAK,YAFmD;MAG7DK,GAAG,EAAO;IAHmD,CAA9D,EAhBgD,CAsBhD;;IACAjB,WAAW,CAAC,mBAAD,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD;MAClDE,OAAO,EAAG,IADwC;MAElDC,KAAK,EAAK,YAFwC;MAGlDK,GAAG,EAAO;IAHwC,CAAnD,EAvBgD,CA6BhD;;IACAjB,WAAW,CAAC,oBAAD,CAAX,CAAkCO,MAAlC,CAAyCC,IAAzC,CAA8CC,KAA9C,CAAoD;MACnDE,OAAO,EAAG,IADyC;MAEnDC,KAAK,EAAK,YAFyC;MAGnDK,GAAG,EAAO;IAHyC,CAApD,EA9BgD,CAoChD;;IACAjB,WAAW,CAAC,qBAAD,EAAwB,IAAxB,CAAX,CAAyCO,MAAzC,CAAgDC,IAAhD,CAAqDC,KAArD,CAA2D;MAC1DE,OAAO,EAAG,IADgD;MAE1DC,KAAK,EAAK;IAFgD,CAA3D;EAIA,CAzCC,CAAF;EA2CAN,EAAE,CAAC,qCAAD,EAAwC,YAAM;IAC/CN,WAAW,CAAC,0BAAD,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D;MACzDE,OAAO,EAAG,IAD+C;MAEzDC,KAAK,EAAK,YAF+C;MAGzDK,GAAG,EAAO;IAH+C,CAA1D,EAD+C,CAO/C;;IACAjB,WAAW,CAAC,8BAAD,CAAX,CAA4CO,MAA5C,CAAmDC,IAAnD,CAAwDC,KAAxD,CAA8D;MAC7DE,OAAO,EAAG,IADmD;MAE7DC,KAAK,EAAK,YAFmD;MAG7DK,GAAG,EAAO;IAHmD,CAA9D,EAR+C,CAc/C;;IACAjB,WAAW,CAAC,2BAAD,CAAX,CAAyCO,MAAzC,CAAgDC,IAAhD,CAAqDC,KAArD,CAA2D,EAA3D;EACA,CAhBC,CAAF;EAkBAH,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/EN,WAAW,CAAC,iBAAD,EAAoB,IAApB,CAAX,CAAqCO,MAArC,CAA4CC,IAA5C,CAAiDC,KAAjD,CAAuD;MACtDE,OAAO,EAAG,IAD4C;MAEtDC,KAAK,EAAK;IAF4C,CAAvD;EAIA,CALC,CAAF;EAOAN,EAAE,CAAC,4BAAD,EAA+B,YAAM;IACtCN,WAAW,CAAC,mBAAD,EAAsB,IAAtB,EAA4B;MAAEa,QAAQ,EAAE;IAAZ,CAA5B,CAAX,CAA2DN,MAA3D,CAAkEC,IAAlE,CAAuEC,KAAvE,CAA6E;MAC5EE,OAAO,EAAc,IADuD;MAE5EG,kBAAkB,EAAG,IAFuD;MAG5EF,KAAK,EAAgB,YAHuD;MAI5EG,WAAW,EAAU,IAJuD;MAK5EE,GAAG,EAAkBD,SALuD;MAM5EE,KAAK,EAAgB,IANuD;MAO5EC,QAAQ,EAAa;IAPuD,CAA7E;EASA,CAVC,CAAF;EAYAb,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCN,WAAW,CAAC,oBAAD,EAAuB,IAAvB,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D;MACzDG,KAAK,EAAK,WAD+C;MAEzDD,OAAO,EAAG;IAF+C,CAA1D;IAKAX,WAAW,CAAC,oBAAD,EAAuB,IAAvB,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D,EAA1D;IAEAT,WAAW,CAAC,mBAAD,EAAsB,IAAtB,CAAX,CAAuCO,MAAvC,CAA8CC,IAA9C,CAAmDC,KAAnD,CAAyD,EAAzD;IAEAT,WAAW,CAAC,oBAAD,EAAuB,IAAvB,CAAX,CAAwCO,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D;MACzDG,KAAK,EAAK,WAD+C;MAEzDD,OAAO,EAAG;IAF+C,CAA1D;EAIA,CAdC,CAAF;EAgBAL,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnCN,WAAW,CAAC,iBAAD,CAAX;EACA,CAFC,CAAF;EAIAM,EAAE,CAAC,oCAAD,EAAuC,YAAM;IAC9C;IACA;IACA;IACAN,WAAW,CAAC,oBAAD,CAAX,CAAkCO,MAAlC,CAAyCC,IAAzC,CAA8CC,KAA9C,CAAoD;MACnDE,OAAO,EAAE,IAD0C;MAEnDC,KAAK,EAAE;IAF4C,CAApD;IAIAZ,WAAW,CAAC,kBAAD,EAAqB,IAArB,CAAX,CAAsCO,MAAtC,CAA6CC,IAA7C,CAAkDC,KAAlD,CAAwD;MACvDE,OAAO,EAAE,IAD8C;MAEvDC,KAAK,EAAE;IAFgD,CAAxD;EAIA,CAZC,CAAF;EAcAN,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACAN,WAAW,CAAC,kBAAD,CAAX,CAAgCO,MAAhC,CAAuCC,IAAvC,CAA4CC,KAA5C,CAAkD;MACjDE,OAAO,EAAE,IADwC;MAEjDC,KAAK,EAAE;IAF0C,CAAlD,EAF2C,CAM3C;IACA;IACA;IACA;IACA;IACA;IACA;;IACAZ,WAAW,CAAC,eAAD,EAAkB,IAAlB,CAAX,CAAmCO,MAAnC,CAA0CC,IAA1C,CAA+CC,KAA/C,CAAqD;MACpDE,OAAO,EAAE,IAD2C;MAEpDC,KAAK,EAAE;IAF6C,CAArD,EAb2C,CAiB3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACAZ,WAAW,CAAC,kBAAD,EAAqB,IAArB,CAAX,CAAsCO,MAAtC,CAA6CC,IAA7C,CAAkDC,KAAlD,CAAwD;MACvDE,OAAO,EAAE,IAD8C;MAEvDC,KAAK,EAAE;IAFgD,CAAxD,EA1B2C,CA8B3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA,CAzCC,CAAF;EA2CAN,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpEN,WAAW,CAAC,eAAD,CAAX,CAA6BO,MAA7B,CAAoCC,IAApC,CAAyCC,KAAzC,CACCL,+BAA+B,GAC/B;MACCO,OAAO,EAAE,KADV;MAECC,KAAK,EAAE;IAFR,CAD+B,GAK/B,EAND;EAQA,CATC,CAAF;EAWAN,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3FN,WAAW,CAAC,WAAD,EAAc;MAAEqB,kBAAkB,EAAE;IAAtB,CAAd,CAAX,CAAwDd,MAAxD,CAA+DC,IAA/D,CAAoEC,KAApE,CACCL,+BAA+B,GAC/B;MACCO,OAAO,EAAE,KADV;MAECC,KAAK,EAAE;IAFR,CAD+B,GAK/B,EAND;EAQA,CATC,CAAF;EAWAN,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/EN,WAAW,CAAC,eAAD,EAAkB;MAAEa,QAAQ,EAAE;IAAZ,CAAlB,CAAX,CAAiDN,MAAjD,CAAwDC,IAAxD,CAA6DC,KAA7D,CAAmE;MAClEE,OAAO,EAAEP,+BAA+B,GAAG,KAAH,GAAWY,SADe;MAElEF,kBAAkB,EAAE,KAF8C;MAGlEF,KAAK,EAAE,WAH2D;MAIlEG,WAAW,EAAEC,SAJqD;MAKlEC,GAAG,EAAED,SAL6D;MAMlEG,QAAQ,EAAE,IANwD;MAOlED,KAAK,EAAE;IAP2D,CAAnE;EASA,CAVC,CAAF;EAYAZ,EAAE,CAAC,4FAAD,EAA+F,YAAM;IACtGN,WAAW,CAAC,WAAD,EAAc;MAAEqB,kBAAkB,EAAE,KAAtB;MAA6BR,QAAQ,EAAE;IAAvC,CAAd,CAAX,CAAwEN,MAAxE,CAA+EC,IAA/E,CAAoFC,KAApF,CAA0F;MACzFE,OAAO,EAAEP,+BAA+B,GAAG,KAAH,GAAWY,SADsC;MAEzFF,kBAAkB,EAAE,KAFqE;MAGzFF,KAAK,EAAE,WAHkF;MAIzFG,WAAW,EAAEC,SAJ4E;MAKzFC,GAAG,EAAED,SALoF;MAMzFG,QAAQ,EAAE,IAN+E;MAOzFD,KAAK,EAAE;IAPkF,CAA1F;EASA,CAVC,CAAF;EAYAZ,EAAE,CAAC,8DAAD,EAAiE,YAAM;IACxEgB,MAAM,CAAC;MAAA,OAAMtB,WAAW,CAAC,WAAD,EAAc;QAAEqB,kBAAkB,EAAE;MAAtB,CAAd,CAAjB;IAAA,CAAD,CAAN,CACEE,EADF,UACW,sBADX;EAEA,CAHC,CAAF;EAKAjB,EAAE,CAAC,yHAAD,EAA4H,YAAM;IACnIN,WAAW,CAAC,aAAD,EAAgB;MAAEqB,kBAAkB,EAAE;IAAtB,CAAhB,CAAX,CAAwDd,MAAxD,CAA+DC,IAA/D,CAAoEC,KAApE,CAA0E;MACzEE,OAAO,EAAE,IADgE;MAEzEC,KAAK,EAAE;IAFkE,CAA1E;EAIA,CALC,CAAF;EAOAN,EAAE,CAAC,oFAAD,EAAuF,YAAM;IAC9F;IACA;IACAN,WAAW,CAAC,iBAAD,CAAX,CAA+BO,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD;MAChDE,OAAO,EAAE,IADuC;MAEhDC,KAAK,EAAE;IAFyC,CAAjD;EAIA,CAPC,CAAF;EASAN,EAAE,CAAC,gDAAD,EAAmD,YAAM;IAC1D;IACAN,WAAW,CAAC,cAAD,EAAiB,IAAjB,CAAX,CAAkCO,MAAlC,CAAyCC,IAAzC,CAA8CC,KAA9C,CAAoD;MACnDE,OAAO,EAAE,IAD0C;MAEnDC,KAAK,EAAE;IAF4C,CAApD,EAF0D,CAM1D;;IACAZ,WAAW,CAAC,aAAD,EAAgB,IAAhB,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD;MAClDE,OAAO,EAAE,IADyC;MAElDC,KAAK,EAAE;IAF2C,CAAnD,EAP0D,CAW1D;;IACAZ,WAAW,CAAC,aAAD,EAAgB,IAAhB,CAAX,CAAiCO,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD;MAClDE,OAAO,EAAE,IADyC;MAElDC,KAAK,EAAE;IAF2C,CAAnD,EAZ0D,CAgB1D;;IACAZ,WAAW,CAAC,YAAD,EAAe,IAAf,CAAX,CAAgCO,MAAhC,CAAuCC,IAAvC,CAA4CC,KAA5C,CAAkD;MACjDE,OAAO,EAAE,IADwC;MAEjDC,KAAK,EAAE;IAF0C,CAAlD,EAjB0D,CAqB1D;;IACAZ,WAAW,CAAC,eAAD,EAAkB,IAAlB,CAAX,CAAmCO,MAAnC,CAA0CC,IAA1C,CAA+CC,KAA/C,CAAqD;MACpDE,OAAO,EAAE,IAD2C;MAEpDC,KAAK,EAAE;IAF6C,CAArD;EAIA,CA1BC,CAAF;EA4BAN,EAAE,CAAC,gEAAD,EAAmE,YAAM;IAC1E;IAEA;IACAN,WAAW,CAAC,gBAAD,EAAmB,IAAnB,CAAX,CAAoCiB,GAApC,CAAwCV,MAAxC,CAA+CE,KAA/C,CAAqD,GAArD;IACAT,WAAW,CAAC,mCAAD,EAAsC,IAAtC,CAAX,CAAuDiB,GAAvD,CAA2DV,MAA3D,CAAkEE,KAAlE,CAAwE,sBAAxE,EAL0E,CAM1E;;IACAa,MAAM,CAACtB,WAAW,CAAC,oCAAD,EAAuC,IAAvC,CAAX,CAAwDiB,GAAzD,CAAN,CAAoEM,EAApE,CAAuEC,EAAvE,CAA0ER,SAA1E,CAP0E,CAS1E;;IACAhB,WAAW,CAAC,iBAAD,EAAoB,IAApB,CAAX,CAAqCiB,GAArC,CAAyCV,MAAzC,CAAgDE,KAAhD,CAAsD,GAAtD;IACAT,WAAW,CAAC,qCAAD,EAAwC,IAAxC,CAAX,CAAyDiB,GAAzD,CAA6DV,MAA7D,CAAoEE,KAApE,CAA0E,sBAA1E;IACAT,WAAW,CAAC,4CAAD,EAA+C,IAA/C,CAAX,CAAgEiB,GAAhE,CAAoEV,MAApE,CAA2EE,KAA3E,CAAiF,sBAAjF;IACAT,WAAW,CAAC,yCAAD,EAA4C,IAA5C,CAAX,CAA6DiB,GAA7D,CAAiEV,MAAjE,CAAwEE,KAAxE,CAA8E,sBAA9E;IACAT,WAAW,CAAC,4CAAD,EAA+C,IAA/C,CAAX,CAAgEiB,GAAhE,CAAoEV,MAApE,CAA2EE,KAA3E,CAAiF,sBAAjF;IACAT,WAAW,CAAC,0CAAD,EAA6C,IAA7C,CAAX,CAA8DiB,GAA9D,CAAkEV,MAAlE,CAAyEE,KAAzE,CAA+E,sBAA/E;IACAT,WAAW,CAAC,qCAAD,EAAwC,IAAxC,CAAX,CAAyDiB,GAAzD,CAA6DV,MAA7D,CAAoEE,KAApE,CAA0E,sBAA1E,EAhB0E,CAkB1E;;IACAa,MAAM,CAACtB,WAAW,CAAC,4CAAD,EAA+C,IAA/C,CAAX,CAAgEiB,GAAjE,CAAN,CAA4EM,EAA5E,CAA+EC,EAA/E,CAAkFR,SAAlF;EACA,CApBC,CAAF;EAsBAV,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/EN,WAAW,CAAC,gCAAD,CAAX,CAA8CiB,GAA9C,CAAkDV,MAAlD,CAAyDE,KAAzD,CAA+D,iBAA/D;IACAT,WAAW,CAAC,+BAAD,CAAX,CAA6CiB,GAA7C,CAAiDV,MAAjD,CAAwDE,KAAxD,CAA8D,iBAA9D;IACAT,WAAW,CAAC,2BAAD,CAAX,CAAyCiB,GAAzC,CAA6CV,MAA7C,CAAoDE,KAApD,CAA0D,WAA1D,EAH+E,CAI/E;;IACAa,MAAM,CAACtB,WAAW,CAAC,iCAAD,CAAX,CAA+CiB,GAAhD,CAAN,CAA2DM,EAA3D,CAA8DC,EAA9D,CAAiER,SAAjE;EACA,CANC,CAAF;EAQAV,EAAE,CAAC,sEAAD,EAAyE,YAAM;IAChFN,WAAW,CAAC,wBAAD,EAA2B,IAA3B,CAAX,CAA4CiB,GAA5C,CAAgDV,MAAhD,CAAuDE,KAAvD,CAA6D,WAA7D;IACAT,WAAW,CAAC,yBAAD,EAA4B,IAA5B,CAAX,CAA6CiB,GAA7C,CAAiDV,MAAjD,CAAwDE,KAAxD,CAA8D,WAA9D;IACAT,WAAW,CAAC,wBAAD,EAA2B,IAA3B,CAAX,CAA4CiB,GAA5C,CAAgDV,MAAhD,CAAuDE,KAAvD,CAA6D,WAA7D;IACAT,WAAW,CAAC,wBAAD,EAA2B,IAA3B,CAAX,CAA4CiB,GAA5C,CAAgDV,MAAhD,CAAuDE,KAAvD,CAA6D,WAA7D,EAJgF,CAKhF;;IACAa,MAAM,CAACtB,WAAW,CAAC,yBAAD,EAA4B,IAA5B,CAAX,CAA6CiB,GAA9C,CAAN,CAAyDM,EAAzD,CAA4DC,EAA5D,CAA+DR,SAA/D;EACA,CAPC,CAAF;EASAV,EAAE,CAAC,mEAAD,EAAsE,YAAM;IAC7EN,WAAW,CAAC,wBAAD,EAA2B;MAAEyB,EAAE,EAAE;IAAN,CAA3B,CAAX,CAAoDR,GAApD,CAAwDV,MAAxD,CAA+DE,KAA/D,CAAqE,QAArE;IACAT,WAAW,CAAC,iBAAD,EAAoB;MAAEyB,EAAE,EAAE;IAAN,CAApB,CAAX,CAA6CR,GAA7C,CAAiDV,MAAjD,CAAwDE,KAAxD,CAA8D,GAA9D,EAF6E,CAG7E;;IACAa,MAAM,CAAC;MAAA,OAAMtB,WAAW,CAAC,yBAAD,EAA4B;QAAEyB,EAAE,EAAE;MAAN,CAA5B,CAAjB;IAAA,CAAD,CAAN,CAAmEF,EAAnE,UAA4E,cAA5E;EACA,CALC,CAAF;AAMA,CAzgBO,CAAR"}