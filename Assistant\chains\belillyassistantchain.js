import { GoogleGenerativeAI } from "@google/generative-ai";
import { StateGraph, END } from "@langchain/langgraph";
import { RunnableSequence } from "@langchain/core/runnables";
import { PromptTemplate } from "@langchain/core/prompts";

// System prompt for the assistant
const systemPrompt = `You are a professional customer support assistant for Belilly, an e-commerce fashion brand.
Your name is Belilly Support Assistant.

Key information about Belilly:
- We sell high-quality athletic wear and fashion clothing for men, women, and kids
- We offer excellent customer service with 24/7 support
- We have a 7-day return policy for unworn items with original tags
- Free shipping on orders above ₹999
- We accept credit/debit cards, UPI, and cash on delivery
- Our products include t-shirts, hoodies, leggings, shorts, joggers, and more

Guidelines:
- Be helpful, friendly, and professional
- Provide accurate information about products, orders, and policies
- If you need to access specific user data, use the appropriate tools
- Always prioritize customer satisfaction
- If you cannot help with something, guide them to contact support directly

Remember: You have access to tools for product information, order details, user account data, and FAQ content.`;

// Custom output parser
class CustomStringOutputParser {
  parse(text) {
    if (typeof text === 'string') {
      return text.trim();
    }
    if (text && typeof text === 'object') {
      return JSON.stringify(text);
    }
    return String(text || '').trim();
  }
}

// Initialize Gemini model with connection reuse
let geminiModel = null;

const initializeGeminiModel = (apiKey) => {
  if (geminiModel) {
    return geminiModel;
  }

  if (!apiKey) {
    throw new Error('Gemini API key is required');
  }

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

  // Create a wrapper that mimics LangChain interface
  geminiModel = {
    invoke: async (prompt) => {
      try {
        const promptText = typeof prompt === "string" ? prompt : JSON.stringify(prompt);
        const result = await model.generateContent(promptText);

        if (result && result.response) {
          if (typeof result.response.text === 'function') {
            return result.response.text();
          } else if (typeof result.response === 'string') {
            return result.response;
          } else if (result.response.text) {
            return result.response.text;
          }
        }

        console.log("Unexpected Gemini response format");
        return JSON.stringify(result);
      } catch (error) {
        console.error("Error in Gemini model invocation");
        return "An error occurred while processing your request.";
      }
    }
  };

  return geminiModel;
};

/**
 * Create the Belilly Assistant Graph for Lambda
 * @param {string} apiKey - Gemini API key
 * @param {Object} user - The authenticated user object (null if not authenticated)
 * @param {Object} mcpClient - MCP client for tool access
 * @returns {Object} - Compiled LangGraph
 */
export const createBelillyAssistantGraph = (apiKey, user, mcpClient) => {
  const model = initializeGeminiModel(apiKey);

  // Router node to determine which tool to use
  const routerPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    Based on the user's message, determine which action to take:
    1. "product_info" - If the user is asking about product details, availability, sizes, etc.
    2. "order_info" - If the user is asking about order status, tracking, order count, latest order, recent orders, etc.
    3. "user_info" - If the user is asking about their account, profile, personal details, contact information, addresses, etc.
    4. "faq" - If the user is asking general questions about policies, returns, etc.
    5. "general" - If none of the above apply

    Previous conversation:
    {history}

    User message: {input}

    Return ONLY ONE of: "product_info", "order_info", "user_info", "faq", or "general"`
  );

  const router = RunnableSequence.from([
    (state) => {
      return {
        input: state.input || "",
        history: state.history || ""
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for router:", formattedInput);
      const prompt = await routerPrompt.format(formattedInput);
      console.log("Router prompt:", prompt);
      return { prompt, originalInput: formattedInput };
    },
    async ({ prompt, originalInput }) => {
      const result = await model.invoke(prompt);
      return { result, originalInput };
    },
    ({ result, originalInput }) => {
      const parsed = new CustomStringOutputParser().parse(result);
      return { output: parsed, originalInput };
    },
    ({ output, originalInput }) => {
      const route = output.toLowerCase().trim();
      console.log("Router determined route:", route);
      
      // Validate route
      const validRoutes = ["product_info", "order_info", "user_info", "faq", "general"];
      const finalRoute = validRoutes.includes(route) ? route : "general";
      
      return { route: finalRoute, ...originalInput };
    }
  ]);

  // Product info node with MCP integration
  const productInfoPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    Product Information:
    {product_data}

    Previous conversation:
    {history}

    User message: {input}

    Based on the product information above, provide a helpful response to the user's question about our products.`
  );

  const productInfoChain = RunnableSequence.from([
    async (state) => {
      console.log("Product info state:", state);
      const productData = await mcpClient.getProductInfo(state.input || "");
      return {
        input: state.input || "",
        history: state.history || "",
        product_data: productData
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for product info:", formattedInput);
      const prompt = await productInfoPrompt.format(formattedInput);
      console.log("Product info prompt:", prompt);
      return prompt;
    },
    model,
    new CustomStringOutputParser()
  ]);

  // Order info node with MCP integration
  const orderInfoPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    Order Information:
    {order_data}

    Previous conversation:
    {history}

    User message: {input}

    Based on the order information above, provide a helpful response to the user's question about their orders.`
  );

  const orderInfoChain = RunnableSequence.from([
    async (state) => {
      console.log("Order info state:", state);
      const orderData = await mcpClient.getOrderInfo(state.input || "", user);
      return {
        input: state.input || "",
        history: state.history || "",
        order_data: orderData
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for order info:", formattedInput);
      const prompt = await orderInfoPrompt.format(formattedInput);
      console.log("Order info prompt:", prompt);
      return prompt;
    },
    model,
    new CustomStringOutputParser()
  ]);

  // User info node with MCP integration
  const userInfoPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    User Information:
    {user_data}

    Previous conversation:
    {history}

    User message: {input}

    Based on the user information above, provide a helpful response to the user's question about their account.`
  );

  const userInfoChain = RunnableSequence.from([
    async (state) => {
      console.log("User info state:", state);
      const userData = await mcpClient.getUserInfo(state.input || "", user);
      return {
        input: state.input || "",
        history: state.history || "",
        user_data: userData
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for user info:", formattedInput);
      const prompt = await userInfoPrompt.format(formattedInput);
      console.log("User info prompt:", prompt);
      return prompt;
    },
    model,
    new CustomStringOutputParser()
  ]);

  // FAQ node with MCP integration
  const faqPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    FAQ Information:
    {faq_data}

    Previous conversation:
    {history}

    User message: {input}

    Based on the FAQ information above, provide a helpful response to the user's question.`
  );

  const faqChain = RunnableSequence.from([
    async (state) => {
      console.log("FAQ state:", state);
      const faqData = await mcpClient.getFAQInfo(state.input || "");
      return {
        input: state.input || "",
        history: state.history || "",
        faq_data: faqData
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for FAQ:", formattedInput);
      const prompt = await faqPrompt.format(formattedInput);
      console.log("FAQ prompt:", prompt);
      return prompt;
    },
    model,
    new CustomStringOutputParser()
  ]);

  // General response node
  const generalPrompt = PromptTemplate.fromTemplate(
    `${systemPrompt}

    Previous conversation:
    {history}

    User message: {input}

    Provide a helpful response to the user's question.`
  );

  const generalChain = RunnableSequence.from([
    (state) => {
      console.log("General state:", state);
      return {
        input: state.input || "",
        history: state.history || ""
      };
    },
    async (formattedInput) => {
      console.log("Formatted input for general:", formattedInput);
      const prompt = await generalPrompt.format(formattedInput);
      console.log("General prompt:", prompt);
      return prompt;
    },
    model,
    new CustomStringOutputParser()
  ]);

  // Create the state graph
  const graph = new StateGraph({
    channels: {
      input: {
        value: ""
      },
      history: {
        value: ""
      }
    }
  });

  console.log("Created state graph");

  // Add nodes to the graph
  graph.addNode("router", router);
  graph.addNode("product_info", productInfoChain);
  graph.addNode("order_info", orderInfoChain);
  graph.addNode("user_info", userInfoChain);
  graph.addNode("faq", faqChain);
  graph.addNode("general", generalChain);

  console.log("Added nodes to graph");

  // Add conditional edges
  graph.addEdge("router", "product_info", (_, result) => {
    console.log(`Router result:`, result, `, going to product_info: ${result.route === "product_info"}`);
    return result.route === "product_info";
  });
  graph.addEdge("router", "order_info", (_, result) => {
    console.log(`Router result:`, result, `, going to order_info: ${result.route === "order_info"}`);
    return result.route === "order_info";
  });
  graph.addEdge("router", "user_info", (_, result) => {
    console.log(`Router result:`, result, `, going to user_info: ${result.route === "user_info"}`);
    return result.route === "user_info";
  });
  graph.addEdge("router", "faq", (_, result) => {
    console.log(`Router result:`, result, `, going to faq: ${result.route === "faq"}`);
    return result.route === "faq";
  });
  graph.addEdge("router", "general", (_, result) => {
    console.log(`Router result:`, result, `, going to general: ${result.route === "general"}`);
    return result.route === "general";
  });

  // Add edges to END
  graph.addEdge("product_info", END);
  graph.addEdge("order_info", END);
  graph.addEdge("user_info", END);
  graph.addEdge("faq", END);
  graph.addEdge("general", END);

  // Set entry point
  graph.setEntryPoint("router");

  console.log("Graph configuration complete");

  // Compile and return the graph
  return graph.compile();
};

export default createBelillyAssistantGraph;
