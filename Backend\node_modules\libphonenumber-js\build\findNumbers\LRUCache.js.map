{"version": 3, "file": "LRUCache.js", "names": ["Node", "key", "value", "next", "prev", "L<PERSON><PERSON><PERSON>", "limit", "size", "head", "tail", "cache", "ensureLimit", "node", "remove", "put", "console", "log"], "sources": ["../../source/findNumbers/LRUCache.js"], "sourcesContent": ["// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\r\n\r\nclass Node {\r\n  constructor(key, value, next = null, prev = null) {\r\n    this.key = key;\r\n    this.value = value;\r\n    this.next = next;\r\n    this.prev = prev;\r\n  }\r\n}\r\n\r\nexport default class LRUCache {\r\n  //set default limit of 10 if limit is not passed.\r\n  constructor(limit = 10) {\r\n    this.size = 0;\r\n    this.limit = limit;\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.cache = {};\r\n  }\r\n\r\n  // Write Node to head of LinkedList\r\n  // update cache with Node key and Node reference\r\n  put(key, value){\r\n    this.ensureLimit();\r\n\r\n    if(!this.head){\r\n      this.head = this.tail = new Node(key, value);\r\n    }else{\r\n      const node = new Node(key, value, this.head);\r\n      this.head.prev = node;\r\n      this.head = node;\r\n    }\r\n\r\n    //Update the cache map\r\n    this.cache[key] = this.head;\r\n    this.size++;\r\n  }\r\n\r\n  // Read from cache map and make that node as new Head of LinkedList\r\n  get(key){\r\n    if(this.cache[key]){\r\n      const value = this.cache[key].value;\r\n\r\n      // node removed from it's position and cache\r\n      this.remove(key)\r\n      // write node again to the head of LinkedList to make it most recently used\r\n      this.put(key, value);\r\n\r\n      return value;\r\n    }\r\n\r\n    console.log(`Item not available in cache for key ${key}`);\r\n  }\r\n\r\n  ensureLimit(){\r\n    if(this.size === this.limit){\r\n      this.remove(this.tail.key)\r\n    }\r\n  }\r\n\r\n  remove(key){\r\n    const node = this.cache[key];\r\n\r\n    if(node.prev !== null){\r\n      node.prev.next = node.next;\r\n    }else{\r\n      this.head = node.next;\r\n    }\r\n\r\n    if(node.next !== null){\r\n      node.next.prev = node.prev;\r\n    }else{\r\n      this.tail = node.prev\r\n    }\r\n\r\n    delete this.cache[key];\r\n    this.size--;\r\n  }\r\n\r\n  clear() {\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.size = 0;\r\n    this.cache = {};\r\n  }\r\n\r\n  // // Invokes the callback function with every node of the chain and the index of the node.\r\n  // forEach(fn) {\r\n  //   let node = this.head;\r\n  //   let counter = 0;\r\n  //   while (node) {\r\n  //     fn(node, counter);\r\n  //     node = node.next;\r\n  //     counter++;\r\n  //   }\r\n  // }\r\n\r\n  // // To iterate over LRU with a 'for...of' loop\r\n  // *[Symbol.iterator]() {\r\n  //   let node = this.head;\r\n  //   while (node) {\r\n  //     yield node;\r\n  //     node = node.next;\r\n  //   }\r\n  // }\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA;IAEMA,I,6BACJ,cAAYC,GAAZ,EAAiBC,KAAjB,EAAkD;EAAA,IAA1BC,IAA0B,uEAAnB,IAAmB;EAAA,IAAbC,IAAa,uEAAN,IAAM;;EAAA;;EAChD,KAAKH,GAAL,GAAWA,GAAX;EACA,KAAKC,KAAL,GAAaA,KAAb;EACA,KAAKC,IAAL,GAAYA,IAAZ;EACA,KAAKC,IAAL,GAAYA,IAAZ;AACD,C;;IAGkBC,Q;EACnB;EACA,oBAAwB;IAAA,IAAZC,KAAY,uEAAJ,EAAI;;IAAA;;IACtB,KAAKC,IAAL,GAAY,CAAZ;IACA,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKE,IAAL,GAAY,IAAZ;IACA,KAAKC,IAAL,GAAY,IAAZ;IACA,KAAKC,KAAL,GAAa,EAAb;EACD,C,CAED;EACA;;;;;WACA,aAAIT,GAAJ,EAASC,KAAT,EAAe;MACb,KAAKS,WAAL;;MAEA,IAAG,CAAC,KAAKH,IAAT,EAAc;QACZ,KAAKA,IAAL,GAAY,KAAKC,IAAL,GAAY,IAAIT,IAAJ,CAASC,GAAT,EAAcC,KAAd,CAAxB;MACD,CAFD,MAEK;QACH,IAAMU,IAAI,GAAG,IAAIZ,IAAJ,CAASC,GAAT,EAAcC,KAAd,EAAqB,KAAKM,IAA1B,CAAb;QACA,KAAKA,IAAL,CAAUJ,IAAV,GAAiBQ,IAAjB;QACA,KAAKJ,IAAL,GAAYI,IAAZ;MACD,CATY,CAWb;;;MACA,KAAKF,KAAL,CAAWT,GAAX,IAAkB,KAAKO,IAAvB;MACA,KAAKD,IAAL;IACD,C,CAED;;;;WACA,aAAIN,GAAJ,EAAQ;MACN,IAAG,KAAKS,KAAL,CAAWT,GAAX,CAAH,EAAmB;QACjB,IAAMC,KAAK,GAAG,KAAKQ,KAAL,CAAWT,GAAX,EAAgBC,KAA9B,CADiB,CAGjB;;QACA,KAAKW,MAAL,CAAYZ,GAAZ,EAJiB,CAKjB;;QACA,KAAKa,GAAL,CAASb,GAAT,EAAcC,KAAd;QAEA,OAAOA,KAAP;MACD;;MAEDa,OAAO,CAACC,GAAR,+CAAmDf,GAAnD;IACD;;;WAED,uBAAa;MACX,IAAG,KAAKM,IAAL,KAAc,KAAKD,KAAtB,EAA4B;QAC1B,KAAKO,MAAL,CAAY,KAAKJ,IAAL,CAAUR,GAAtB;MACD;IACF;;;WAED,gBAAOA,GAAP,EAAW;MACT,IAAMW,IAAI,GAAG,KAAKF,KAAL,CAAWT,GAAX,CAAb;;MAEA,IAAGW,IAAI,CAACR,IAAL,KAAc,IAAjB,EAAsB;QACpBQ,IAAI,CAACR,IAAL,CAAUD,IAAV,GAAiBS,IAAI,CAACT,IAAtB;MACD,CAFD,MAEK;QACH,KAAKK,IAAL,GAAYI,IAAI,CAACT,IAAjB;MACD;;MAED,IAAGS,IAAI,CAACT,IAAL,KAAc,IAAjB,EAAsB;QACpBS,IAAI,CAACT,IAAL,CAAUC,IAAV,GAAiBQ,IAAI,CAACR,IAAtB;MACD,CAFD,MAEK;QACH,KAAKK,IAAL,GAAYG,IAAI,CAACR,IAAjB;MACD;;MAED,OAAO,KAAKM,KAAL,CAAWT,GAAX,CAAP;MACA,KAAKM,IAAL;IACD;;;WAED,iBAAQ;MACN,KAAKC,IAAL,GAAY,IAAZ;MACA,KAAKC,IAAL,GAAY,IAAZ;MACA,KAAKF,IAAL,GAAY,CAAZ;MACA,KAAKG,KAAL,GAAa,EAAb;IACD,C,CAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA"}