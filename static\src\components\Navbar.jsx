// Navbar.jsx - Static site navigation

import React from "react";
import { Link } from "react-router-dom";

const Navbar = () => {
  return (
    <nav className="bg-white fixed w-full top-0 z-50 border-gray-200 shadow-sm">
      <div className="px-4 relative flex justify-between items-center h-14">
        <Link to="/" className="flex-shrink-0 flex items-center">
          <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
            Belilly
          </span>
        </Link>

        <div className="flex items-center space-x-4">
          {/* Navigation Links */}
          <Link 
            to="/about" 
            className="text-gray-600 hover:text-indigo-600 transition-colors text-sm font-medium"
          >
            About
          </Link>
          <Link 
            to="/contact" 
            className="text-gray-600 hover:text-indigo-600 transition-colors text-sm font-medium"
          >
            Contact
          </Link>
          
          {/* Link back to main store */}
          <a
            href={import.meta.env.VITE_MAIN_STORE_URL || "http://localhost:5173"}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors"
          >
            Shop Now
          </a>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
