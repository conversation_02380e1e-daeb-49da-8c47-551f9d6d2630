// services/onlinePurchase.js

import { addData } from "../utils/access_crud.js";
import { initiatePayment } from "../service/paymentservice.js";

export const processOnlinePurchase = async ({ req, purchaseData, phone, price, transaction_id }) => {
  // Rename transaction_id to transactionId for consistency with the payment service
  const transactionId = transaction_id;

  // Check if required parameters exist
  if (!phone) {
    throw new Error("Please update your phone number in your profile to proceed with online payment");
  }

  if (!price) {
    throw new Error("Price is missing. Please try again or contact support.");
  }

  if (!transactionId) {
    throw new Error("Transaction ID is missing. Please try again or contact support.");
  }

  const PurchasesOnline = req.app.locals.PurchasesOnline;

  // Log the purchase data before inserting
  console.log("Purchase data to be inserted:", purchaseData);

  try {
    const paymentRecord = await addData(PurchasesOnline, [purchaseData]);
    console.log("Payment record after insertion:", paymentRecord);

    // If data array is empty but status is 201, it means the record was created but not returned
    if (paymentRecord.status === 201 && (!paymentRecord.data || paymentRecord.data.length === 0)) {
      console.log("Record created but no data returned. This is normal with some MongoDB drivers.");
    }

    // Process payment
    const payload = { phone, price, transactionId };
    try {
      const paymentResponse = await initiatePayment(payload);
      console.log("PhonePe payment response:", paymentResponse);

      const redirectUrl = paymentResponse?.data?.instrumentResponse?.redirectInfo?.url || null;
      if (!redirectUrl) {
        console.error("No redirect URL in PhonePe response:", paymentResponse);
        throw new Error("Redirect URL not found in payment response");
      }
      return { message: "Purchase initiated!", url: redirectUrl };
    } catch (error) {
      console.error("Error in processOnlinePurchase:", error);

      // Check if the error is related to missing phone number
      if (error.message.includes("Missing required parameters") && !phone) {
        throw new Error("Please update your phone number in your profile to proceed with online payment");
      }

      // Check for PhonePe service issues
      if (error.message.includes("Request failed with status code 526") ||
          error.message.includes("PhonePe API configuration error")) {
        throw new Error("Payment service is temporarily unavailable. Please try Cash on Delivery or contact support.");
      }

      throw new Error(`Payment processing error: ${error.message}`);
    }
  } catch (error) {
    console.error("Error in processOnlinePurchase:", error);
    throw new Error("Payment record creation failed: " + error.message);
  }
};
