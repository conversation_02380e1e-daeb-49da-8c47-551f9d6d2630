// purchaseController.js

import { processOnlinePurchase } from "../service/onlinepurchase.js";
import { processCodPurchase } from "../service/codpurchase.js";
import { getProductFromSearch } from "../service/searchproduct.js";

export const processPurchase = async (req, res) => {
  const { size, quantity, color, payment_method, price, product_id } = req.body;

  if (!size || !color || !quantity || !payment_method || !price || !product_id) {
    return res.status(400).json({ message: "All fields are required." });
  }

  try {
    const product = await getProductFromSearch({ product_id, color, price }, req.app);
    console.log(product);
    if (!product) {
      return res.status(404).json({ message: "Product not found." });
    }

    const email = req.user.email;
    const phone = req.user.phone;

    // Enhanced IP address extraction to handle Nginx proxy headers
    // When behind Nginx, x-forwarded-for will contain the client's real IP
    let ip_address = req.headers["x-forwarded-for"];
    if (ip_address) {
      // x-forwarded-for can contain multiple IPs (client, proxy1, proxy2, ...)
      // The leftmost one is the original client IP
      const ips = ip_address.split(',');
      ip_address = ips[0].trim();
    } else {
      // Fallback to other methods if x-forwarded-for is not available
      ip_address = req.headers["x-real-ip"] || req.socket.remoteAddress || req.ip;
    }
    const transaction_id = `MT-${Math.floor(Math.random() * (99999999999 - 1111111111 + 1) + 1111111111)}`;
    const orderId = `${Math.floor(Math.random() * (99999999999 - 1111111111 + 1) + 1111111111)}`;

    const purchaseData = {
      email: email,
      product_id: product_id,
      color: color,
      size: size,
      price: price,
      quantity: quantity,
      order_id: orderId,
      order_status: "Initiated",
      transaction_id: transaction_id,
      transaction_status: "Initiated",
      ip_address: ip_address,
      payment_method: payment_method,
    };

    if (payment_method === "Online") {
      try {
        // Check if required parameters exist
        if (!phone) {
          return res.status(400).json({
            message: "Missing phone number",
            error: "Please update your phone number"
          });
        }

        if (!price) {
          return res.status(400).json({
            message: "Missing price",
            error: "Price is missing. Please try again or contact support."
          });
        }

        if (!transaction_id) {
          return res.status(400).json({
            message: "Missing transaction ID",
            error: "Transaction ID is missing. Please try again or contact support."
          });
        }

        const onlineResponse = await processOnlinePurchase({ req, purchaseData, phone, price, transaction_id });
        return res.status(201).json(onlineResponse);
      } catch (onlineError) {
        console.error("Payment Processing Error:", onlineError);
        // Check if the error is related to missing phone number
        if (onlineError.message.includes("phone number")) {
          return res.status(400).json({
            message: "Missing phone number",
            error: onlineError.message
          });
        }
        return res.status(500).json({
          message: "Payment processing error.",
          error: onlineError.message,
        });
      }
    } else if (payment_method === "COD") {

      try {
        const codResponse = await processCodPurchase({ req, purchaseData, phone, price, transaction_id });
        return res.status(201).json(codResponse);
      } catch (codError) {
        console.error("COD Processing Error:", codError);
        return res.status(500).json({
          message: "COD purchase processing error.",
          error: codError.message,
        });
      }
    } else {

      return res.status(400).json({ message: "Invalid payment method." });
    }
  }

  catch (error) {
    console.error("Process Purchase Error:", error);
    return res.status(500).json({ message: "Something went wrong.", error: error.message });
  }
};
