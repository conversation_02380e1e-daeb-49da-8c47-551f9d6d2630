/**
 * CSRF Protection middleware
 *
 * This module provides CSRF protection using the modern csrf-csrf library,
 * which is the recommended replacement for the deprecated csurf package.
 *
 * It supports both Redis and in-memory storage for tokens with automatic fallback.
 */

import { doubleCsrf } from 'csrf-csrf';
import { StatusCodes } from 'http-status-codes';
import { logger } from './errorhandler.js';

// Try to import Redis client if available
let redisClient = null;

try {
  // Import Redis dynamically to avoid breaking if <PERSON><PERSON> is not configured
  const { createClient } = await import('redis');

  // Check if Redis configuration is available
  if (process.env.REDIS_HOST) {
    redisClient = createClient({
      url: `redis://${process.env.REDIS_PASSWORD ? process.env.REDIS_PASSWORD + '@' : ''}${process.env.REDIS_HOST}:${process.env.REDIS_PORT || 6379}`,
      socket: {
        reconnectStrategy: (retries) => {
          // Limit reconnection attempts to 3 with exponential backoff
          if (retries >= 3) {
            logger.warn('Max Redis reconnection attempts reached for CSRF protection. Using in-memory store.');
            return false; // Stop reconnecting
          }

          // Exponential backoff with max delay of 5 seconds
          const delay = Math.min(Math.pow(2, retries) * 100, 5000);
          logger.info(`Redis CSRF protection reconnecting in ${delay}ms... (attempt ${retries + 1}/3)`);
          return delay;
        },
        connectTimeout: 3000 // 3 second connection timeout
      }
    });

    // Set up event handlers
    redisClient.on('error', (err) => {
      logger.error('Redis CSRF protection client error:', err);
    });

    // Connect to Redis with a timeout
    try {
      // Set a timeout for the connection attempt
      const connectPromise = redisClient.connect();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis connection timeout')), 3000);
      });

      // Race the connection against the timeout
      await Promise.race([connectPromise, timeoutPromise]);
      logger.info('Redis client connected for CSRF token storage');
    } catch (connectError) {
      logger.warn(`Redis connection failed for CSRF protection: ${connectError.message}. Using in-memory store.`);
      redisClient = null;
    }
  }
} catch (error) {
  logger.warn('Redis client not available for CSRF token storage, using in-memory store:', error.message);
  redisClient = null;
}

/**
 * Custom storage for CSRF tokens with Redis support
 */
const tokenStorage = {
  // In-memory fallback store
  tokens: new Map(),

  // Save token to storage
  async saveToken(token, userId) {
    const expirySeconds = 3600; // 1 hour

    if (redisClient) {
      try {
        await redisClient.set(
          `csrf:${token}`,
          userId,
          { EX: expirySeconds }
        );
        return true;
      } catch (error) {
        logger.error('Error storing CSRF token in Redis:', error);
        // Fallback to in-memory
        this.tokens.set(token, {
          userId,
          expires: Date.now() + (expirySeconds * 1000)
        });
        return true;
      }
    } else {
      // Use in-memory store
      this.tokens.set(token, {
        userId,
        expires: Date.now() + (expirySeconds * 1000)
      });

      // Clean up expired tokens periodically (5% chance)
      if (Math.random() < 0.05) {
        this.cleanupExpiredTokens();
      }

      return true;
    }
  },

  // Get token from storage
  async getToken(token) {
    if (redisClient) {
      try {
        return await redisClient.get(`csrf:${token}`);
      } catch (error) {
        logger.error('Error retrieving CSRF token from Redis:', error);
        // Fallback to in-memory
        const data = this.tokens.get(token);
        if (!data || data.expires < Date.now()) {
          return null;
        }
        return data.userId;
      }
    } else {
      // Use in-memory store
      const data = this.tokens.get(token);
      if (!data || data.expires < Date.now()) {
        return null;
      }
      return data.userId;
    }
  },

  // Clean up expired tokens from in-memory store
  cleanupExpiredTokens() {
    const now = Date.now();
    for (const [token, data] of this.tokens.entries()) {
      if (data.expires < now) {
        this.tokens.delete(token);
      }
    }
  }
};

// Validate CSRF secret key
function validateCsrfSecret() {
  if (!process.env.CSRF_SECRET) {
    logger.error('CRITICAL SECURITY ERROR: CSRF_SECRET environment variable is not set');
    throw new Error('CSRF_SECRET environment variable is required for security');
  }

  // Ensure the secret is sufficiently long (at least 32 characters)
  if (process.env.CSRF_SECRET.length < 32) {
    logger.error('CRITICAL SECURITY ERROR: CSRF_SECRET is too short (must be at least 32 characters)');
    throw new Error('CSRF_SECRET must be at least 32 characters long');
  }
}

// Validate the CSRF secret on startup
validateCsrfSecret();

// Configure CSRF protection options
const csrfProtectionConfig = {
  getSecret: () => process.env.CSRF_SECRET,
  cookieName: '_csrf',
  cookieOptions: {
    httpOnly: true,
    sameSite: 'strict',
    // Always use secure cookies when behind Nginx with SSL
    secure: true,
    path: '/',
    maxAge: 3600 * 1000, // 1 hour
    // Add domain restriction if specified
    ...(process.env.COOKIE_DOMAIN ? { domain: process.env.COOKIE_DOMAIN } : {}),
    // Prevent cookie from being accessed by client-side JavaScript
    httpOnly: true
  },
  size: 64, // token size in bytes (larger is more secure)
  ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
  getTokenFromRequest: (req) => req.headers['x-csrf-token'] || req.body._csrf,
};

// Create CSRF protection middleware
const { generateToken, doubleCsrfProtection: csrfMiddleware } = doubleCsrf(csrfProtectionConfig);

/**
 * Custom CSRF protection middleware with user-specific tokens
 * @returns {Function} - Express middleware
 */
export const csrfProtection = () => {
  return async (req, res, next) => {
    // Skip CSRF check for ignored methods
    if (csrfProtectionConfig.ignoredMethods.includes(req.method)) {
      return next();
    }

    // Skip CSRF check for social login, logout, profile, product, and address routes
    if (req.path.includes('/auth/google') || req.path.includes('/auth/facebook') || req.path.includes('/auth/logout') || req.path.includes('/profile') || req.path.includes('/product') || req.path.includes('/addresses')) {
      return next();
    }

    // Get CSRF token from request
    const token = csrfProtectionConfig.getTokenFromRequest(req);

    // Get user ID from session, cookie, or JWT token
    const userId = req.user?.email || req.session?.userId || req.cookies?.sessionId;

    // If no user ID, skip CSRF check (unauthenticated request)
    if (!userId) {
      return next();
    }

    // If no token, return error
    if (!token) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'CSRF token missing'
      });
    }

    try {
      // Verify token with standard CSRF middleware first
      csrfMiddleware(req, res, async (err) => {
        if (err) {
          return res.status(StatusCodes.FORBIDDEN).json({
            success: false,
            message: 'Invalid CSRF token'
          });
        }

        // Additional check: verify token belongs to this user
        const tokenUserId = await tokenStorage.getToken(token);

        if (!tokenUserId || tokenUserId !== userId) {
          return res.status(StatusCodes.FORBIDDEN).json({
            success: false,
            message: 'Invalid or expired CSRF token'
          });
        }

        next();
      });
    } catch (error) {
      logger.error('Error verifying CSRF token:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Error processing security token'
      });
    }
  };
};

/**
 * Generate a CSRF token for a specific user
 * @param {string} userId - User ID or session ID
 * @returns {Promise<string>} - Generated CSRF token
 */
export const generateCsrfToken = async (userId) => {
  // Generate token using csrf-csrf
  const token = generateToken();

  // Store token with user association
  await tokenStorage.saveToken(token, userId);

  return token;
};

/**
 * middleware to attach CSRF token to response
 * @returns {Function} - Express middleware
 */
export const attachCsrfToken = () => {
  return async (req, res, next) => {
    // Only attach token for GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Get user ID from session, cookie, or JWT token
    const userId = req.user?.email || req.session?.userId || req.cookies?.sessionId;

    // If no user ID, skip (unauthenticated request)
    if (!userId) {
      return next();
    }

    try {
      // Generate a new CSRF token
      const token = await generateCsrfToken(userId);

      // Set the CSRF cookie (handled by csrf-csrf)
      res.cookie(csrfProtectionConfig.cookieName, token, csrfProtectionConfig.cookieOptions);

      // Also set a non-HttpOnly cookie for JavaScript access
      res.cookie('XSRF-TOKEN', token, {
        // Copy base options but override httpOnly
        sameSite: csrfProtectionConfig.cookieOptions.sameSite,
        // Always use secure cookies when behind Nginx with SSL
        secure: true,
        path: csrfProtectionConfig.cookieOptions.path,
        maxAge: csrfProtectionConfig.cookieOptions.maxAge,
        // Add domain restriction if specified
        ...(process.env.COOKIE_DOMAIN ? { domain: process.env.COOKIE_DOMAIN } : {}),
        // Client-side JavaScript needs to read this
        httpOnly: false,
        // Add SameSite protection
        sameSite: 'strict'
      });

      // Also attach to response locals for template rendering
      res.locals.csrfToken = token;

      next();
    } catch (error) {
      logger.error('Error generating CSRF token:', error);
      next();
    }
  };
};

export default {
  generateCsrfToken,
  csrfProtection,
  csrfMiddleware,
  attachCsrfToken
};
