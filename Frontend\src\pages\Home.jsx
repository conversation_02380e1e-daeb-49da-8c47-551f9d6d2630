// Home.jsx

import React, { useEffect } from "react";
import Navbar from "../components/Navbar";
import Navbar_large from "../components/Navbar_large";
import SearchBar from "../components/SearchBar";
import StickyNavbar from "../components/StickyNavbar";
import PageTransition from "../components/PageTransition";
import Category_linear from "../components/assets/Category_linear";
import Carousel_01 from "../components/assets/Carousel_01";
import Carousel_02 from "../components/assets/Carousel_02";
import Carousel_04 from "../components/assets/Carousel_04";
import Carousel_10 from "../components/assets/Carousel_10";
import Carousel_13 from "../components/assets/Carousel_13";
import { defaultProduct } from "../utils/imageConfig";

const Home = () => {
    useEffect(() => {
        // Preload resources
        const preloadImages = () => {
            // Add images
        };
        preloadImages();
        sessionStorage.setItem('homeVisited', 'true');
    }, []);

    const Carousel_01_men = [
        { name: "Casual Shirts", price: "Fresh Styles", imageurl: defaultProduct, link: "/category/men/shirt" },
        { name: "Formal Shirts", price: "Must-Haves", imageurl: defaultProduct, link: "/category/men/shirt" },
        { name: "Tshirts", price: "Season's Best", imageurl: defaultProduct, link: "/category/men/tshirt  " },
        { name: "Polo", price: "Exclusive", imageurl: defaultProduct, link: "/category/men/polo" },
        { name: "Cuban", price: "Season's Best", imageurl: defaultProduct, link: "/category/men/tshirt" },
        { name: "Raglan", price: "Exclusive", imageurl: defaultProduct, link: "/category/men/raglan" }
    ];
    const Carousel_13_men_1 = [
        { name: "Summer Dresses", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/men" },
        { name: "Indianwear Classics", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "New Arrivals", price: "Min 10% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "Western Wear", price: "From ₹499", imageurl: defaultProduct, link: "/category/men" },
        { name: "Ethnic Collection", price: "30-50% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "Accessories", price: "Buy 2 Get 1 Free", imageurl: defaultProduct, link: "/category/men" }
    ];
    const Carousel_13_men_2 = [
        { name: "Summer Dresses", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/men" },
        { name: "Indianwear Classics", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "New Arrivals", price: "Min 10% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "Western Wear", price: "From ₹499", imageurl: defaultProduct, link: "/category/men" },
        { name: "Ethnic Collection", price: "30-50% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "Accessories", price: "Buy 2 Get 1 Free", imageurl: defaultProduct, link: "/category/men" }
    ];
    const Carousel_10_men = [
        { name: "Featured Products", price: "Up to 60% off", imageurl: defaultProduct, link: "/category/men" },
        { name: "Best Sellers", price: "Starting ₹299", imageurl: defaultProduct, link: "/category/men" },
        { name: "New Arrivals", price: "Latest Collection", imageurl: defaultProduct, link: "/category/new" },
        { name: "Clearance Sale", price: "Extra 20% off", imageurl: defaultProduct, link: "/category/sale" },
        { name: "New Arrivals", price: "Latest Collection", imageurl: defaultProduct, link: "/category/new" },
        { name: "Clearance Sale", price: "Extra 20% off", imageurl: defaultProduct, link: "/category/men" }
    ];



    const Carousel_01_women = [
        { name: "Tshirt", price: "Fresh Styles", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Crop Top", price: "Season's Best", imageurl: defaultProduct, link: "/category/Women/croptop" },
        { name: "Polo", price: "Must-Haves", imageurl: defaultProduct, link: "/category/Women/polo" },
        { name: "Raglan", price: "Exclusive", imageurl: defaultProduct, link: "/category/Women/raglan" },
        { name: "Casual Shirt", price: "Season's Best", imageurl: defaultProduct, link: "/category/Women/shirt" },
        { name: "Formal Shirt", price: "Exclusive", imageurl: defaultProduct, link: "/category/Women/shirt" }
    ];
    const Carousel_13_women_1 = [
        { name: "Cuban", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Shorts", price: "From ₹499", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Crop Tank", price: "Min 10% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Graphic Tees", price: "From ₹499", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Bra and Panties", price: "30-50% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Leggings", price: "Buy 2 Get 1 Free", imageurl: defaultProduct, link: "/category/Women/tshirt" }

    ];
    const Carousel_13_women_2 = [
        { name: "Coords", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Dress", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Sweatshirt", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Tank Top", price: "Min 10% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Jackets", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Joggers", price: "30-50% off", imageurl: defaultProduct, link: "/category/Women/tshirt" }
    ];

    const Carousel_10_women = [
        { name: "Featured Products", price: "Up to 60% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Best Sellers", price: "Starting ₹299", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "New Arrivals", price: "Latest Collection", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Clearance Sale", price: "Extra 20% off", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "New Arrivals", price: "Latest Collection", imageurl: defaultProduct, link: "/category/Women/tshirt" },
        { name: "Clearance Sale", price: "Extra 20% off", imageurl: defaultProduct, link: "/category/Women/tshirt" }
    ];



    const Carousel_01_kids = [
        { name: "New Arrivals", price: "Fresh Styles", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Best Sellers", price: "Must-Haves", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Spring Collection", price: "Season's Best", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Designer Picks", price: "Exclusive", imageurl: defaultProduct, link: "/category/kids" }
    ];
    const Carousel_13_kids_1 = [
        { name: "Summer Dresses", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Indianwear Classics", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "New Arrivals", price: "Min 10% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Western Wear", price: "From ₹499", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Ethnic Collection", price: "30-50% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Accessories", price: "Buy 2 Get 1 Free", imageurl: defaultProduct, link: "/category/kids" }
    ];
    const Carousel_13_kids_2 = [
        { name: "Summer Dresses", price: "Starting at ₹399", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Indianwear Classics", price: "Upto 70% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "New Arrivals", price: "Min 10% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Western Wear", price: "From ₹499", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Ethnic Collection", price: "30-50% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Accessories", price: "Buy 2 Get 1 Free", imageurl: defaultProduct, link: "/category/kids" }
    ];
    const Carousel_10_kids = [
        { name: "Featured Products", price: "Up to 60% off", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Best Sellers", price: "Starting ₹299", imageurl: defaultProduct, link: "/category/kids" },
        { name: "New Arrivals", price: "Latest Collection", imageurl: defaultProduct, link: "/category/kids" },
        { name: "Clearance Sale", price: "Extra 20% off", imageurl: defaultProduct, link: "/category/kids" }
    ];

    return (
        <div>
            <PageTransition />
            <div className="pt-14 md:hidden"><Navbar /><SearchBar /><Category_linear /><Carousel_04 /><StickyNavbar /></div>
            <div className="hidden h-screen md:block"><Navbar_large /><Carousel_02 /></div>
            
            <Carousel_01 title="MENS FASHION" categories={Carousel_01_men} bannerImageUrl="https://images.pexels.com/photos/6311586/pexels-photo-6311586.jpeg"/>
            <Carousel_13 title="TRENDING FASHION" categories={Carousel_13_men_1} backgroundImage="https://img.freepik.com/premium-photo/white-cement-marble-texture-with-natural-pattern-background_1715-2103.jpg?w=996"/>
            <Carousel_13 title="SUMMER COLLECTION" categories={Carousel_13_men_2} backgroundImage="https://img.freepik.com/premium-photo/white-cement-marble-texture-with-natural-pattern-background_1715-2103.jpg?w=996"/>
            <Carousel_10 title="SPECIAL OFFERS" categories={Carousel_10_men} backgroundImage="https://img.freepik.com/free-photo/aesthetic-wallpaper-pink-smoke-background_53876-138232.jpg"/>

            <Carousel_01 title="WOMENS NEW" categories={Carousel_01_women} bannerImageUrl="https://images.pexels.com/photos/6311586/pexels-photo-6311586.jpeg"/>
            <Carousel_13 title="ETHNIC ELEGANCE" categories={Carousel_13_women_1} backgroundImage="https://img.freepik.com/free-photo/aesthetic-wallpaper-pink-smoke-background_53876-138232.jpg?t=st=1746556851~exp=1746560451~hmac=dbcf666bec420865ebefd7c275c882f36c475ae0adf42f7597be3c1fcac80ebe&w=996"/>
            <Carousel_13 title="ACCESSORIES COLLECTION" categories={Carousel_13_women_2} backgroundImage="https://img.freepik.com/free-photo/aesthetic-wallpaper-pink-smoke-background_53876-138232.jpg?t=st=1746556851~exp=1746560451~hmac=dbcf666bec420865ebefd7c275c882f36c475ae0adf42f7597be3c1fcac80ebe&w=996"/>
            <Carousel_10 title="CLEARANCE DEALS" categories={Carousel_10_women} backgroundImage="https://img.freepik.com/premium-photo/background_1150648-26.jpg?w=740"/>

            <Carousel_01 title="KIDS FASHION" categories={Carousel_01_kids} bannerImageUrl="https://images.pexels.com/photos/6311586/pexels-photo-6311586.jpeg"/>
            <Carousel_13 title="WINTER ESSENTIALS" categories={Carousel_13_kids_1} backgroundImage="https://img.freepik.com/premium-photo/background_1150648-26.jpg?w=740"/>
            <Carousel_13 title="FOOTWEAR FAVORITES" categories={Carousel_13_kids_2} backgroundImage="https://img.freepik.com/premium-photo/background_1150648-26.jpg?w=740"/>
            <Carousel_10 title="CLEARANCE DEALS" categories={Carousel_10_kids} backgroundImage="https://img.freepik.com/premium-photo/background_1150648-26.jpg?w=740"/>
        </div>
    );
};

export default Home;
