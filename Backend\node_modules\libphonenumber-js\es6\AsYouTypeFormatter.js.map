{"version": 3, "file": "AsYouTypeFormatter.js", "names": ["DIGIT_PLACEHOLDER", "countOccurences", "repeat", "cutAndStripNonPairedParens", "closeNonPairedParens", "stripNonPairedParens", "populateTemplateWithDigits", "formatCompleteNumber", "canFormatCompleteNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseDigits", "FIRST_GROUP_PATTERN", "VALID_PUNCTUATION", "applyInternationalSeparatorStyle", "DUMMY_DIGIT", "LONGEST_NATIONAL_PHONE_NUMBER_LENGTH", "LONGEST_DUMMY_PHONE_NUMBER", "NATIONAL_PREFIX_SEPARATORS_PATTERN", "SUPPORT_LEGACY_FORMATTING_PATTERNS", "CREATE_CHARACTER_CLASS_PATTERN", "CREATE_STANDALONE_DIGIT_PATTERN", "NON_ALTERING_FORMAT_REG_EXP", "RegExp", "MIN_LEADING_DIGITS_LENGTH", "AsYouTypeFormatter", "state", "metadata", "resetFormat", "chosenFormat", "undefined", "template", "nationalNumberTemplate", "populatedNationalNumberTemplate", "populatedNationalNumberTemplatePosition", "numberingPlan", "isNANP", "callingCode", "matchingFormats", "formats", "nationalSignificantNumber", "narrowDownMatchingFormats", "nextDigits", "format", "formattedCompleteNumber", "shouldTryNationalPrefixFormattingRule", "international", "nationalPrefix", "getSeparatorAfterNationalPrefix", "setNationalNumberTemplate", "replace", "lastIndexOf", "formatNationalNumberWithNextDigits", "previouslyChosenFormat", "newlyChosenFormat", "chooseFormat", "formatNextNationalNumberDigits", "getNationalDigits", "leadingDigits", "leadingDigitsPatternIndex", "length", "filter", "formatSuits", "formatMatches", "indexOf", "usesNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "leadingDigitsPatternsCount", "leadingDigitsPatterns", "Math", "min", "leadingDigitsPattern", "match", "allowOverflow", "error", "console", "test", "internationalFormat", "getFormatFormat", "createTemplateForFormat", "_", "slice", "pattern", "getTemplateForFormat", "nationalPrefixFormattingRule", "options", "IDDPrefix", "missingPlus", "spacing", "index", "i", "internationalPrefix", "getInternationalPrefixBeforeCountryCallingCode", "getDigitsWithoutInternationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "digits", "strictPattern", "nationalNumberDummyDigits", "numberFormat", "nationalPrefixIncludedInTemplate", "numberFormatWithNationalPrefix", "result"], "sources": ["../source/AsYouTypeFormatter.js"], "sourcesContent": ["import {\r\n\tDIGIT_PLACEHOLDER,\r\n\tcountOccurences,\r\n\trepeat,\r\n\tcutAndStripNonPairedParens,\r\n\tcloseNonPairedParens,\r\n\tstripNonPairedParens,\r\n\tpopulateTemplateWithDigits\r\n} from './AsYouTypeFormatter.util.js'\r\n\r\nimport formatCompleteNumber, {\r\n\tcanFormatCompleteNumber\r\n} from './AsYouTypeFormatter.complete.js'\r\n\r\nimport PatternMatcher from './AsYouTypeFormatter.PatternMatcher.js'\r\n\r\nimport parseDigits from './helpers/parseDigits.js'\r\nexport { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.util.js'\r\nimport { FIRST_GROUP_PATTERN } from './helpers/formatNationalNumberUsingFormat.js'\r\nimport { VALID_PUNCTUATION } from './constants.js'\r\nimport applyInternationalSeparatorStyle from './helpers/applyInternationalSeparatorStyle.js'\r\n\r\n// Used in phone number format template creation.\r\n// Could be any digit, I guess.\r\nconst DUMMY_DIGIT = '9'\r\n// I don't know why is it exactly `15`\r\nconst LONGEST_NATIONAL_PHONE_NUMBER_LENGTH = 15\r\n// Create a phone number consisting only of the digit 9 that matches the\r\n// `number_pattern` by applying the pattern to the \"longest phone number\" string.\r\nconst LONGEST_DUMMY_PHONE_NUMBER = repeat(DUMMY_DIGIT, LONGEST_NATIONAL_PHONE_NUMBER_LENGTH)\r\n\r\n// A set of characters that, if found in a national prefix formatting rules, are an indicator to\r\n// us that we should separate the national prefix from the number when formatting.\r\nconst NATIONAL_PREFIX_SEPARATORS_PATTERN = /[- ]/\r\n\r\n// Deprecated: Google has removed some formatting pattern related code from their repo.\r\n// https://github.com/googlei18n/libphonenumber/commit/a395b4fef3caf57c4bc5f082e1152a4d2bd0ba4c\r\n// \"We no longer have numbers in formatting matching patterns, only \\d.\"\r\n// Because this library supports generating custom metadata\r\n// some users may still be using old metadata so the relevant\r\n// code seems to stay until some next major version update.\r\nconst SUPPORT_LEGACY_FORMATTING_PATTERNS = true\r\n\r\n// A pattern that is used to match character classes in regular expressions.\r\n// An example of a character class is \"[1-4]\".\r\nconst CREATE_CHARACTER_CLASS_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && (() => /\\[([^\\[\\]])*\\]/g)\r\n\r\n// Any digit in a regular expression that actually denotes a digit. For\r\n// example, in the regular expression \"80[0-2]\\d{6,10}\", the first 2 digits\r\n// (8 and 0) are standalone digits, but the rest are not.\r\n// Two look-aheads are needed because the number following \\\\d could be a\r\n// two-digit number, since the phone number can be as long as 15 digits.\r\nconst CREATE_STANDALONE_DIGIT_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && (() => /\\d(?=[^,}][^,}])/g)\r\n\r\n// A regular expression that is used to determine if a `format` is\r\n// suitable to be used in the \"as you type formatter\".\r\n// A `format` is suitable when the resulting formatted number has\r\n// the same digits as the user has entered.\r\n//\r\n// In the simplest case, that would mean that the format\r\n// doesn't add any additional digits when formatting a number.\r\n// Google says that it also shouldn't add \"star\" (`*`) characters,\r\n// like it does in some Israeli formats.\r\n// Such basic format would only contain \"valid punctuation\"\r\n// and \"captured group\" identifiers ($1, $2, etc).\r\n//\r\n// An example of a format that adds additional digits:\r\n//\r\n// Country: `AR` (Argentina).\r\n// Format:\r\n// {\r\n//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n//    \"leading_digits_patterns\": [\"91\"],\r\n//    \"national_prefix_formatting_rule\": \"0$1\",\r\n//    \"format\": \"$2 15-$3-$4\",\r\n//    \"international_format\": \"$1 $2 $3-$4\"\r\n// }\r\n//\r\n// In the format above, the `format` adds `15` to the digits when formatting a number.\r\n// A sidenote: this format actually is suitable because `national_prefix_for_parsing`\r\n// has previously removed `15` from a national number, so re-adding `15` in `format`\r\n// doesn't actually result in any extra digits added to user's input.\r\n// But verifying that would be a complex procedure, so the code chooses a simpler path:\r\n// it simply filters out all `format`s that contain anything but \"captured group\" ids.\r\n//\r\n// This regular expression is called `ELIGIBLE_FORMAT_PATTERN` in Google's\r\n// `libphonenumber` code.\r\n//\r\nconst NON_ALTERING_FORMAT_REG_EXP = new RegExp(\r\n\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t// Google developers say:\r\n\t// \"We require that the first matching group is present in the\r\n\t//  output pattern to ensure no data is lost while formatting.\"\r\n\t'\\\\$1' +\r\n\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t'(\\\\$\\\\d[' + VALID_PUNCTUATION + ']*)*' +\r\n\t'$'\r\n)\r\n\r\n// This is the minimum length of the leading digits of a phone number\r\n// to guarantee the first \"leading digits pattern\" for a phone number format\r\n// to be preemptive.\r\nconst MIN_LEADING_DIGITS_LENGTH = 3\r\n\r\nexport default class AsYouTypeFormatter {\r\n\tconstructor({\r\n\t\tstate,\r\n\t\tmetadata\r\n\t}) {\r\n\t\tthis.metadata = metadata\r\n\t\tthis.resetFormat()\r\n\t}\r\n\r\n\tresetFormat() {\r\n\t\tthis.chosenFormat = undefined\r\n\t\tthis.template = undefined\r\n\t\tthis.nationalNumberTemplate = undefined\r\n\t\tthis.populatedNationalNumberTemplate = undefined\r\n\t\tthis.populatedNationalNumberTemplatePosition = -1\r\n\t}\r\n\r\n\treset(numberingPlan, state) {\r\n\t\tthis.resetFormat()\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.isNANP = numberingPlan.callingCode() === '1'\r\n\t\t\tthis.matchingFormats = numberingPlan.formats()\r\n\t\t\tif (state.nationalSignificantNumber) {\r\n\t\t\t\tthis.narrowDownMatchingFormats(state)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.isNANP = undefined\r\n\t\t\tthis.matchingFormats = []\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Formats an updated phone number.\r\n\t * @param  {string} nextDigits — Additional phone number digits.\r\n\t * @param  {object} state — `AsYouType` state.\r\n\t * @return {[string]} Returns undefined if the updated phone number can't be formatted using any of the available formats.\r\n\t */\r\n\tformat(nextDigits, state) {\r\n\t\t// See if the phone number digits can be formatted as a complete phone number.\r\n\t\t// If not, use the results from `formatNationalNumberWithNextDigits()`,\r\n\t\t// which formats based on the chosen formatting pattern.\r\n\t\t//\r\n\t\t// Attempting to format complete phone number first is how it's done\r\n\t\t// in Google's `libphonenumber`, so this library just follows it.\r\n\t\t// Google's `libphonenumber` code doesn't explain in detail why does it\r\n\t\t// attempt to format digits as a complete phone number\r\n\t\t// instead of just going with a previoulsy (or newly) chosen `format`:\r\n\t\t//\r\n\t\t// \"Checks to see if there is an exact pattern match for these digits.\r\n\t\t//  If so, we should use this instead of any other formatting template\r\n\t\t//  whose leadingDigitsPattern also matches the input.\"\r\n\t\t//\r\n\t\tif (canFormatCompleteNumber(state.nationalSignificantNumber, this.metadata)) {\r\n\t\t\tfor (const format of this.matchingFormats) {\r\n\t\t\t\tconst formattedCompleteNumber = formatCompleteNumber(\r\n\t\t\t\t\tstate,\r\n\t\t\t\t\tformat,\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmetadata: this.metadata,\r\n\t\t\t\t\t\tshouldTryNationalPrefixFormattingRule: (format) => this.shouldTryNationalPrefixFormattingRule(format, {\r\n\t\t\t\t\t\t\tinternational: state.international,\r\n\t\t\t\t\t\t\tnationalPrefix: state.nationalPrefix\r\n\t\t\t\t\t\t}),\r\n\t\t\t\t\t\tgetSeparatorAfterNationalPrefix: (format) => this.getSeparatorAfterNationalPrefix(format)\r\n\t\t\t\t\t}\r\n\t\t\t\t)\r\n\t\t\t\tif (formattedCompleteNumber) {\r\n\t\t\t\t\tthis.resetFormat()\r\n\t\t\t\t\tthis.chosenFormat = format\r\n\t\t\t\t\tthis.setNationalNumberTemplate(formattedCompleteNumber.replace(/\\d/g, DIGIT_PLACEHOLDER), state)\r\n\t\t\t\t\tthis.populatedNationalNumberTemplate = formattedCompleteNumber\r\n\t\t\t\t\t// With a new formatting template, the matched position\r\n\t\t\t\t\t// using the old template needs to be reset.\r\n\t\t\t\t\tthis.populatedNationalNumberTemplatePosition = this.template.lastIndexOf(DIGIT_PLACEHOLDER)\r\n\t\t\t\t\treturn formattedCompleteNumber\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Format the digits as a partial (incomplete) phone number\r\n\t\t// using the previously chosen formatting pattern (or a newly chosen one).\r\n\t\treturn this.formatNationalNumberWithNextDigits(nextDigits, state)\r\n\t}\r\n\r\n\t// Formats the next phone number digits.\r\n\tformatNationalNumberWithNextDigits(nextDigits, state) {\r\n\t\tconst previouslyChosenFormat = this.chosenFormat\r\n\r\n\t\t// Choose a format from the list of matching ones.\r\n\t\tconst newlyChosenFormat = this.chooseFormat(state)\r\n\r\n\t\tif (newlyChosenFormat) {\r\n\t\t\tif (newlyChosenFormat === previouslyChosenFormat) {\r\n\t\t\t\t// If it can format the next (current) digits\r\n\t\t\t\t// using the previously chosen phone number format\r\n\t\t\t\t// then return the updated formatted number.\r\n\t\t\t\treturn this.formatNextNationalNumberDigits(nextDigits)\r\n\t\t\t} else {\r\n\t\t\t\t// If a more appropriate phone number format\r\n\t\t\t\t// has been chosen for these \"leading digits\",\r\n\t\t\t\t// then re-format the national phone number part\r\n\t\t\t\t// using the newly selected format.\r\n\t\t\t\treturn this.formatNextNationalNumberDigits(state.getNationalDigits())\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tnarrowDownMatchingFormats({\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalPrefix,\r\n\t\tinternational\r\n\t}) {\r\n\t\tconst leadingDigits = nationalSignificantNumber\r\n\r\n\t\t// \"leading digits\" pattern list starts with a\r\n\t\t// \"leading digits\" pattern fitting a maximum of 3 leading digits.\r\n\t\t// So, after a user inputs 3 digits of a national (significant) phone number\r\n\t\t// this national (significant) number can already be formatted.\r\n\t\t// The next \"leading digits\" pattern is for 4 leading digits max,\r\n\t\t// and the \"leading digits\" pattern after it is for 5 leading digits max, etc.\r\n\r\n\t\t// This implementation is different from Google's\r\n\t\t// in that it searches for a fitting format\r\n\t\t// even if the user has entered less than\r\n\t\t// `MIN_LEADING_DIGITS_LENGTH` digits of a national number.\r\n\t\t// Because some leading digit patterns already match for a single first digit.\r\n\t\tlet leadingDigitsPatternIndex = leadingDigits.length - MIN_LEADING_DIGITS_LENGTH\r\n\t\tif (leadingDigitsPatternIndex < 0) {\r\n\t\t\tleadingDigitsPatternIndex = 0\r\n\t\t}\r\n\r\n\t\tthis.matchingFormats = this.matchingFormats.filter(\r\n\t\t\tformat => this.formatSuits(format, international, nationalPrefix)\r\n\t\t\t\t&& this.formatMatches(format, leadingDigits, leadingDigitsPatternIndex)\r\n\t\t)\r\n\r\n\t\t// If there was a phone number format chosen\r\n\t\t// and it no longer holds given the new leading digits then reset it.\r\n\t\t// The test for this `if` condition is marked as:\r\n\t\t// \"Reset a chosen format when it no longer holds given the new leading digits\".\r\n\t\t// To construct a valid test case for this one can find a country\r\n\t\t// in `PhoneNumberMetadata.xml` yielding one format for 3 `<leadingDigits>`\r\n\t\t// and yielding another format for 4 `<leadingDigits>` (Australia in this case).\r\n\t\tif (this.chosenFormat && this.matchingFormats.indexOf(this.chosenFormat) === -1) {\r\n\t\t\tthis.resetFormat()\r\n\t\t}\r\n\t}\r\n\r\n\tformatSuits(format, international, nationalPrefix) {\r\n\t\t// When a prefix before a national (significant) number is\r\n\t\t// simply a national prefix, then it's parsed as `this.nationalPrefix`.\r\n\t\t// In more complex cases, a prefix before national (significant) number\r\n\t\t// could include a national prefix as well as some \"capturing groups\",\r\n\t\t// and in that case there's no info whether a national prefix has been parsed.\r\n\t\t// If national prefix is not used when formatting a phone number\r\n\t\t// using this format, but a national prefix has been entered by the user,\r\n\t\t// and was extracted, then discard such phone number format.\r\n\t\t// In Google's \"AsYouType\" formatter code, the equivalent would be this part:\r\n\t\t// https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L175-L184\r\n\t\tif (nationalPrefix &&\r\n\t\t\t!format.usesNationalPrefix() &&\r\n\t\t\t// !format.domesticCarrierCodeFormattingRule() &&\r\n\t\t\t!format.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\t// If national prefix is mandatory for this phone number format\r\n\t\t// and there're no guarantees that a national prefix is present in user input\r\n\t\t// then discard this phone number format as not suitable.\r\n\t\t// In Google's \"AsYouType\" formatter code, the equivalent would be this part:\r\n\t\t// https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L185-L193\r\n\t\tif (!international &&\r\n\t\t\t!nationalPrefix &&\r\n\t\t\tformat.nationalPrefixIsMandatoryWhenFormattingInNationalFormat()) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn true\r\n\t}\r\n\r\n\tformatMatches(format, leadingDigits, leadingDigitsPatternIndex) {\r\n\t\tconst leadingDigitsPatternsCount = format.leadingDigitsPatterns().length\r\n\r\n\t\t// If this format is not restricted to a certain\r\n\t\t// leading digits pattern then it fits.\r\n\t\t// The test case could be found by searching for \"leadingDigitsPatternsCount === 0\".\r\n\t\tif (leadingDigitsPatternsCount === 0) {\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\t// Start narrowing down the list of possible formats based on the leading digits.\r\n\t\t// (only previously matched formats take part in the narrowing down process)\r\n\r\n\t\t// `leading_digits_patterns` start with 3 digits min\r\n\t\t// and then go up from there one digit at a time.\r\n\t\tleadingDigitsPatternIndex = Math.min(leadingDigitsPatternIndex, leadingDigitsPatternsCount - 1)\r\n\t\tconst leadingDigitsPattern = format.leadingDigitsPatterns()[leadingDigitsPatternIndex]\r\n\r\n\t\t// Google imposes a requirement on the leading digits\r\n\t\t// to be minimum 3 digits long in order to be eligible\r\n\t\t// for checking those with a leading digits pattern.\r\n\t\t//\r\n\t\t// Since `leading_digits_patterns` start with 3 digits min,\r\n\t\t// Google's original `libphonenumber` library only starts\r\n\t\t// excluding any non-matching formats only when the\r\n\t\t// national number entered so far is at least 3 digits long,\r\n\t\t// otherwise format matching would give false negatives.\r\n\t\t//\r\n\t\t// For example, when the digits entered so far are `2`\r\n\t\t// and the leading digits pattern is `21` –\r\n\t\t// it's quite obvious in this case that the format could be the one\r\n\t\t// but due to the absence of further digits it would give false negative.\r\n\t\t//\r\n\t\t// Also, `leading_digits_patterns` doesn't always correspond to a single\r\n\t\t// digits count. For example, `60|8` pattern would already match `8`\r\n\t\t// but the `60` part would require having at least two leading digits,\r\n\t\t// so the whole pattern would require inputting two digits first in order to\r\n\t\t// decide on whether it matches the input, even when the input is \"80\".\r\n\t\t//\r\n\t\t// This library — `libphonenumber-js` — allows filtering by `leading_digits_patterns`\r\n\t\t// even when there's only 1 or 2 digits of the national (significant) number.\r\n\t\t// To do that, it uses a non-strict pattern matcher written specifically for that.\r\n\t\t//\r\n\t\tif (leadingDigits.length < MIN_LEADING_DIGITS_LENGTH) {\r\n\t\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t\t// return true\r\n\t\t\t//\r\n\t\t\t// After leading digits < 3 matching was implemented:\r\n\t\t\ttry {\r\n\t\t\t\treturn new PatternMatcher(leadingDigitsPattern).match(leadingDigits, { allowOverflow: true }) !== undefined\r\n\t\t\t} catch (error) /* istanbul ignore next */ {\r\n\t\t\t\t// There's a slight possibility that there could be some undiscovered bug\r\n\t\t\t\t// in the pattern matcher code. Since the \"leading digits < 3 matching\"\r\n\t\t\t\t// feature is not \"essential\" for operation, it can fall back to the old way\r\n\t\t\t\t// in case of any issues rather than halting the application's execution.\r\n\t\t\t\tconsole.error(error)\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// If at least `MIN_LEADING_DIGITS_LENGTH` digits of a national number are\r\n\t\t// available then use the usual regular expression matching.\r\n\t\t//\r\n\t\t// The whole pattern is wrapped in round brackets (`()`) because\r\n\t\t// the pattern can use \"or\" operator (`|`) at the top level of the pattern.\r\n\t\t//\r\n\t\treturn new RegExp(`^(${leadingDigitsPattern})`).test(leadingDigits)\r\n\t}\r\n\r\n\tgetFormatFormat(format, international) {\r\n\t\treturn international ? format.internationalFormat() : format.format()\r\n\t}\r\n\r\n\tchooseFormat(state) {\r\n\t\t// When there are multiple available formats, the formatter uses the first\r\n\t\t// format where a formatting template could be created.\r\n\t\t//\r\n\t\t// For some weird reason, `istanbul` says \"else path not taken\"\r\n\t\t// for the `for of` line below. Supposedly that means that\r\n\t\t// the loop doesn't ever go over the last element in the list.\r\n\t\t// That's true because there always is `this.chosenFormat`\r\n\t\t// when `this.matchingFormats` is non-empty.\r\n\t\t// And, for some weird reason, it doesn't think that the case\r\n\t\t// with empty `this.matchingFormats` qualifies for a valid \"else\" path.\r\n\t\t// So simply muting this `istanbul` warning.\r\n\t\t// It doesn't skip the contents of the `for of` loop,\r\n\t\t// it just skips the `for of` line.\r\n\t\t//\r\n\t\t/* istanbul ignore next */\r\n\t\tfor (const format of this.matchingFormats.slice()) {\r\n\t\t\t// If this format is currently being used\r\n\t\t\t// and is still suitable, then stick to it.\r\n\t\t\tif (this.chosenFormat === format) {\r\n\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t\t// Sometimes, a formatting rule inserts additional digits in a phone number,\r\n\t\t\t// and \"as you type\" formatter can't do that: it should only use the digits\r\n\t\t\t// that the user has input.\r\n\t\t\t//\r\n\t\t\t// For example, in Argentina, there's a format for mobile phone numbers:\r\n\t\t\t//\r\n\t\t\t// {\r\n\t\t\t//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n\t\t\t//    \"leading_digits_patterns\": [\"91\"],\r\n\t\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t\t//    \"format\": \"$2 15-$3-$4\",\r\n\t\t\t//    \"international_format\": \"$1 $2 $3-$4\"\r\n\t\t\t// }\r\n\t\t\t//\r\n\t\t\t// In that format, `international_format` is used instead of `format`\r\n\t\t\t// because `format` inserts `15` in the formatted number,\r\n\t\t\t// and `AsYouType` formatter should only use the digits\r\n\t\t\t// the user has actually input, without adding any extra digits.\r\n\t\t\t// In this case, it wouldn't make a difference, because the `15`\r\n\t\t\t// is first stripped when applying `national_prefix_for_parsing`\r\n\t\t\t// and then re-added when using `format`, so in reality it doesn't\r\n\t\t\t// add any new digits to the number, but to detect that, the code\r\n\t\t\t// would have to be more complex: it would have to try formatting\r\n\t\t\t// the digits using the format and then see if any digits have\r\n\t\t\t// actually been added or removed, and then, every time a new digit\r\n\t\t\t// is input, it should re-check whether the chosen format doesn't\r\n\t\t\t// alter the digits.\r\n\t\t\t//\r\n\t\t\t// Google's code doesn't go that far, and so does this library:\r\n\t\t\t// it simply requires that a `format` doesn't add any additonal\r\n\t\t\t// digits to user's input.\r\n\t\t\t//\r\n\t\t\t// Also, people in general should move from inputting phone numbers\r\n\t\t\t// in national format (possibly with national prefixes)\r\n\t\t\t// and use international phone number format instead:\r\n\t\t\t// it's a logical thing in the modern age of mobile phones,\r\n\t\t\t// globalization and the internet.\r\n\t\t\t//\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (!NON_ALTERING_FORMAT_REG_EXP.test(this.getFormatFormat(format, state.international))) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t\tif (!this.createTemplateForFormat(format, state)) {\r\n\t\t\t\t// Remove the format if it can't generate a template.\r\n\t\t\t\tthis.matchingFormats = this.matchingFormats.filter(_ => _ !== format)\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t\tthis.chosenFormat = format\r\n\t\t\tbreak\r\n\t\t}\r\n\t\tif (!this.chosenFormat) {\r\n\t\t\t// No format matches the national (significant) phone number.\r\n\t\t\tthis.resetFormat()\r\n\t\t}\r\n\t\treturn this.chosenFormat\r\n\t}\r\n\r\n\tcreateTemplateForFormat(format, state) {\r\n\t\t// The formatter doesn't format numbers when numberPattern contains '|', e.g.\r\n\t\t// (20|3)\\d{4}. In those cases we quickly return.\r\n\t\t// (Though there's no such format in current metadata)\r\n\t\t/* istanbul ignore if */\r\n\t\tif (SUPPORT_LEGACY_FORMATTING_PATTERNS && format.pattern().indexOf('|') >= 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Get formatting template for this phone number format\r\n\t\tconst template = this.getTemplateForFormat(format, state)\r\n\t\t// If the national number entered is too long\r\n\t\t// for any phone number format, then abort.\r\n\t\tif (template) {\r\n\t\t\tthis.setNationalNumberTemplate(template, state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tgetSeparatorAfterNationalPrefix(format) {\r\n\t\t// `US` metadata doesn't have a `national_prefix_formatting_rule`,\r\n\t\t// so the `if` condition below doesn't apply to `US`,\r\n\t\t// but in reality there shoudl be a separator\r\n\t\t// between a national prefix and a national (significant) number.\r\n\t\t// So `US` national prefix separator is a \"special\" \"hardcoded\" case.\r\n\t\tif (this.isNANP) {\r\n\t\t\treturn ' '\r\n\t\t}\r\n\t\t// If a `format` has a `national_prefix_formatting_rule`\r\n\t\t// and that rule has a separator after a national prefix,\r\n\t\t// then it means that there should be a separator\r\n\t\t// between a national prefix and a national (significant) number.\r\n\t\tif (format &&\r\n\t\t\tformat.nationalPrefixFormattingRule() &&\r\n\t\t\tNATIONAL_PREFIX_SEPARATORS_PATTERN.test(format.nationalPrefixFormattingRule())) {\r\n\t\t\treturn ' '\r\n\t\t}\r\n\t\t// At this point, there seems to be no clear evidence that\r\n\t\t// there should be a separator between a national prefix\r\n\t\t// and a national (significant) number. So don't insert one.\r\n\t\treturn ''\r\n\t}\r\n\r\n\tgetInternationalPrefixBeforeCountryCallingCode({ IDDPrefix, missingPlus }, options) {\r\n\t\tif (IDDPrefix) {\r\n\t\t\treturn options && options.spacing === false ? IDDPrefix : IDDPrefix + ' '\r\n\t\t}\r\n\t\tif (missingPlus) {\r\n\t\t\treturn ''\r\n\t\t}\r\n\t\treturn '+'\r\n\t}\r\n\r\n\tgetTemplate(state) {\r\n\t\tif (!this.template) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// `this.template` holds the template for a \"complete\" phone number.\r\n\t\t// The currently entered phone number is most likely not \"complete\",\r\n\t\t// so trim all non-populated digits.\r\n\t\tlet index = -1\r\n\t\tlet i = 0\r\n\t\tconst internationalPrefix = state.international ? this.getInternationalPrefixBeforeCountryCallingCode(state, { spacing: false }) : ''\r\n\t\twhile (i < internationalPrefix.length + state.getDigitsWithoutInternationalPrefix().length) {\r\n\t\t\tindex = this.template.indexOf(DIGIT_PLACEHOLDER, index + 1)\r\n\t\t\ti++\r\n\t\t}\r\n\t\treturn cutAndStripNonPairedParens(this.template, index + 1)\r\n\t}\r\n\r\n\tsetNationalNumberTemplate(template, state) {\r\n\t\tthis.nationalNumberTemplate = template\r\n\t\tthis.populatedNationalNumberTemplate = template\r\n\t\t// With a new formatting template, the matched position\r\n\t\t// using the old template needs to be reset.\r\n\t\tthis.populatedNationalNumberTemplatePosition = -1\r\n\t\t// For convenience, the public `.template` property\r\n\t\t// contains the whole international number\r\n\t\t// if the phone number being input is international:\r\n\t\t// 'x' for the '+' sign, 'x'es for the country phone code,\r\n\t\t// a spacebar and then the template for the formatted national number.\r\n\t\tif (state.international) {\r\n\t\t\tthis.template =\r\n\t\t\t\tthis.getInternationalPrefixBeforeCountryCallingCode(state).replace(/[\\d\\+]/g, DIGIT_PLACEHOLDER) +\r\n\t\t\t\trepeat(DIGIT_PLACEHOLDER, state.callingCode.length) +\r\n\t\t\t\t' ' +\r\n\t\t\t\ttemplate\r\n\t\t} else {\r\n\t\t\tthis.template = template\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Generates formatting template for a national phone number,\r\n\t * optionally containing a national prefix, for a format.\r\n\t * @param  {Format} format\r\n\t * @param  {string} nationalPrefix\r\n\t * @return {string}\r\n\t */\r\n\tgetTemplateForFormat(format, {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t}) {\r\n\t\tlet pattern = format.pattern()\r\n\r\n\t\t/* istanbul ignore else */\r\n\t\tif (SUPPORT_LEGACY_FORMATTING_PATTERNS) {\r\n\t\t\tpattern = pattern\r\n\t\t\t\t// Replace anything in the form of [..] with \\d\r\n\t\t\t\t.replace(CREATE_CHARACTER_CLASS_PATTERN(), '\\\\d')\r\n\t\t\t\t// Replace any standalone digit (not the one in `{}`) with \\d\r\n\t\t\t\t.replace(CREATE_STANDALONE_DIGIT_PATTERN(), '\\\\d')\r\n\t\t}\r\n\r\n\t\t// Generate a dummy national number (consisting of `9`s)\r\n\t\t// that fits this format's `pattern`.\r\n\t\t//\r\n\t\t// This match will always succeed,\r\n\t\t// because the \"longest dummy phone number\"\r\n\t\t// has enough length to accomodate any possible\r\n\t\t// national phone number format pattern.\r\n\t\t//\r\n\t\tlet digits = LONGEST_DUMMY_PHONE_NUMBER.match(pattern)[0]\r\n\r\n\t\t// If the national number entered is too long\r\n\t\t// for any phone number format, then abort.\r\n\t\tif (nationalSignificantNumber.length > digits.length) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// Get a formatting template which can be used to efficiently format\r\n\t\t// a partial number where digits are added one by one.\r\n\r\n\t\t// Below `strictPattern` is used for the\r\n\t\t// regular expression (with `^` and `$`).\r\n\t\t// This wasn't originally in Google's `libphonenumber`\r\n\t\t// and I guess they don't really need it\r\n\t\t// because they're not using \"templates\" to format phone numbers\r\n\t\t// but I added `strictPattern` after encountering\r\n\t\t// South Korean phone number formatting bug.\r\n\t\t//\r\n\t\t// Non-strict regular expression bug demonstration:\r\n\t\t//\r\n\t\t// this.nationalSignificantNumber : `111111111` (9 digits)\r\n\t\t//\r\n\t\t// pattern : (\\d{2})(\\d{3,4})(\\d{4})\r\n\t\t// format : `$1 $2 $3`\r\n\t\t// digits : `9999999999` (10 digits)\r\n\t\t//\r\n\t\t// '9999999999'.replace(new RegExp(/(\\d{2})(\\d{3,4})(\\d{4})/g), '$1 $2 $3') = \"99 9999 9999\"\r\n\t\t//\r\n\t\t// template : xx xxxx xxxx\r\n\t\t//\r\n\t\t// But the correct template in this case is `xx xxx xxxx`.\r\n\t\t// The template was generated incorrectly because of the\r\n\t\t// `{3,4}` variability in the `pattern`.\r\n\t\t//\r\n\t\t// The fix is, if `this.nationalSignificantNumber` has already sufficient length\r\n\t\t// to satisfy the `pattern` completely then `this.nationalSignificantNumber`\r\n\t\t// is used instead of `digits`.\r\n\r\n\t\tconst strictPattern = new RegExp('^' + pattern + '$')\r\n\t\tconst nationalNumberDummyDigits = nationalSignificantNumber.replace(/\\d/g, DUMMY_DIGIT)\r\n\r\n\t\t// If `this.nationalSignificantNumber` has already sufficient length\r\n\t\t// to satisfy the `pattern` completely then use it\r\n\t\t// instead of `digits`.\r\n\t\tif (strictPattern.test(nationalNumberDummyDigits)) {\r\n\t\t\tdigits = nationalNumberDummyDigits\r\n\t\t}\r\n\r\n\t\tlet numberFormat = this.getFormatFormat(format, international)\r\n\t\tlet nationalPrefixIncludedInTemplate\r\n\r\n\t\t// If a user did input a national prefix (and that's guaranteed),\r\n\t\t// and if a `format` does have a national prefix formatting rule,\r\n\t\t// then see if that national prefix formatting rule\r\n\t\t// prepends exactly the same national prefix the user has input.\r\n\t\t// If that's the case, then use the `format` with the national prefix formatting rule.\r\n\t\t// Otherwise, use  the `format` without the national prefix formatting rule,\r\n\t\t// and prepend a national prefix manually to it.\r\n\t\tif (this.shouldTryNationalPrefixFormattingRule(format, { international, nationalPrefix })) {\r\n\t\t\tconst numberFormatWithNationalPrefix = numberFormat.replace(\r\n\t\t\t\tFIRST_GROUP_PATTERN,\r\n\t\t\t\tformat.nationalPrefixFormattingRule()\r\n\t\t\t)\r\n\t\t\t// If `national_prefix_formatting_rule` of a `format` simply prepends\r\n\t\t\t// national prefix at the start of a national (significant) number,\r\n\t\t\t// then such formatting can be used with `AsYouType` formatter.\r\n\t\t\t// There seems to be no `else` case: everywhere in metadata,\r\n\t\t\t// national prefix formatting rule is national prefix + $1,\r\n\t\t\t// or `($1)`, in which case such format isn't even considered\r\n\t\t\t// when the user has input a national prefix.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (parseDigits(format.nationalPrefixFormattingRule()) === (nationalPrefix || '') + parseDigits('$1')) {\r\n\t\t\t\tnumberFormat = numberFormatWithNationalPrefix\r\n\t\t\t\tnationalPrefixIncludedInTemplate = true\r\n\t\t\t\t// Replace all digits of the national prefix in the formatting template\r\n\t\t\t\t// with `DIGIT_PLACEHOLDER`s.\r\n\t\t\t\tif (nationalPrefix) {\r\n\t\t\t\t\tlet i = nationalPrefix.length\r\n\t\t\t\t\twhile (i > 0) {\r\n\t\t\t\t\t\tnumberFormat = numberFormat.replace(/\\d/, DIGIT_PLACEHOLDER)\r\n\t\t\t\t\t\ti--\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Generate formatting template for this phone number format.\r\n\t\tlet template = digits\r\n\t\t\t// Format the dummy phone number according to the format.\r\n\t\t\t.replace(new RegExp(pattern), numberFormat)\r\n\t\t\t// Replace each dummy digit with a DIGIT_PLACEHOLDER.\r\n\t\t\t.replace(new RegExp(DUMMY_DIGIT, 'g'), DIGIT_PLACEHOLDER)\r\n\r\n\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t// as just a basic national prefix, then just prepend such prefix\r\n\t\t// before the national (significant) number, optionally spacing\r\n\t\t// the two with a whitespace.\r\n\t\tif (!nationalPrefixIncludedInTemplate) {\r\n\t\t\tif (complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\t\t// Prepend the prefix to the template manually.\r\n\t\t\t\ttemplate = repeat(DIGIT_PLACEHOLDER, complexPrefixBeforeNationalSignificantNumber.length) +\r\n\t\t\t\t\t' ' +\r\n\t\t\t\t\ttemplate\r\n\t\t\t} else if (nationalPrefix) {\r\n\t\t\t\t// Prepend national prefix to the template manually.\r\n\t\t\t\ttemplate = repeat(DIGIT_PLACEHOLDER, nationalPrefix.length) +\r\n\t\t\t\t\tthis.getSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\t\ttemplate\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (international) {\r\n\t\t\ttemplate = applyInternationalSeparatorStyle(template)\r\n\t\t}\r\n\r\n\t\treturn template\r\n\t}\r\n\r\n\tformatNextNationalNumberDigits(digits) {\r\n\t\tconst result = populateTemplateWithDigits(\r\n\t\t\tthis.populatedNationalNumberTemplate,\r\n\t\t\tthis.populatedNationalNumberTemplatePosition,\r\n\t\t\tdigits\r\n\t\t)\r\n\r\n\t\tif (!result) {\r\n\t\t\t// Reset the format.\r\n\t\t\tthis.resetFormat()\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tthis.populatedNationalNumberTemplate = result[0]\r\n\t\tthis.populatedNationalNumberTemplatePosition = result[1]\r\n\r\n\t\t// Return the formatted phone number so far.\r\n\t\treturn cutAndStripNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\r\n\r\n\t\t// The old way which was good for `input-format` but is not so good\r\n\t\t// for `react-phone-number-input`'s default input (`InputBasic`).\r\n\t\t// return closeNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\r\n\t\t// \t.replace(new RegExp(DIGIT_PLACEHOLDER, 'g'), ' ')\r\n\t}\r\n\r\n\tshouldTryNationalPrefixFormattingRule(format, { international, nationalPrefix }) {\r\n\t\tif (format.nationalPrefixFormattingRule()) {\r\n\t\t\t// In some countries, `national_prefix_formatting_rule` is `($1)`,\r\n\t\t\t// so it applies even if the user hasn't input a national prefix.\r\n\t\t\t// `format.usesNationalPrefix()` detects such cases.\r\n\t\t\tconst usesNationalPrefix = format.usesNationalPrefix()\r\n\t\t\tif ((usesNationalPrefix && nationalPrefix) ||\r\n\t\t\t\t(!usesNationalPrefix && !international)) {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;AAAA,SACCA,iBADD,EAECC,eAFD,EAGCC,MAHD,EAICC,0BAJD,EAKCC,oBALD,EAMCC,oBAND,EAOCC,0BAPD,QAQO,8BARP;AAUA,OAAOC,oBAAP,IACCC,uBADD,QAEO,kCAFP;AAIA,OAAOC,cAAP,MAA2B,wCAA3B;AAEA,OAAOC,WAAP,MAAwB,0BAAxB;AACA,SAASV,iBAAT,QAAkC,8BAAlC;AACA,SAASW,mBAAT,QAAoC,8CAApC;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,OAAOC,gCAAP,MAA6C,+CAA7C,C,CAEA;AACA;;AACA,IAAMC,WAAW,GAAG,GAApB,C,CACA;;AACA,IAAMC,oCAAoC,GAAG,EAA7C,C,CACA;AACA;;AACA,IAAMC,0BAA0B,GAAGd,MAAM,CAACY,WAAD,EAAcC,oCAAd,CAAzC,C,CAEA;AACA;;AACA,IAAME,kCAAkC,GAAG,MAA3C,C,CAEA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMC,kCAAkC,GAAG,IAA3C,C,CAEA;AACA;;AACA,IAAMC,8BAA8B,GAAGD,kCAAkC,IAAK;EAAA,OAAM,iBAAN;AAAA,CAA9E,C,CAEA;AACA;AACA;AACA;AACA;;;AACA,IAAME,+BAA+B,GAAGF,kCAAkC,IAAK;EAAA,OAAM,mBAAN;AAAA,CAA/E,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMG,2BAA2B,GAAG,IAAIC,MAAJ,CACnC,MAAMV,iBAAN,GAA0B,IAA1B,GACA;AACA;AACA;AACA,MAJA,GAKA,GALA,GAKMA,iBALN,GAK0B,IAL1B,GAMA,UANA,GAMaA,iBANb,GAMiC,MANjC,GAOA,GARmC,CAApC,C,CAWA;AACA;AACA;;AACA,IAAMW,yBAAyB,GAAG,CAAlC;;IAEqBC,kB;EACpB,kCAGG;IAAA,IAFFC,KAEE,QAFFA,KAEE;IAAA,IADFC,QACE,QADFA,QACE;;IAAA;;IACF,KAAKA,QAAL,GAAgBA,QAAhB;IACA,KAAKC,WAAL;EACA;;;;WAED,uBAAc;MACb,KAAKC,YAAL,GAAoBC,SAApB;MACA,KAAKC,QAAL,GAAgBD,SAAhB;MACA,KAAKE,sBAAL,GAA8BF,SAA9B;MACA,KAAKG,+BAAL,GAAuCH,SAAvC;MACA,KAAKI,uCAAL,GAA+C,CAAC,CAAhD;IACA;;;WAED,eAAMC,aAAN,EAAqBT,KAArB,EAA4B;MAC3B,KAAKE,WAAL;;MACA,IAAIO,aAAJ,EAAmB;QAClB,KAAKC,MAAL,GAAcD,aAAa,CAACE,WAAd,OAAgC,GAA9C;QACA,KAAKC,eAAL,GAAuBH,aAAa,CAACI,OAAd,EAAvB;;QACA,IAAIb,KAAK,CAACc,yBAAV,EAAqC;UACpC,KAAKC,yBAAL,CAA+Bf,KAA/B;QACA;MACD,CAND,MAMO;QACN,KAAKU,MAAL,GAAcN,SAAd;QACA,KAAKQ,eAAL,GAAuB,EAAvB;MACA;IACD;IAED;AACD;AACA;AACA;AACA;AACA;;;;WACC,gBAAOI,UAAP,EAAmBhB,KAAnB,EAA0B;MAAA;;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIjB,uBAAuB,CAACiB,KAAK,CAACc,yBAAP,EAAkC,KAAKb,QAAvC,CAA3B,EAA6E;QAC5E,qDAAqB,KAAKW,eAA1B,wCAA2C;UAAA,IAAhCK,MAAgC;UAC1C,IAAMC,uBAAuB,GAAGpC,oBAAoB,CACnDkB,KADmD,EAEnDiB,MAFmD,EAGnD;YACChB,QAAQ,EAAE,KAAKA,QADhB;YAECkB,qCAAqC,EAAE,+CAACF,MAAD;cAAA,OAAY,KAAI,CAACE,qCAAL,CAA2CF,MAA3C,EAAmD;gBACrGG,aAAa,EAAEpB,KAAK,CAACoB,aADgF;gBAErGC,cAAc,EAAErB,KAAK,CAACqB;cAF+E,CAAnD,CAAZ;YAAA,CAFxC;YAMCC,+BAA+B,EAAE,yCAACL,MAAD;cAAA,OAAY,KAAI,CAACK,+BAAL,CAAqCL,MAArC,CAAZ;YAAA;UANlC,CAHmD,CAApD;;UAYA,IAAIC,uBAAJ,EAA6B;YAC5B,KAAKhB,WAAL;YACA,KAAKC,YAAL,GAAoBc,MAApB;YACA,KAAKM,yBAAL,CAA+BL,uBAAuB,CAACM,OAAxB,CAAgC,KAAhC,EAAuCjD,iBAAvC,CAA/B,EAA0FyB,KAA1F;YACA,KAAKO,+BAAL,GAAuCW,uBAAvC,CAJ4B,CAK5B;YACA;;YACA,KAAKV,uCAAL,GAA+C,KAAKH,QAAL,CAAcoB,WAAd,CAA0BlD,iBAA1B,CAA/C;YACA,OAAO2C,uBAAP;UACA;QAED;MACD,CAzCwB,CA0CzB;MACA;;;MACA,OAAO,KAAKQ,kCAAL,CAAwCV,UAAxC,EAAoDhB,KAApD,CAAP;IACA,C,CAED;;;;WACA,4CAAmCgB,UAAnC,EAA+ChB,KAA/C,EAAsD;MACrD,IAAM2B,sBAAsB,GAAG,KAAKxB,YAApC,CADqD,CAGrD;;MACA,IAAMyB,iBAAiB,GAAG,KAAKC,YAAL,CAAkB7B,KAAlB,CAA1B;;MAEA,IAAI4B,iBAAJ,EAAuB;QACtB,IAAIA,iBAAiB,KAAKD,sBAA1B,EAAkD;UACjD;UACA;UACA;UACA,OAAO,KAAKG,8BAAL,CAAoCd,UAApC,CAAP;QACA,CALD,MAKO;UACN;UACA;UACA;UACA;UACA,OAAO,KAAKc,8BAAL,CAAoC9B,KAAK,CAAC+B,iBAAN,EAApC,CAAP;QACA;MACD;IACD;;;WAED,0CAIG;MAAA;;MAAA,IAHFjB,yBAGE,SAHFA,yBAGE;MAAA,IAFFO,cAEE,SAFFA,cAEE;MAAA,IADFD,aACE,SADFA,aACE;MACF,IAAMY,aAAa,GAAGlB,yBAAtB,CADE,CAGF;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;;MACA,IAAImB,yBAAyB,GAAGD,aAAa,CAACE,MAAd,GAAuBpC,yBAAvD;;MACA,IAAImC,yBAAyB,GAAG,CAAhC,EAAmC;QAClCA,yBAAyB,GAAG,CAA5B;MACA;;MAED,KAAKrB,eAAL,GAAuB,KAAKA,eAAL,CAAqBuB,MAArB,CACtB,UAAAlB,MAAM;QAAA,OAAI,MAAI,CAACmB,WAAL,CAAiBnB,MAAjB,EAAyBG,aAAzB,EAAwCC,cAAxC,KACN,MAAI,CAACgB,aAAL,CAAmBpB,MAAnB,EAA2Be,aAA3B,EAA0CC,yBAA1C,CADE;MAAA,CADgB,CAAvB,CApBE,CAyBF;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAK9B,YAAL,IAAqB,KAAKS,eAAL,CAAqB0B,OAArB,CAA6B,KAAKnC,YAAlC,MAAoD,CAAC,CAA9E,EAAiF;QAChF,KAAKD,WAAL;MACA;IACD;;;WAED,qBAAYe,MAAZ,EAAoBG,aAApB,EAAmCC,cAAnC,EAAmD;MAClD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,cAAc,IACjB,CAACJ,MAAM,CAACsB,kBAAP,EADE,IAEH;MACA,CAACtB,MAAM,CAACuB,sDAAP,EAHF,EAGmE;QAClE,OAAO,KAAP;MACA,CAhBiD,CAiBlD;MACA;MACA;MACA;MACA;;;MACA,IAAI,CAACpB,aAAD,IACH,CAACC,cADE,IAEHJ,MAAM,CAACwB,uDAAP,EAFD,EAEmE;QAClE,OAAO,KAAP;MACA;;MACD,OAAO,IAAP;IACA;;;WAED,uBAAcxB,MAAd,EAAsBe,aAAtB,EAAqCC,yBAArC,EAAgE;MAC/D,IAAMS,0BAA0B,GAAGzB,MAAM,CAAC0B,qBAAP,GAA+BT,MAAlE,CAD+D,CAG/D;MACA;MACA;;MACA,IAAIQ,0BAA0B,KAAK,CAAnC,EAAsC;QACrC,OAAO,IAAP;MACA,CAR8D,CAU/D;MACA;MAEA;MACA;;;MACAT,yBAAyB,GAAGW,IAAI,CAACC,GAAL,CAASZ,yBAAT,EAAoCS,0BAA0B,GAAG,CAAjE,CAA5B;MACA,IAAMI,oBAAoB,GAAG7B,MAAM,CAAC0B,qBAAP,GAA+BV,yBAA/B,CAA7B,CAhB+D,CAkB/D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAID,aAAa,CAACE,MAAd,GAAuBpC,yBAA3B,EAAsD;QACrD;QACA;QACA;QACA;QACA,IAAI;UACH,OAAO,IAAId,cAAJ,CAAmB8D,oBAAnB,EAAyCC,KAAzC,CAA+Cf,aAA/C,EAA8D;YAAEgB,aAAa,EAAE;UAAjB,CAA9D,MAA2F5C,SAAlG;QACA,CAFD,CAEE,OAAO6C,KAAP;QAAc;QAA2B;UAC1C;UACA;UACA;UACA;UACAC,OAAO,CAACD,KAAR,CAAcA,KAAd;UACA,OAAO,IAAP;QACA;MACD,CA1D8D,CA4D/D;MACA;MACA;MACA;MACA;MACA;;;MACA,OAAO,IAAIpD,MAAJ,aAAgBiD,oBAAhB,QAAyCK,IAAzC,CAA8CnB,aAA9C,CAAP;IACA;;;WAED,yBAAgBf,MAAhB,EAAwBG,aAAxB,EAAuC;MACtC,OAAOA,aAAa,GAAGH,MAAM,CAACmC,mBAAP,EAAH,GAAkCnC,MAAM,CAACA,MAAP,EAAtD;IACA;;;WAED,sBAAajB,KAAb,EAAoB;MAAA;;MAAA;QAAA,IAgBRiB,MAhBQ;;QAiBlB;QACA;QACA,IAAI,MAAI,CAACd,YAAL,KAAsBc,MAA1B,EAAkC;UACjC;QACA,CArBiB,CAsBlB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA;;;QACA,IAAI,CAACrB,2BAA2B,CAACuD,IAA5B,CAAiC,MAAI,CAACE,eAAL,CAAqBpC,MAArB,EAA6BjB,KAAK,CAACoB,aAAnC,CAAjC,CAAL,EAA0F;UACzF;QACA;;QACD,IAAI,CAAC,MAAI,CAACkC,uBAAL,CAA6BrC,MAA7B,EAAqCjB,KAArC,CAAL,EAAkD;UACjD;UACA,MAAI,CAACY,eAAL,GAAuB,MAAI,CAACA,eAAL,CAAqBuB,MAArB,CAA4B,UAAAoB,CAAC;YAAA,OAAIA,CAAC,KAAKtC,MAAV;UAAA,CAA7B,CAAvB;UACA;QACA;;QACD,MAAI,CAACd,YAAL,GAAoBc,MAApB;QACA;MAtEkB;;MACnB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA;MACA,sDAAqB,KAAKL,eAAL,CAAqB4C,KAArB,EAArB,2CAAmD;QAAA;;QAAA,sBAsDlD;QAtDkD,yBAmDjD;MAID;;MACD,IAAI,CAAC,KAAKrD,YAAV,EAAwB;QACvB;QACA,KAAKD,WAAL;MACA;;MACD,OAAO,KAAKC,YAAZ;IACA;;;WAED,iCAAwBc,MAAxB,EAAgCjB,KAAhC,EAAuC;MACtC;MACA;MACA;;MACA;MACA,IAAIP,kCAAkC,IAAIwB,MAAM,CAACwC,OAAP,GAAiBnB,OAAjB,CAAyB,GAAzB,KAAiC,CAA3E,EAA8E;QAC7E;MACA,CAPqC,CAQtC;;;MACA,IAAMjC,QAAQ,GAAG,KAAKqD,oBAAL,CAA0BzC,MAA1B,EAAkCjB,KAAlC,CAAjB,CATsC,CAUtC;MACA;;MACA,IAAIK,QAAJ,EAAc;QACb,KAAKkB,yBAAL,CAA+BlB,QAA/B,EAAyCL,KAAzC;QACA,OAAO,IAAP;MACA;IACD;;;WAED,yCAAgCiB,MAAhC,EAAwC;MACvC;MACA;MACA;MACA;MACA;MACA,IAAI,KAAKP,MAAT,EAAiB;QAChB,OAAO,GAAP;MACA,CARsC,CASvC;MACA;MACA;MACA;;;MACA,IAAIO,MAAM,IACTA,MAAM,CAAC0C,4BAAP,EADG,IAEHnE,kCAAkC,CAAC2D,IAAnC,CAAwClC,MAAM,CAAC0C,4BAAP,EAAxC,CAFD,EAEiF;QAChF,OAAO,GAAP;MACA,CAjBsC,CAkBvC;MACA;MACA;;;MACA,OAAO,EAAP;IACA;;;WAED,+DAA2EC,OAA3E,EAAoF;MAAA,IAAnCC,SAAmC,SAAnCA,SAAmC;MAAA,IAAxBC,WAAwB,SAAxBA,WAAwB;;MACnF,IAAID,SAAJ,EAAe;QACd,OAAOD,OAAO,IAAIA,OAAO,CAACG,OAAR,KAAoB,KAA/B,GAAuCF,SAAvC,GAAmDA,SAAS,GAAG,GAAtE;MACA;;MACD,IAAIC,WAAJ,EAAiB;QAChB,OAAO,EAAP;MACA;;MACD,OAAO,GAAP;IACA;;;WAED,qBAAY9D,KAAZ,EAAmB;MAClB,IAAI,CAAC,KAAKK,QAAV,EAAoB;QACnB;MACA,CAHiB,CAIlB;MACA;MACA;;;MACA,IAAI2D,KAAK,GAAG,CAAC,CAAb;MACA,IAAIC,CAAC,GAAG,CAAR;MACA,IAAMC,mBAAmB,GAAGlE,KAAK,CAACoB,aAAN,GAAsB,KAAK+C,8CAAL,CAAoDnE,KAApD,EAA2D;QAAE+D,OAAO,EAAE;MAAX,CAA3D,CAAtB,GAAuG,EAAnI;;MACA,OAAOE,CAAC,GAAGC,mBAAmB,CAAChC,MAApB,GAA6BlC,KAAK,CAACoE,mCAAN,GAA4ClC,MAApF,EAA4F;QAC3F8B,KAAK,GAAG,KAAK3D,QAAL,CAAciC,OAAd,CAAsB/D,iBAAtB,EAAyCyF,KAAK,GAAG,CAAjD,CAAR;QACAC,CAAC;MACD;;MACD,OAAOvF,0BAA0B,CAAC,KAAK2B,QAAN,EAAgB2D,KAAK,GAAG,CAAxB,CAAjC;IACA;;;WAED,mCAA0B3D,QAA1B,EAAoCL,KAApC,EAA2C;MAC1C,KAAKM,sBAAL,GAA8BD,QAA9B;MACA,KAAKE,+BAAL,GAAuCF,QAAvC,CAF0C,CAG1C;MACA;;MACA,KAAKG,uCAAL,GAA+C,CAAC,CAAhD,CAL0C,CAM1C;MACA;MACA;MACA;MACA;;MACA,IAAIR,KAAK,CAACoB,aAAV,EAAyB;QACxB,KAAKf,QAAL,GACC,KAAK8D,8CAAL,CAAoDnE,KAApD,EAA2DwB,OAA3D,CAAmE,SAAnE,EAA8EjD,iBAA9E,IACAE,MAAM,CAACF,iBAAD,EAAoByB,KAAK,CAACW,WAAN,CAAkBuB,MAAtC,CADN,GAEA,GAFA,GAGA7B,QAJD;MAKA,CAND,MAMO;QACN,KAAKA,QAAL,GAAgBA,QAAhB;MACA;IACD;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;;WACC,8BAAqBY,MAArB,SAKG;MAAA,IAJFH,yBAIE,SAJFA,yBAIE;MAAA,IAHFM,aAGE,SAHFA,aAGE;MAAA,IAFFC,cAEE,SAFFA,cAEE;MAAA,IADFgD,4CACE,SADFA,4CACE;MACF,IAAIZ,OAAO,GAAGxC,MAAM,CAACwC,OAAP,EAAd;MAEA;;MACA,IAAIhE,kCAAJ,EAAwC;QACvCgE,OAAO,GAAGA,OAAO,CAChB;QADgB,CAEfjC,OAFQ,CAEA9B,8BAA8B,EAF9B,EAEkC,KAFlC,EAGT;QAHS,CAIR8B,OAJQ,CAIA7B,+BAA+B,EAJ/B,EAImC,KAJnC,CAAV;MAKA,CAVC,CAYF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAI2E,MAAM,GAAG/E,0BAA0B,CAACwD,KAA3B,CAAiCU,OAAjC,EAA0C,CAA1C,CAAb,CApBE,CAsBF;MACA;;MACA,IAAI3C,yBAAyB,CAACoB,MAA1B,GAAmCoC,MAAM,CAACpC,MAA9C,EAAsD;QACrD;MACA,CA1BC,CA4BF;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MAEA,IAAMqC,aAAa,GAAG,IAAI1E,MAAJ,CAAW,MAAM4D,OAAN,GAAgB,GAA3B,CAAtB;MACA,IAAMe,yBAAyB,GAAG1D,yBAAyB,CAACU,OAA1B,CAAkC,KAAlC,EAAyCnC,WAAzC,CAAlC,CA5DE,CA8DF;MACA;MACA;;MACA,IAAIkF,aAAa,CAACpB,IAAd,CAAmBqB,yBAAnB,CAAJ,EAAmD;QAClDF,MAAM,GAAGE,yBAAT;MACA;;MAED,IAAIC,YAAY,GAAG,KAAKpB,eAAL,CAAqBpC,MAArB,EAA6BG,aAA7B,CAAnB;MACA,IAAIsD,gCAAJ,CAtEE,CAwEF;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAKvD,qCAAL,CAA2CF,MAA3C,EAAmD;QAAEG,aAAa,EAAbA,aAAF;QAAiBC,cAAc,EAAdA;MAAjB,CAAnD,CAAJ,EAA2F;QAC1F,IAAMsD,8BAA8B,GAAGF,YAAY,CAACjD,OAAb,CACtCtC,mBADsC,EAEtC+B,MAAM,CAAC0C,4BAAP,EAFsC,CAAvC,CAD0F,CAK1F;QACA;QACA;QACA;QACA;QACA;QACA;;QACA;;QACA,IAAI1E,WAAW,CAACgC,MAAM,CAAC0C,4BAAP,EAAD,CAAX,KAAuD,CAACtC,cAAc,IAAI,EAAnB,IAAyBpC,WAAW,CAAC,IAAD,CAA/F,EAAuG;UACtGwF,YAAY,GAAGE,8BAAf;UACAD,gCAAgC,GAAG,IAAnC,CAFsG,CAGtG;UACA;;UACA,IAAIrD,cAAJ,EAAoB;YACnB,IAAI4C,CAAC,GAAG5C,cAAc,CAACa,MAAvB;;YACA,OAAO+B,CAAC,GAAG,CAAX,EAAc;cACbQ,YAAY,GAAGA,YAAY,CAACjD,OAAb,CAAqB,IAArB,EAA2BjD,iBAA3B,CAAf;cACA0F,CAAC;YACD;UACD;QACD;MACD,CAzGC,CA2GF;;;MACA,IAAI5D,QAAQ,GAAGiE,MAAM,CACpB;MADoB,CAEnB9C,OAFa,CAEL,IAAI3B,MAAJ,CAAW4D,OAAX,CAFK,EAEgBgB,YAFhB,EAGd;MAHc,CAIbjD,OAJa,CAIL,IAAI3B,MAAJ,CAAWR,WAAX,EAAwB,GAAxB,CAJK,EAIyBd,iBAJzB,CAAf,CA5GE,CAkHF;MACA;MACA;MACA;;MACA,IAAI,CAACmG,gCAAL,EAAuC;QACtC,IAAIL,4CAAJ,EAAkD;UACjD;UACAhE,QAAQ,GAAG5B,MAAM,CAACF,iBAAD,EAAoB8F,4CAA4C,CAACnC,MAAjE,CAAN,GACV,GADU,GAEV7B,QAFD;QAGA,CALD,MAKO,IAAIgB,cAAJ,EAAoB;UAC1B;UACAhB,QAAQ,GAAG5B,MAAM,CAACF,iBAAD,EAAoB8C,cAAc,CAACa,MAAnC,CAAN,GACV,KAAKZ,+BAAL,CAAqCL,MAArC,CADU,GAEVZ,QAFD;QAGA;MACD;;MAED,IAAIe,aAAJ,EAAmB;QAClBf,QAAQ,GAAGjB,gCAAgC,CAACiB,QAAD,CAA3C;MACA;;MAED,OAAOA,QAAP;IACA;;;WAED,wCAA+BiE,MAA/B,EAAuC;MACtC,IAAMM,MAAM,GAAG/F,0BAA0B,CACxC,KAAK0B,+BADmC,EAExC,KAAKC,uCAFmC,EAGxC8D,MAHwC,CAAzC;;MAMA,IAAI,CAACM,MAAL,EAAa;QACZ;QACA,KAAK1E,WAAL;QACA;MACA;;MAED,KAAKK,+BAAL,GAAuCqE,MAAM,CAAC,CAAD,CAA7C;MACA,KAAKpE,uCAAL,GAA+CoE,MAAM,CAAC,CAAD,CAArD,CAdsC,CAgBtC;;MACA,OAAOlG,0BAA0B,CAAC,KAAK6B,+BAAN,EAAuC,KAAKC,uCAAL,GAA+C,CAAtF,CAAjC,CAjBsC,CAmBtC;MACA;MACA;MACA;IACA;;;WAED,+CAAsCS,MAAtC,SAAiF;MAAA,IAAjCG,aAAiC,SAAjCA,aAAiC;MAAA,IAAlBC,cAAkB,SAAlBA,cAAkB;;MAChF,IAAIJ,MAAM,CAAC0C,4BAAP,EAAJ,EAA2C;QAC1C;QACA;QACA;QACA,IAAMpB,kBAAkB,GAAGtB,MAAM,CAACsB,kBAAP,EAA3B;;QACA,IAAKA,kBAAkB,IAAIlB,cAAvB,IACF,CAACkB,kBAAD,IAAuB,CAACnB,aAD1B,EAC0C;UACzC,OAAO,IAAP;QACA;MACD;IACD;;;;;;SAhmBmBrB,kB"}