// usePurchase.js

import { useState } from "react";
import axios from 'axios';
import { buildApiUrl } from '../utils/apiConfig';

const usePurchase = () => {
  const [successMessage, setSuccess] = useState("");
  const [errorMessage, setError] = useState("");

  const purchaseProduct = async ({ size, quantity, color, payment_method, price, product_id }) => {
    if (!size || !quantity || !payment_method || !price || !product_id) {
      setError("Please fill in all required fields.");
      return null;
    }

    // Determine the method based on payment_method
    let Method;
    if (payment_method === "Online") {
      Method = "online";
    } else if (payment_method === "Checkout") {
      Method = "checkout";
    } else if (payment_method === "COD") {
      Method = "cod";
    } else {
      setError("Invalid payment method provided.");
      return null;
    }

    try {
      // Use axios with withCredentials to include HTTP-only cookies
      const response = await axios.post(
        buildApiUrl('purchases'),
        { size, quantity, color, payment_method, price, product_id },
        {
          headers: {
            "Content-Type": "application/json",
          },
          withCredentials: true, // Important for HTTP-only cookies to be included
        }
      );

      const purchaseData = response.data;

      setSuccess(purchaseData.message);
      setError("");
      return purchaseData;
    } catch (err) {
      console.error("Purchase error details:", {
        status: err.response?.status,
        data: err.response?.data,
        message: err.message
      });

      const errorMessage = err.response?.data?.message || err.message || "An error occurred during purchase.";
      setError(errorMessage);
      setSuccess("");

      // Re-throw the error with additional context for the Payment component
      const enhancedError = new Error(errorMessage);
      enhancedError.response = err.response;
      throw enhancedError;
    }
  };

  return { purchaseProduct, successMessage, errorMessage };
};

export default usePurchase;
