import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_BASE64 = "isBase64";
/**
 * Checks if a string is base64 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function isBase64(value: unknown, options?: ValidatorJS.IsBase64Options): boolean;
/**
 * Checks if a string is base64 encoded.
 * If given value is not a string, then it returns false.
 */
export declare function IsBase64(options?: ValidatorJS.IsBase64Options, validationOptions?: ValidationOptions): PropertyDecorator;
