import { ValidationOptions } from '../ValidationOptions';
export declare const IS_HALF_WIDTH = "isHalfWidth";
/**
 * Checks if the string contains any half-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function isHalfWidth(value: unknown): boolean;
/**
 * Checks if the string contains any half-width chars.
 * If given value is not a string, then it returns false.
 */
export declare function IsHalfWidth(validationOptions?: ValidationOptions): PropertyDecorator;
