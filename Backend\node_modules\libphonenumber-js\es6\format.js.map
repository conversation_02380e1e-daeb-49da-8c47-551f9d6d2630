{"version": 3, "file": "format.js", "names": ["matchesEntirely", "formatNationalNumberUsingFormat", "<PERSON><PERSON><PERSON>", "getCountryCallingCode", "getIddPrefix", "formatRFC3966", "DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "ext", "formatNumber", "input", "format", "options", "country", "hasCountry", "Error", "countryCallingCode", "selectNumberingPlan", "phone", "nationalNumber", "v2", "number", "formatNationalNumber", "carrierCode", "addExtension", "fromCountry", "formatIDD", "formatAs", "chooseFormatForNumber", "formats", "useInternationalFormat", "withNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "availableFormats", "nationalNnumber", "leadingDigitsPatterns", "length", "lastLeadingDigitsPattern", "search", "pattern", "fromCountryCallingCode", "iddPrefix", "undefined"], "sources": ["../source/format.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\nimport Metadata, { getCountryCallingCode } from './metadata.js'\r\nimport getIddPrefix from './helpers/getIddPrefix.js'\r\nimport { formatRFC3966 } from './helpers/RFC3966.js'\r\n\r\nconst DEFAULT_OPTIONS = {\r\n\tformatExtension: (formattedNumber, extension, metadata) => `${formattedNumber}${metadata.ext()}${extension}`\r\n}\r\n\r\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatNumber(input, format, options, metadata) {\r\n\t// Apply default options.\r\n\tif (options) {\r\n\t\toptions = { ...DEFAULT_OPTIONS, ...options }\r\n\t} else {\r\n\t\toptions = DEFAULT_OPTIONS\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (input.country && input.country !== '001') {\r\n\t\t// Validate `input.country`.\r\n\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t}\r\n\t\tmetadata.country(input.country)\r\n\t}\r\n\telse if (input.countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t}\r\n\telse return input.phone || ''\r\n\r\n\tconst countryCallingCode = metadata.countryCallingCode()\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// This variable should have been declared inside `case`s\r\n\t// but Babel has a bug and it says \"duplicate variable declaration\".\r\n\tlet number\r\n\r\n\tswitch (format) {\r\n\t\tcase 'NATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options)\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'INTERNATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn `+${countryCallingCode}`\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options)\r\n\t\t\tnumber = `+${countryCallingCode} ${number}`\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'E.164':\r\n\t\t\t// `E.164` doesn't define \"phone number extensions\".\r\n\t\t\treturn `+${countryCallingCode}${nationalNumber}`\r\n\r\n\t\tcase 'RFC3966':\r\n\t\t\treturn formatRFC3966({\r\n\t\t\t\tnumber: `+${countryCallingCode}${nationalNumber}`,\r\n\t\t\t\text: input.ext\r\n\t\t\t})\r\n\r\n\t\t// For reference, here's Google's IDD formatter:\r\n\t\t// https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\r\n\t\t// Not saying that this IDD formatter replicates it 1:1, but it seems to work.\r\n\t\t// Who would even need to format phone numbers in IDD format anyway?\r\n\t\tcase 'IDD':\r\n\t\t\tif (!options.fromCountry) {\r\n\t\t\t\treturn\r\n\t\t\t\t// throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\r\n\t\t\t}\r\n\t\t\tconst formattedNumber = formatIDD(\r\n\t\t\t\tnationalNumber,\r\n\t\t\t\tinput.carrierCode,\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\toptions.fromCountry,\r\n\t\t\t\tmetadata\r\n\t\t\t)\r\n\t\t\treturn addExtension(formattedNumber, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unknown \"format\" argument passed to \"formatNumber()\": \"${format}\"`)\r\n\t}\r\n}\r\n\r\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\r\n\tconst format = chooseFormatForNumber(metadata.formats(), number)\r\n\tif (!format) {\r\n\t\treturn number\r\n\t}\r\n\treturn formatNationalNumberUsingFormat(\r\n\t\tnumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tuseInternationalFormat: formatAs === 'INTERNATIONAL',\r\n\t\t\twithNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && (options && options.nationalPrefix === false) ? false : true,\r\n\t\t\tcarrierCode,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n}\r\n\r\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\r\n\tfor (const format of availableFormats) {\r\n\t\t// Validate leading digits.\r\n\t\t// The test case for \"else path\" could be found by searching for\r\n\t\t// \"format.leadingDigitsPatterns().length === 0\".\r\n\t\tif (format.leadingDigitsPatterns().length > 0) {\r\n\t\t\t// The last leading_digits_pattern is used here, as it is the most detailed\r\n\t\t\tconst lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]\r\n\t\t\t// If leading digits don't match then move on to the next phone number format\r\n\t\t\tif (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Check that the national number matches the phone number format regular expression\r\n\t\tif (matchesEntirely(nationalNnumber, format.pattern())) {\r\n\t\t\treturn format\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\r\n\treturn ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber\r\n}\r\n\r\nfunction formatIDD(\r\n\tnationalNumber,\r\n\tcarrierCode,\r\n\tcountryCallingCode,\r\n\tfromCountry,\r\n\tmetadata\r\n) {\r\n\tconst fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata)\r\n\t// When calling within the same country calling code.\r\n\tif (fromCountryCallingCode === countryCallingCode) {\r\n\t\tconst formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata)\r\n\t\t// For NANPA regions, return the national format for these regions\r\n\t\t// but prefix it with the country calling code.\r\n\t\tif (countryCallingCode === '1') {\r\n\t\t\treturn countryCallingCode + ' ' + formattedNumber\r\n\t\t}\r\n\t\t// If regions share a country calling code, the country calling code need\r\n\t\t// not be dialled. This also applies when dialling within a region, so this\r\n\t\t// if clause covers both these cases. Technically this is the case for\r\n\t\t// dialling from La Reunion to other overseas departments of France (French\r\n\t\t// Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\r\n\t\t// this edge case for now and for those cases return the version including\r\n\t\t// country calling code. Details here:\r\n\t\t// http://www.petitfute.com/voyage/225-info-pratiques-reunion\r\n\t\t//\r\n\t\treturn formattedNumber\r\n\t}\r\n\tconst iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata)\r\n\tif (iddPrefix) {\r\n\t\treturn `${iddPrefix} ${countryCallingCode} ${formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata)}`\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA,OAAOA,eAAP,MAA4B,8BAA5B;AACA,OAAOC,+BAAP,MAA4C,8CAA5C;AACA,OAAOC,QAAP,IAAmBC,qBAAnB,QAAgD,eAAhD;AACA,OAAOC,YAAP,MAAyB,2BAAzB;AACA,SAASC,aAAT,QAA8B,sBAA9B;AAEA,IAAMC,eAAe,GAAG;EACvBC,eAAe,EAAE,yBAACC,eAAD,EAAkBC,SAAlB,EAA6BC,QAA7B;IAAA,iBAA6CF,eAA7C,SAA+DE,QAAQ,CAACC,GAAT,EAA/D,SAAgFF,SAAhF;EAAA;AADM,CAAxB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASG,YAAT,CAAsBC,KAAtB,EAA6BC,MAA7B,EAAqCC,OAArC,EAA8CL,QAA9C,EAAwD;EACtE;EACA,IAAIK,OAAJ,EAAa;IACZA,OAAO,mCAAQT,eAAR,GAA4BS,OAA5B,CAAP;EACA,CAFD,MAEO;IACNA,OAAO,GAAGT,eAAV;EACA;;EAEDI,QAAQ,GAAG,IAAIR,QAAJ,CAAaQ,QAAb,CAAX;;EAEA,IAAIG,KAAK,CAACG,OAAN,IAAiBH,KAAK,CAACG,OAAN,KAAkB,KAAvC,EAA8C;IAC7C;IACA,IAAI,CAACN,QAAQ,CAACO,UAAT,CAAoBJ,KAAK,CAACG,OAA1B,CAAL,EAAyC;MACxC,MAAM,IAAIE,KAAJ,4BAA8BL,KAAK,CAACG,OAApC,EAAN;IACA;;IACDN,QAAQ,CAACM,OAAT,CAAiBH,KAAK,CAACG,OAAvB;EACA,CAND,MAOK,IAAIH,KAAK,CAACM,kBAAV,EAA8B;IAClCT,QAAQ,CAACU,mBAAT,CAA6BP,KAAK,CAACM,kBAAnC;EACA,CAFI,MAGA,OAAON,KAAK,CAACQ,KAAN,IAAe,EAAtB;;EAEL,IAAMF,kBAAkB,GAAGT,QAAQ,CAACS,kBAAT,EAA3B;EAEA,IAAMG,cAAc,GAAGP,OAAO,CAACQ,EAAR,GAAaV,KAAK,CAACS,cAAnB,GAAoCT,KAAK,CAACQ,KAAjE,CAxBsE,CA0BtE;EACA;;EACA,IAAIG,MAAJ;;EAEA,QAAQV,MAAR;IACC,KAAK,UAAL;MACC;MACA;MACA,IAAI,CAACQ,cAAL,EAAqB;QACpB,OAAO,EAAP;MACA;;MACDE,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiBT,KAAK,CAACa,WAAvB,EAAoC,UAApC,EAAgDhB,QAAhD,EAA0DK,OAA1D,CAA7B;MACA,OAAOY,YAAY,CAACH,MAAD,EAASX,KAAK,CAACF,GAAf,EAAoBD,QAApB,EAA8BK,OAAO,CAACR,eAAtC,CAAnB;;IAED,KAAK,eAAL;MACC;MACA;MACA,IAAI,CAACe,cAAL,EAAqB;QACpB,kBAAWH,kBAAX;MACA;;MACDK,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCZ,QAAxC,EAAkDK,OAAlD,CAA7B;MACAS,MAAM,cAAOL,kBAAP,cAA6BK,MAA7B,CAAN;MACA,OAAOG,YAAY,CAACH,MAAD,EAASX,KAAK,CAACF,GAAf,EAAoBD,QAApB,EAA8BK,OAAO,CAACR,eAAtC,CAAnB;;IAED,KAAK,OAAL;MACC;MACA,kBAAWY,kBAAX,SAAgCG,cAAhC;;IAED,KAAK,SAAL;MACC,OAAOjB,aAAa,CAAC;QACpBmB,MAAM,aAAML,kBAAN,SAA2BG,cAA3B,CADc;QAEpBX,GAAG,EAAEE,KAAK,CAACF;MAFS,CAAD,CAApB;IAKD;IACA;IACA;IACA;;IACA,KAAK,KAAL;MACC,IAAI,CAACI,OAAO,CAACa,WAAb,EAA0B;QACzB,OADyB,CAEzB;MACA;;MACD,IAAMpB,eAAe,GAAGqB,SAAS,CAChCP,cADgC,EAEhCT,KAAK,CAACa,WAF0B,EAGhCP,kBAHgC,EAIhCJ,OAAO,CAACa,WAJwB,EAKhClB,QALgC,CAAjC;MAOA,OAAOiB,YAAY,CAACnB,eAAD,EAAkBK,KAAK,CAACF,GAAxB,EAA6BD,QAA7B,EAAuCK,OAAO,CAACR,eAA/C,CAAnB;;IAED;MACC,MAAM,IAAIW,KAAJ,uEAAoEJ,MAApE,QAAN;EAjDF;AAmDA;;AAED,SAASW,oBAAT,CAA8BD,MAA9B,EAAsCE,WAAtC,EAAmDI,QAAnD,EAA6DpB,QAA7D,EAAuEK,OAAvE,EAAgF;EAC/E,IAAMD,MAAM,GAAGiB,qBAAqB,CAACrB,QAAQ,CAACsB,OAAT,EAAD,EAAqBR,MAArB,CAApC;;EACA,IAAI,CAACV,MAAL,EAAa;IACZ,OAAOU,MAAP;EACA;;EACD,OAAOvB,+BAA+B,CACrCuB,MADqC,EAErCV,MAFqC,EAGrC;IACCmB,sBAAsB,EAAEH,QAAQ,KAAK,eADtC;IAECI,kBAAkB,EAAEpB,MAAM,CAACqB,sDAAP,MAAoEpB,OAAO,IAAIA,OAAO,CAACqB,cAAR,KAA2B,KAA1G,GAAmH,KAAnH,GAA2H,IAFhJ;IAGCV,WAAW,EAAXA,WAHD;IAIChB,QAAQ,EAARA;EAJD,CAHqC,CAAtC;AAUA;;AAED,OAAO,SAASqB,qBAAT,CAA+BM,gBAA/B,EAAiDC,eAAjD,EAAkE;EACxE,qDAAqBD,gBAArB,wCAAuC;IAAA,IAA5BvB,MAA4B;;IACtC;IACA;IACA;IACA,IAAIA,MAAM,CAACyB,qBAAP,GAA+BC,MAA/B,GAAwC,CAA5C,EAA+C;MAC9C;MACA,IAAMC,wBAAwB,GAAG3B,MAAM,CAACyB,qBAAP,GAA+BzB,MAAM,CAACyB,qBAAP,GAA+BC,MAA/B,GAAwC,CAAvE,CAAjC,CAF8C,CAG9C;;MACA,IAAIF,eAAe,CAACI,MAAhB,CAAuBD,wBAAvB,MAAqD,CAAzD,EAA4D;QAC3D;MACA;IACD,CAXqC,CAYtC;;;IACA,IAAIzC,eAAe,CAACsC,eAAD,EAAkBxB,MAAM,CAAC6B,OAAP,EAAlB,CAAnB,EAAwD;MACvD,OAAO7B,MAAP;IACA;EACD;AACD;;AAED,SAASa,YAAT,CAAsBnB,eAAtB,EAAuCG,GAAvC,EAA4CD,QAA5C,EAAsDH,eAAtD,EAAuE;EACtE,OAAOI,GAAG,GAAGJ,eAAe,CAACC,eAAD,EAAkBG,GAAlB,EAAuBD,QAAvB,CAAlB,GAAqDF,eAA/D;AACA;;AAED,SAASqB,SAAT,CACCP,cADD,EAECI,WAFD,EAGCP,kBAHD,EAICS,WAJD,EAKClB,QALD,EAME;EACD,IAAMkC,sBAAsB,GAAGzC,qBAAqB,CAACyB,WAAD,EAAclB,QAAQ,CAACA,QAAvB,CAApD,CADC,CAED;;EACA,IAAIkC,sBAAsB,KAAKzB,kBAA/B,EAAmD;IAClD,IAAMX,eAAe,GAAGiB,oBAAoB,CAACH,cAAD,EAAiBI,WAAjB,EAA8B,UAA9B,EAA0ChB,QAA1C,CAA5C,CADkD,CAElD;IACA;;IACA,IAAIS,kBAAkB,KAAK,GAA3B,EAAgC;MAC/B,OAAOA,kBAAkB,GAAG,GAArB,GAA2BX,eAAlC;IACA,CANiD,CAOlD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,OAAOA,eAAP;EACA;;EACD,IAAMqC,SAAS,GAAGzC,YAAY,CAACwB,WAAD,EAAckB,SAAd,EAAyBpC,QAAQ,CAACA,QAAlC,CAA9B;;EACA,IAAImC,SAAJ,EAAe;IACd,iBAAUA,SAAV,cAAuB1B,kBAAvB,cAA6CM,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCZ,QAAxC,CAAjE;EACA;AACD"}