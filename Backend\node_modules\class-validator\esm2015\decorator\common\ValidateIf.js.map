{"version": 3, "file": "ValidateIf.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/ValidateIf.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AAEpE;;GAEG;AACH,MAAM,UAAU,UAAU,CACxB,SAA+C,EAC/C,iBAAqC;IAErC,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,MAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,eAAe,CAAC,sBAAsB;YAC5C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,WAAW,EAAE,CAAC,SAAS,CAAC;YACxB,iBAAiB,EAAE,iBAAiB;SACrC,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * Ignores the other validators on a property when the provided condition function returns false.\n */\nexport function ValidateIf(\n  condition: (object: any, value: any) => boolean,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.CONDITIONAL_VALIDATION,\n      target: object.constructor,\n      propertyName: propertyName,\n      constraints: [condition],\n      validationOptions: validationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}