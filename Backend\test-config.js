// test-config.js
// Test PhonePe configuration without making API calls

import { config } from 'dotenv';
import crypto from 'crypto';

// Load environment variables
config();

function testPhonePeConfig() {
  console.log('🧪 Testing PhonePe Configuration (No API Calls)...\n');
  
  // Check environment variables
  console.log('📋 Environment Configuration:');
  console.log(`  API_STATUS: ${process.env.API_STATUS}`);
  console.log(`  MERCHANT_ID_UAT: ${process.env.MERCHANT_ID_UAT ? '✅ Set' : '❌ Missing'}`);
  console.log(`  SALT_KEY_UAT: ${process.env.SALT_KEY_UAT ? '✅ Set' : '❌ Missing'}`);
  console.log(`  SALT_INDEX: ${process.env.SALT_INDEX}`);
  console.log(`  UAT_URL_PAY: ${process.env.UAT_URL_PAY}`);
  console.log(`  BASE_URL: ${process.env.BASE_URL}`);
  console.log(`  REDIRECTURL: ${process.env.REDIRECTURL}\n`);
  
  // Validate required fields
  const requiredFields = [
    'API_STATUS',
    'MERCHANT_ID_UAT', 
    'SALT_KEY_UAT',
    'SALT_INDEX',
    'UAT_URL_PAY',
    'BASE_URL'
  ];
  
  let allValid = true;
  console.log('✅ Validation Results:');
  
  requiredFields.forEach(field => {
    const value = process.env[field];
    const isValid = value && value.trim() !== '';
    console.log(`  ${field}: ${isValid ? '✅ Valid' : '❌ Missing/Empty'}`);
    if (!isValid) allValid = false;
  });
  
  if (!allValid) {
    console.log('\n❌ Configuration is incomplete!');
    return false;
  }
  
  // Test payload generation
  console.log('\n🔧 Testing Payload Generation:');
  
  const testData = {
    phone: '9999999999',
    price: 100,
    transactionId: 'TEST-' + Date.now()
  };
  
  const merchantId = process.env.MERCHANT_ID_UAT;
  const saltKey = process.env.SALT_KEY_UAT;
  const saltIndex = process.env.SALT_INDEX;
  const baseUrl = process.env.BASE_URL;
  const redirectUrl = process.env.REDIRECTURL;
  
  const payload = {
    merchantId,
    merchantTransactionId: testData.transactionId,
    merchantUserId: "M-" + Date.now(),
    amount: Number(testData.price) * 100,
    redirectUrl: baseUrl + redirectUrl,
    redirectMode: "POST",
    callbackUrl: baseUrl + redirectUrl,
    mobileNumber: testData.phone,
    paymentInstrument: { type: "PAY_PAGE" }
  };
  
  console.log('📦 Generated Payload:');
  console.log(JSON.stringify(payload, null, 2));
  
  // Test checksum generation
  const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString('base64');
  const dataToHash = payloadBase64 + "/pg/v1/pay" + saltKey;
  const sha256Hash = crypto.createHash('sha256').update(dataToHash).digest('hex');
  const checksum = `${sha256Hash}###${saltIndex}`;
  
  console.log('\n🔐 Checksum Generation:');
  console.log(`  Payload Base64 Length: ${payloadBase64.length}`);
  console.log(`  Data to Hash Length: ${dataToHash.length}`);
  console.log(`  SHA256 Hash: ${sha256Hash}`);
  console.log(`  Final Checksum: ${checksum}`);
  
  console.log('\n✅ Configuration appears valid!');
  console.log('\n💡 If payments still fail, the issue might be:');
  console.log('  - Network connectivity to PhonePe servers');
  console.log('  - PhonePe sandbox service availability');
  console.log('  - Merchant account status with PhonePe');
  console.log('  - API endpoint changes by PhonePe');
  
  return true;
}

// Run the test
try {
  const result = testPhonePeConfig();
  console.log(`\n🎯 Test Result: ${result ? 'PASSED' : 'FAILED'}`);
} catch (error) {
  console.error('\n💥 Test Error:', error.message);
}
