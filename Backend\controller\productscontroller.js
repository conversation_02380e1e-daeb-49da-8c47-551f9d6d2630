// controller/ProductsController.js

export const getUserItems = async (req, res) => {
  try {
    const email = req.user?.email;
    if (!email) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const Products = req.app.locals.Products;
    const response = await Products.find({ email });
    res.status(200).json({ products: response });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching products",
      error: error.message,
    });
  }
};

export const updateUserProducts = async (req, res) => {
  const { product_id, wishlist, cart } = req.body;
  const email = req.user?.email;
  if (!email || !product_id) {
    return res.status(400).json({ message: "Email and Product ID are required." });
  }
  try {
    const Products = req.app.locals.Products;
    let data = await Products.findOne({ email, product_id });
    if (data) {
      // Update existing products record
      data.wishlist = typeof wishlist !== "undefined" ? wishlist : data.wishlist;
      data.cart = typeof cart !== "undefined" ? cart : data.cart;
      await data.save();
      return res.status(200).json({ message: "Products updated.", data });
    } else {
      // Create new products record
      const newProducts = new Products({ email, product_id, wishlist, cart });
      await newProducts.save();
      return res.status(201).json({ message: "Products created.", data: newProducts });
    }
  } catch (err) {
    return res.status(500).json({ message: "Something went wrong.", error: err.message });
  }
};
