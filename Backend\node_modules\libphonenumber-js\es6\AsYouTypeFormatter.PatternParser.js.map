{"version": 3, "file": "AsYouTypeFormatter.PatternParser.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "context", "or", "instructions", "parsePattern", "length", "Error", "branches", "op", "args", "concat", "expandSingleElementArray", "push", "pop", "match", "OPERATOR", "ILLEGAL_CHARACTER_REGEXP", "test", "getContext", "split", "operator", "before", "slice", "index", "rightPart", "startContext", "endContext", "oneOfSet", "parseOneOfSet", "values", "i", "prevValue", "charCodeAt", "nextValue", "value", "String", "fromCharCode", "RegExp", "array"], "sources": ["../source/AsYouTypeFormatter.PatternParser.js"], "sourcesContent": ["export default class PatternParser {\r\n\tparse(pattern) {\r\n\t\tthis.context = [{\r\n\t\t\tor: true,\r\n\t\t\tinstructions: []\r\n\t\t}]\r\n\r\n\t\tthis.parsePattern(pattern)\r\n\r\n\t\tif (this.context.length !== 1) {\r\n\t\t\tthrow new Error('Non-finalized contexts left when pattern parse ended')\r\n\t\t}\r\n\r\n\t\tconst { branches, instructions } = this.context[0]\r\n\r\n\t\tif (branches) {\r\n\t\t\treturn {\r\n\t\t\t\top: '|',\r\n\t\t\t\targs: branches.concat([\r\n\t\t\t\t\texpandSingleElementArray(instructions)\r\n\t\t\t\t])\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (instructions.length === 0) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tif (instructions.length === 1) {\r\n\t\t\treturn instructions[0]\r\n\t\t}\r\n\r\n\t\treturn instructions\r\n\t}\r\n\r\n\tstartContext(context) {\r\n\t\tthis.context.push(context)\r\n\t}\r\n\r\n\tendContext() {\r\n\t\tthis.context.pop()\r\n\t}\r\n\r\n\tgetContext() {\r\n\t\treturn this.context[this.context.length - 1]\r\n\t}\r\n\r\n\tparsePattern(pattern) {\r\n\t\tif (!pattern) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tconst match = pattern.match(OPERATOR)\r\n\t\tif (!match) {\r\n\t\t\tif (ILLEGAL_CHARACTER_REGEXP.test(pattern)) {\r\n\t\t\t\tthrow new Error(`Illegal characters found in a pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tthis.getContext().instructions = this.getContext().instructions.concat(\r\n\t\t\t\tpattern.split('')\r\n\t\t\t)\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tconst operator = match[1]\r\n\t\tconst before = pattern.slice(0, match.index)\r\n\t\tconst rightPart = pattern.slice(match.index + operator.length)\r\n\r\n\t\tswitch (operator) {\r\n\t\t\tcase '(?:':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\tor: true,\r\n\t\t\t\t\tinstructions: [],\r\n\t\t\t\t\tbranches: []\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ')':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\")\" operator must be preceded by \"(?:\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.getContext().instructions.length === 0) {\r\n\t\t\t\t\tthrow new Error('No instructions found after \"|\" operator in an \"or\" group')\r\n\t\t\t\t}\r\n\t\t\t\tconst { branches } = this.getContext()\r\n\t\t\t\tbranches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '|',\r\n\t\t\t\t\targs: branches\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '|':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\"|\" operator can only be used inside \"or\" groups')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\t// The top-level is an implicit \"or\" group, if required.\r\n\t\t\t\tif (!this.getContext().branches) {\r\n\t\t\t\t\t// `branches` are not defined only for the root implicit \"or\" operator.\r\n\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\tif (this.context.length === 1) {\r\n\t\t\t\t\t\tthis.getContext().branches = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error('\"branches\" not found in an \"or\" group context')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.getContext().branches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.getContext().instructions = []\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '[':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\toneOfSet: true\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ']':\r\n\t\t\t\tif (!this.getContext().oneOfSet) {\r\n\t\t\t\t\tthrow new Error('\"]\" operator must be preceded by \"[\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '[]',\r\n\t\t\t\t\targs: parseOneOfSet(before)\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\tdefault:\r\n\t\t\t\tthrow new Error(`Unknown operator: ${operator}`)\r\n\t\t}\r\n\r\n\t\tif (rightPart) {\r\n\t\t\tthis.parsePattern(rightPart)\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction parseOneOfSet(pattern) {\r\n\tconst values = []\r\n\tlet i = 0\r\n\twhile (i < pattern.length) {\r\n\t\tif (pattern[i] === '-') {\r\n\t\t\tif (i === 0 || i === pattern.length - 1) {\r\n\t\t\t\tthrow new Error(`Couldn't parse a one-of set pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tconst prevValue = pattern[i - 1].charCodeAt(0) + 1\r\n\t\t\tconst nextValue = pattern[i + 1].charCodeAt(0) - 1\r\n\t\t\tlet value = prevValue\r\n\t\t\twhile (value <= nextValue) {\r\n\t\t\t\tvalues.push(String.fromCharCode(value))\r\n\t\t\t\tvalue++\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalues.push(pattern[i])\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn values\r\n}\r\n\r\nconst ILLEGAL_CHARACTER_REGEXP = /[\\(\\)\\[\\]\\?\\:\\|]/\r\n\r\nconst OPERATOR = new RegExp(\r\n\t// any of:\r\n\t'(' +\r\n\t\t// or operator\r\n\t\t'\\\\|' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group start\r\n\t\t'\\\\(\\\\?\\\\:' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group end\r\n\t\t'\\\\)' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set start\r\n\t\t'\\\\[' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set end\r\n\t\t'\\\\]' +\r\n\t')'\r\n)\r\n\r\nfunction expandSingleElementArray(array) {\r\n\tif (array.length === 1) {\r\n\t\treturn array[0]\r\n\t}\r\n\treturn array\r\n}"], "mappings": ";;;;;;IAAqBA,a;;;;;;;WACpB,eAAMC,OAAN,EAAe;MACd,KAAKC,OAAL,GAAe,CAAC;QACfC,EAAE,EAAE,IADW;QAEfC,YAAY,EAAE;MAFC,CAAD,CAAf;MAKA,KAAKC,YAAL,CAAkBJ,OAAlB;;MAEA,IAAI,KAAKC,OAAL,CAAaI,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,sDAAV,CAAN;MACA;;MAED,qBAAmC,KAAKL,OAAL,CAAa,CAAb,CAAnC;MAAA,IAAQM,QAAR,kBAAQA,QAAR;MAAA,IAAkBJ,YAAlB,kBAAkBA,YAAlB;;MAEA,IAAII,QAAJ,EAAc;QACb,OAAO;UACNC,EAAE,EAAE,GADE;UAENC,IAAI,EAAEF,QAAQ,CAACG,MAAT,CAAgB,CACrBC,wBAAwB,CAACR,YAAD,CADH,CAAhB;QAFA,CAAP;MAMA;MAED;;;MACA,IAAIA,YAAY,CAACE,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,qBAAV,CAAN;MACA;;MAED,IAAIH,YAAY,CAACE,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,OAAOF,YAAY,CAAC,CAAD,CAAnB;MACA;;MAED,OAAOA,YAAP;IACA;;;WAED,sBAAaF,OAAb,EAAsB;MACrB,KAAKA,OAAL,CAAaW,IAAb,CAAkBX,OAAlB;IACA;;;WAED,sBAAa;MACZ,KAAKA,OAAL,CAAaY,GAAb;IACA;;;WAED,sBAAa;MACZ,OAAO,KAAKZ,OAAL,CAAa,KAAKA,OAAL,CAAaI,MAAb,GAAsB,CAAnC,CAAP;IACA;;;WAED,sBAAaL,OAAb,EAAsB;MACrB,IAAI,CAACA,OAAL,EAAc;QACb,MAAM,IAAIM,KAAJ,CAAU,qBAAV,CAAN;MACA;;MAED,IAAMQ,KAAK,GAAGd,OAAO,CAACc,KAAR,CAAcC,QAAd,CAAd;;MACA,IAAI,CAACD,KAAL,EAAY;QACX,IAAIE,wBAAwB,CAACC,IAAzB,CAA8BjB,OAA9B,CAAJ,EAA4C;UAC3C,MAAM,IAAIM,KAAJ,kDAAoDN,OAApD,EAAN;QACA;;QACD,KAAKkB,UAAL,GAAkBf,YAAlB,GAAiC,KAAKe,UAAL,GAAkBf,YAAlB,CAA+BO,MAA/B,CAChCV,OAAO,CAACmB,KAAR,CAAc,EAAd,CADgC,CAAjC;QAGA;MACA;;MAED,IAAMC,QAAQ,GAAGN,KAAK,CAAC,CAAD,CAAtB;MACA,IAAMO,MAAM,GAAGrB,OAAO,CAACsB,KAAR,CAAc,CAAd,EAAiBR,KAAK,CAACS,KAAvB,CAAf;MACA,IAAMC,SAAS,GAAGxB,OAAO,CAACsB,KAAR,CAAcR,KAAK,CAACS,KAAN,GAAcH,QAAQ,CAACf,MAArC,CAAlB;;MAEA,QAAQe,QAAR;QACC,KAAK,KAAL;UACC,IAAIC,MAAJ,EAAY;YACX,KAAKjB,YAAL,CAAkBiB,MAAlB;UACA;;UACD,KAAKI,YAAL,CAAkB;YACjBvB,EAAE,EAAE,IADa;YAEjBC,YAAY,EAAE,EAFG;YAGjBI,QAAQ,EAAE;UAHO,CAAlB;UAKA;;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKW,UAAL,GAAkBhB,EAAvB,EAA2B;YAC1B,MAAM,IAAII,KAAJ,CAAU,iDAAV,CAAN;UACA;;UACD,IAAIe,MAAJ,EAAY;YACX,KAAKjB,YAAL,CAAkBiB,MAAlB;UACA;;UACD,IAAI,KAAKH,UAAL,GAAkBf,YAAlB,CAA+BE,MAA/B,KAA0C,CAA9C,EAAiD;YAChD,MAAM,IAAIC,KAAJ,CAAU,2DAAV,CAAN;UACA;;UACD,uBAAqB,KAAKY,UAAL,EAArB;UAAA,IAAQX,QAAR,oBAAQA,QAAR;;UACAA,QAAQ,CAACK,IAAT,CACCD,wBAAwB,CACvB,KAAKO,UAAL,GAAkBf,YADK,CADzB;UAKA,KAAKuB,UAAL;UACA,KAAKR,UAAL,GAAkBf,YAAlB,CAA+BS,IAA/B,CAAoC;YACnCJ,EAAE,EAAE,GAD+B;YAEnCC,IAAI,EAAEF;UAF6B,CAApC;UAIA;;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKW,UAAL,GAAkBhB,EAAvB,EAA2B;YAC1B,MAAM,IAAII,KAAJ,CAAU,kDAAV,CAAN;UACA;;UACD,IAAIe,MAAJ,EAAY;YACX,KAAKjB,YAAL,CAAkBiB,MAAlB;UACA,CANF,CAOC;;;UACA,IAAI,CAAC,KAAKH,UAAL,GAAkBX,QAAvB,EAAiC;YAChC;;YACA;YACA,IAAI,KAAKN,OAAL,CAAaI,MAAb,KAAwB,CAA5B,EAA+B;cAC9B,KAAKa,UAAL,GAAkBX,QAAlB,GAA6B,EAA7B;YACA,CAFD,MAEO;cACN,MAAM,IAAID,KAAJ,CAAU,+CAAV,CAAN;YACA;UACD;;UACD,KAAKY,UAAL,GAAkBX,QAAlB,CAA2BK,IAA3B,CACCD,wBAAwB,CACvB,KAAKO,UAAL,GAAkBf,YADK,CADzB;UAKA,KAAKe,UAAL,GAAkBf,YAAlB,GAAiC,EAAjC;UACA;;QAED,KAAK,GAAL;UACC,IAAIkB,MAAJ,EAAY;YACX,KAAKjB,YAAL,CAAkBiB,MAAlB;UACA;;UACD,KAAKI,YAAL,CAAkB;YACjBE,QAAQ,EAAE;UADO,CAAlB;UAGA;;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKT,UAAL,GAAkBS,QAAvB,EAAiC;YAChC,MAAM,IAAIrB,KAAJ,CAAU,+CAAV,CAAN;UACA;;UACD,KAAKoB,UAAL;UACA,KAAKR,UAAL,GAAkBf,YAAlB,CAA+BS,IAA/B,CAAoC;YACnCJ,EAAE,EAAE,IAD+B;YAEnCC,IAAI,EAAEmB,aAAa,CAACP,MAAD;UAFgB,CAApC;UAIA;;QAED;;QACA;UACC,MAAM,IAAIf,KAAJ,6BAA+Bc,QAA/B,EAAN;MAlFF;;MAqFA,IAAII,SAAJ,EAAe;QACd,KAAKpB,YAAL,CAAkBoB,SAAlB;MACA;IACD;;;;;;SA5JmBzB,a;;AA+JrB,SAAS6B,aAAT,CAAuB5B,OAAvB,EAAgC;EAC/B,IAAM6B,MAAM,GAAG,EAAf;EACA,IAAIC,CAAC,GAAG,CAAR;;EACA,OAAOA,CAAC,GAAG9B,OAAO,CAACK,MAAnB,EAA2B;IAC1B,IAAIL,OAAO,CAAC8B,CAAD,CAAP,KAAe,GAAnB,EAAwB;MACvB,IAAIA,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAK9B,OAAO,CAACK,MAAR,GAAiB,CAAtC,EAAyC;QACxC,MAAM,IAAIC,KAAJ,gDAAkDN,OAAlD,EAAN;MACA;;MACD,IAAM+B,SAAS,GAAG/B,OAAO,CAAC8B,CAAC,GAAG,CAAL,CAAP,CAAeE,UAAf,CAA0B,CAA1B,IAA+B,CAAjD;MACA,IAAMC,SAAS,GAAGjC,OAAO,CAAC8B,CAAC,GAAG,CAAL,CAAP,CAAeE,UAAf,CAA0B,CAA1B,IAA+B,CAAjD;MACA,IAAIE,KAAK,GAAGH,SAAZ;;MACA,OAAOG,KAAK,IAAID,SAAhB,EAA2B;QAC1BJ,MAAM,CAACjB,IAAP,CAAYuB,MAAM,CAACC,YAAP,CAAoBF,KAApB,CAAZ;QACAA,KAAK;MACL;IACD,CAXD,MAWO;MACNL,MAAM,CAACjB,IAAP,CAAYZ,OAAO,CAAC8B,CAAD,CAAnB;IACA;;IACDA,CAAC;EACD;;EACD,OAAOD,MAAP;AACA;;AAED,IAAMb,wBAAwB,GAAG,kBAAjC;AAEA,IAAMD,QAAQ,GAAG,IAAIsB,MAAJ,EAChB;AACA,MACC;AACA,KAFD,GAGC;AACA,GAJD,GAKC;AACA,WAND,GAOC;AACA,GARD,GASC;AACA,KAVD,GAWC;AACA,GAZD,GAaC;AACA,KAdD,GAeC;AACA,GAhBD,GAiBC;AACA,KAlBD,GAmBA,GArBgB,CAAjB;;AAwBA,SAAS1B,wBAAT,CAAkC2B,KAAlC,EAAyC;EACxC,IAAIA,KAAK,CAACjC,MAAN,KAAiB,CAArB,EAAwB;IACvB,OAAOiC,KAAK,CAAC,CAAD,CAAZ;EACA;;EACD,OAAOA,KAAP;AACA"}