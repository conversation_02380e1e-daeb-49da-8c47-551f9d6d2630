// imageConfig.js
// Configuration for image URLs and CDN settings

/**
 * Image CDN Configuration
 * This file centralizes all image URL configurations to avoid hardcoding
 */

// Default image CDN URL - can be overridden via environment variables
const IMAGE_CDN_URL = "https://veirdo.in/cdn/shop/files";

// Default product image
const DEFAULT_PRODUCT_IMAGE = `${IMAGE_CDN_URL}/8_2be593bf-344e-4f3a-8a4b-bed67331917f.jpg?v=1725445401&width=360`;

// Placeholder image for when products don't have images
const PLACEHOLDER_IMAGE = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='360' height='360' viewBox='0 0 360 360'%3E%3Crect width='360' height='360' fill='%23e2e8f0'/%3E%3Ctext x='50%25' y='50%25' font-family='Arial' font-size='16' fill='%231e293b' text-anchor='middle' dominant-baseline='middle'%3ENo Image%3C/text%3E%3C/svg%3E";

// Image configuration object
export const imageConfig = {
  // CDN base URL
  cdnUrl: IMAGE_CDN_URL,
  
  // Default images
  defaultProduct: DEFAULT_PRODUCT_IMAGE,
  placeholder: PLACEHOLDER_IMAGE,
  
  // Helper function to get product image with fallback
  getProductImage: (imageUrl) => {
    if (!imageUrl || imageUrl.trim() === '') {
      return DEFAULT_PRODUCT_IMAGE;
    }
    return imageUrl;
  },
  
  // Helper function to get image with placeholder fallback
  getImageWithFallback: (imageUrl) => {
    if (!imageUrl || imageUrl.trim() === '') {
      return PLACEHOLDER_IMAGE;
    }
    return imageUrl;
  },
  
  // Helper function to build CDN URL
  buildCdnUrl: (imagePath) => {
    if (!imagePath) return DEFAULT_PRODUCT_IMAGE;
    if (imagePath.startsWith('http')) return imagePath;
    return `${IMAGE_CDN_URL}/${imagePath}`;
  }
};

// Export individual items for convenience
export const {
  cdnUrl,
  defaultProduct,
  placeholder,
  getProductImage,
  getImageWithFallback,
  buildCdnUrl
} = imageConfig;

export default imageConfig;
