// useAddress.js

import { useState, useEffect } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { selectUserProfile } from "../app/users/usersSelector";

// API configuration
import { buildApiUrl } from '../utils/apiConfig';

const useAddress = () => {
  const user = useSelector(selectUserProfile);
  const [addresses, setAddresses] = useState({
    primaryAddress: {},
    secondaryAddresses: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(!!user);

  // Fetch addresses
  const fetchAddresses = async () => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setIsAuthenticated(false);
      setLoading(false);
      return;
    }

    setIsAuthenticated(true);

    try {
      const response = await axios.get(buildApiUrl('addresses'), {
        withCredentials: true // Use HTTP-only cookies for authentication
      });
      setAddresses(response.data);
      setLoading(false);
      return response.data;
    } catch (err) {
      console.error("Error fetching addresses:", err);
      setError("Failed to load addresses. Please try again.");
      setLoading(false);
      return null;
    }
  };

  // Add a new address
  const addAddress = async (addressData) => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setIsAuthenticated(false);
      return { success: false, error: "User not authenticated" };
    }

    try {
      const response = await axios.post(
        buildApiUrl('addresses'),
        addressData,
        {
          headers: {
            "Content-Type": "application/json"
          },
          withCredentials: true // Use HTTP-only cookies for authentication
        }
      );

      // Check if the response has the addresses property
      if (response.data.addresses) {
        setAddresses(response.data.addresses);
        return { success: true, data: response.data.addresses };
      } else {
        // If not, refresh the addresses from the server
        await fetchAddresses();
        return { success: true };
      }
    } catch (err) {
      console.error("Error adding address:", err);
      return {
        success: false,
        error: err.response?.data?.message || "Failed to add address. Please try again."
      };
    }
  };

  // Update an existing address
  const updateAddress = async (addressId, addressData) => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setIsAuthenticated(false);
      return { success: false, error: "User not authenticated" };
    }

    try {
      const response = await axios.put(
        buildApiUrl('addresses'),
        {
          addressId,
          ...addressData
        },
        {
          headers: {
            "Content-Type": "application/json"
          },
          withCredentials: true // Use HTTP-only cookies for authentication
        }
      );

      // Check if the response has the addresses property
      if (response.data.addresses) {
        setAddresses(response.data.addresses);
        return { success: true, data: response.data.addresses };
      } else {
        // If not, refresh the addresses from the server
        await fetchAddresses();
        return { success: true };
      }
    } catch (err) {
      console.error("Error updating address:", err);
      return {
        success: false,
        error: err.response?.data?.message || "Failed to update address. Please try again."
      };
    }
  };

  // Delete an address
  const deleteAddress = async (addressId) => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setIsAuthenticated(false);
      return { success: false, error: "User not authenticated" };
    }

    try {
      const response = await axios.delete(
        buildApiUrl(`addresses/${addressId}`),
        {
          withCredentials: true // Use HTTP-only cookies for authentication
        }
      );

      // Check if the response has the addresses property
      if (response.data.addresses) {
        setAddresses(response.data.addresses);
        return { success: true, data: response.data.addresses };
      } else {
        // If not, refresh the addresses from the server
        await fetchAddresses();
        return { success: true };
      }
    } catch (err) {
      console.error("Error deleting address:", err);
      return {
        success: false,
        error: err.response?.data?.message || "Failed to delete address. Please try again."
      };
    }
  };

  // Set an address as primary
  const setPrimaryAddress = async (addressId) => {
    // Check if user is authenticated using Redux state
    if (!user) {
      setIsAuthenticated(false);
      return { success: false, error: "User not authenticated" };
    }

    try {
      const response = await axios.post(
        buildApiUrl('addresses/primary'),
        { addressId },
        {
          headers: {
            "Content-Type": "application/json"
          },
          withCredentials: true // Use HTTP-only cookies for authentication
        }
      );

      // Check if the response has the addresses property
      if (response.data.addresses) {
        setAddresses(response.data.addresses);
        return { success: true, data: response.data.addresses };
      } else {
        // If not, refresh the addresses from the server
        await fetchAddresses();
        return { success: true };
      }
    } catch (err) {
      console.error("Error setting primary address:", err);
      return {
        success: false,
        error: err.response?.data?.message || "Failed to set primary address. Please try again."
      };
    }
  };

  // Format address for display
  const formatAddress = (address) => {
    if (!address || Object.keys(address).length === 0) return "No address available";

    return [
      address.street,
      address.city,
      address.district,
      address.state,
      address.country,
      address.pincode
    ].filter(Boolean).join(", ");
  };

  // Load addresses on hook initialization
  useEffect(() => {
    fetchAddresses();
  }, [user]);

  return {
    addresses,
    loading,
    error,
    isAuthenticated,
    fetchAddresses,
    addAddress,
    updateAddress,
    deleteAddress,
    setPrimaryAddress,
    formatAddress
  };
};

export default useAddress;