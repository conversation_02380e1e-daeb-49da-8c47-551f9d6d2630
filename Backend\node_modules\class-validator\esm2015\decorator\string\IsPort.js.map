{"version": 3, "file": "IsPort.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsPort.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAEnD,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isPortValidator from 'validator/lib/isPort';\n\nexport const IS_PORT = 'isPort';\n\n/**\n * Check if the string is a valid port number.\n */\nexport function isPort(value: unknown): boolean {\n  return typeof value === 'string' && isPortValidator(value);\n}\n\n/**\n * Check if the string is a valid port number.\n */\nexport function IsPort(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_PORT,\n      validator: {\n        validate: (value, args): boolean => isPort(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a port', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}