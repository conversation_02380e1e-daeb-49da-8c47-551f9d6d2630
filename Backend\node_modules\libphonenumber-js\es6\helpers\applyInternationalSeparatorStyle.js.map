{"version": 3, "file": "applyInternationalSeparatorStyle.js", "names": ["VALID_PUNCTUATION", "applyInternationalSeparatorStyle", "formattedNumber", "replace", "RegExp", "trim"], "sources": ["../../source/helpers/applyInternationalSeparatorStyle.js"], "sourcesContent": ["import { VALID_PUNCTUATION } from '../constants.js'\r\n\r\n// Removes brackets and replaces dashes with spaces.\r\n//\r\n// E.g. \"(999) 111-22-33\" -> \"999 111 22 33\"\r\n//\r\n// For some reason Google's metadata contains `<intlFormat/>`s with brackets and dashes.\r\n// Meanwhile, there's no single opinion about using punctuation in international phone numbers.\r\n//\r\n// For example, Google's `<intlFormat/>` for USA is `******-373-4253`.\r\n// And here's a quote from WikiPedia's \"North American Numbering Plan\" page:\r\n// https://en.wikipedia.org/wiki/North_American_Numbering_Plan\r\n//\r\n// \"The country calling code for all countries participating in the NANP is 1.\r\n// In international format, an NANP number should be listed as ****** 555 01 00,\r\n// where 301 is an area code (Maryland).\"\r\n//\r\n// I personally prefer the international format without any punctuation.\r\n// For example, brackets are remnants of the old age, meaning that the\r\n// phone number part in brackets (so called \"area code\") can be omitted\r\n// if dialing within the same \"area\".\r\n// And hyphens were clearly introduced for splitting local numbers into memorizable groups.\r\n// For example, remembering \"5553535\" is difficult but \"555-35-35\" is much simpler.\r\n// Imagine a man taking a bus from home to work and seeing an ad with a phone number.\r\n// He has a couple of seconds to memorize that number until it passes by.\r\n// If it were spaces instead of hyphens the man wouldn't necessarily get it,\r\n// but with hyphens instead of spaces the grouping is more explicit.\r\n// I personally think that hyphens introduce visual clutter,\r\n// so I prefer replacing them with spaces in international numbers.\r\n// In the modern age all output is done on displays where spaces are clearly distinguishable\r\n// so hyphens can be safely replaced with spaces without losing any legibility.\r\n//\r\nexport default function applyInternationalSeparatorStyle(formattedNumber) {\r\n\treturn formattedNumber.replace(new RegExp(`[${VALID_PUNCTUATION}]+`, 'g'), ' ').trim()\r\n}"], "mappings": "AAAA,SAASA,iBAAT,QAAkC,iBAAlC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,gCAAT,CAA0CC,eAA1C,EAA2D;EACzE,OAAOA,eAAe,CAACC,OAAhB,CAAwB,IAAIC,MAAJ,YAAeJ,iBAAf,SAAsC,GAAtC,CAAxB,EAAoE,GAApE,EAAyEK,IAAzE,EAAP;AACA"}