# Belilly API
mylovelysis27lilly
Backend API for the Belilly e-commerce platform.

## Features

- User authentication and authorization
- Product management
- Order processing
- Search functionality
- AI assistant integration via AWS Lambda
- Payment processing (COD and Online)

## Tech Stack

- Node.js
- Express.js
- MongoDB (multiple databases)
- Redis (optional)
- JWT for authentication
- AWS Lambda for AI assistant
- MCP (Model Context Protocol) for tool access

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB
- Redis (optional)
- mkcert (optional, for HTTPS in development)

### Installation

1. Clone the repository
   ```bash
   git clone https://github.com/rajibbaidhya/api.belilly.git
   cd api.belilly
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Configure AWS Lambda AI Assistant (see Lambda deployment guide)
   ```bash
   # AI processing is now handled by AWS Lambda
   # No additional AI dependencies needed in backend
   ```

4. Configure your `.env` file in the root directory with the following variables:
   ```
   # Server
   PORT=3000
   NODE_ENV=production

   # MongoDB
   DB1_URI=mongodb://localhost:27017/customer_db
   DB2_URI=mongodb://localhost:27017/products_db
   DB3_URI=mongodb://localhost:27017/purchases_db

   # JWT
   JWT_SECRET_KEY=your_jwt_secret

   # Security settings (optimized for Nginx)
   USE_SECURE_COOKIES=true
   TRUST_PROXY=true
   API_BASE_PATH=/api

   # Redis (optional)
   # REDIS_HOST=localhost
   REDIS_PORT=6379

   # AI Assistant Lambda Configuration
   LAMBDA_AI_ASSISTANT_URL=https://your-lambda-url.lambda-url.us-east-1.on.aws/
   MCP_API_KEY=your_mcp_api_key_here
   ```

5. Start the server
   ```bash
   node server.js
   ```

### Single Environment Approach

The API server uses a single environment approach optimized for production behind Nginx. This means:

1. The server is configured to always use secure cookies
2. The server always trusts the first proxy (Nginx)
3. The server always enforces HTTPS

This approach simplifies configuration and ensures consistent behavior across all deployments.

#### Configuration

Make sure these settings are in your `.env` file:
```
NODE_ENV=production
USE_SECURE_COOKIES=true
TRUST_PROXY=true
API_BASE_PATH=/api  # If your Nginx location block is /api
```

For detailed Nginx setup instructions, see [NGINX_SETUP.md](./NGINX_SETUP.md).

## API Documentation

### Authentication

- **POST /v1/signup** - Register a new user
- **POST /v1/auth/login** - Login and get JWT token

### User Profile

- **GET /v1/profile** - Get user profile
- **PUT /v1/profile** - Update user profile
- **PUT /v1/profile/password** - Change password
- **GET /v1/profile/terminate** - Delete account

### Products

- **GET /v1/product** - Get all products
- **GET /v1/product/status** - Get product status
- **POST /v1/product/cart** - Add product to cart
- **POST /v1/product/wishlist** - Add product to wishlist

### Orders

- **GET /v1/orders** - Get user orders

### Purchases

- **POST /v1/purchases** - Create a new purchase
- **POST /v1/purchases/checkout** - Checkout multiple items

### Search

- **GET /search** - Search products

## Security Features

- HTTPS enforcement
- CSRF protection
- Rate limiting
- Input sanitization
- XSS protection
- Parameter pollution prevention
- Secure cookies

## License

[MIT](LICENSE)
