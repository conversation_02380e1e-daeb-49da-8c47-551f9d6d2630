{"version": 3, "file": "parse.js", "names": ["parseNumber", "normalizeArguments", "arguments", "text", "options", "metadata", "_parseNumber"], "sources": ["../../source/legacy/parse.js"], "sourcesContent": ["import _parseNumber from '../parse.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function parseNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _parseNumber(text, options, metadata)\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEe,SAASA,WAAT,GAAuB;EACrC,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAO,IAAAC,iBAAA,EAAaH,IAAb,EAAmBC,OAAnB,EAA4BC,QAA5B,CAAP;AACA"}