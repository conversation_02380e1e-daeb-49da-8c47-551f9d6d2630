import { Pinecone } from '@pinecone-database/pinecone';
import { GoogleGenerativeAI } from "@google/generative-ai";
import { v4 as uuidv4 } from 'uuid';

let pineconeClient = null;
let pineconeIndex = null;
let geminiModel = null;

const initPinecone = () => {
  if (pineconeClient) {
    return pineconeClient;
  }

  const pineconeApiKey = process.env.PINECONE_API_KEY;
  if (!pineconeApiKey) {
    return null;
  }

  try {
    pineconeClient = new Pinecone({
      apiKey: pineconeApiKey
    });
    console.log('Pinecone client initialized');
    return pineconeClient;
  } catch (error) {
    console.error('Error initializing Pinecone');
    return null;
  }
};

const initGemini = () => {
  if (geminiModel) {
    return geminiModel;
  }

  const geminiApiKey = process.env.GEMINI_API_KEY;
  if (!geminiApiKey) {
    return null;
  }

  try {
    const genAI = new GoogleGenerativeAI(geminiApiKey);
    geminiModel = genAI.getGenerativeModel({ model: "text-embedding-004" });
    console.log('Gemini model initialized');
    return geminiModel;
  } catch (error) {
    console.error('Error initializing Gemini');
    return null;
  }
};

export class PineconeMemory {
  constructor() {
    this.pinecone = initPinecone();
    this.genAI = initGemini();
    this.indexName = process.env.PINECONE_INDEX_NAME || 'belilly-conversations';
    this.namespace = 'chat-history';
    this.dimension = 768;
    this.fallbackConversations = new Map();
    this.initialized = false;
  }

  /**
   * Initialize Pinecone index (optimized for Lambda)
   */
  async init() {
    if (this.initialized) {
      return;
    }

    if (!this.pinecone || !this.genAI) {
      console.log('Using fallback memory storage in Lambda');
      this.initialized = true;
      return;
    }

    try {
      // Reuse existing index connection if available
      if (pineconeIndex) {
        this.index = pineconeIndex;
        this.initialized = true;
        console.log('Reusing existing Pinecone index connection');
        return;
      }

      // Check if index exists
      const indexList = await this.pinecone.listIndexes();
      const indexExists = indexList.indexes?.some(index => index.name === this.indexName);

      if (!indexExists) {
        console.log(`Pinecone index ${this.indexName} does not exist. Using fallback memory.`);
        this.index = null;
        this.initialized = true;
        return;
      }

      // Get the index and cache it globally
      this.index = this.pinecone.index(this.indexName);
      pineconeIndex = this.index;
      this.initialized = true;
      console.log('Pinecone memory initialized successfully in Lambda');

    } catch (error) {
      console.error('Error initializing Pinecone index in Lambda');
      this.index = null;
      this.initialized = true;
    }
  }

  /**
   * Generate embedding using Gemini (with caching)
   */
  async generateEmbedding(text) {
    if (!this.genAI) {
      return null;
    }

    try {
      const result = await this.genAI.embedContent(text);
      return result.embedding.values;
    } catch (error) {
      console.error('Error generating embedding');
      return null;
    }
  }

  /**
   * Get or create conversation (optimized for Lambda)
   */
  async getOrCreateConversation(conversationId = null) {
    const id = conversationId || uuidv4();
    
    // For Lambda, we'll use a simple approach since conversations are stateless
    return {
      id,
      createdAt: new Date(),
      lastActivity: new Date()
    };
  }

  /**
   * Add message to conversation (optimized for Lambda)
   */
  async addMessage(conversationId, message) {
    if (!this.initialized) {
      await this.init();
    }

    const timestamp = new Date().toISOString();
    const id = `${conversationId}_${timestamp}_${message.role}`;

    // Try Pinecone first
    if (this.index && this.genAI) {
      try {
        const vector = await this.generateEmbedding(message.content);
        
        if (vector) {
          await this.index.upsert([{
            id,
            values: vector,
            metadata: {
              conversationId,
              role: message.role,
              content: message.content,
              timestamp
            }
          }]);
          
          console.log(`Message added to Pinecone: ${id}`);
          return;
        }
      } catch (error) {
        console.error('Error adding message to Pinecone');
      }
    }

    // Fallback to in-memory storage
    if (!this.fallbackConversations.has(conversationId)) {
      this.fallbackConversations.set(conversationId, []);
    }
    
    this.fallbackConversations.get(conversationId).push({
      ...message,
      timestamp,
      id
    });
    
    console.log(`Message added to fallback storage: ${id}`);
  }

  /**
   * Get conversation history (optimized for Lambda)
   */
  async getConversationHistory(conversationId, limit = 10) {
    if (!this.initialized) {
      await this.init();
    }

    // Try Pinecone first
    if (this.index) {
      try {
        const queryResponse = await this.index.query({
          filter: { conversationId },
          topK: limit * 2, // Get more to account for filtering
          includeMetadata: true
        });

        if (queryResponse.matches && queryResponse.matches.length > 0) {
          const messages = queryResponse.matches
            .map(match => ({
              role: match.metadata.role,
              content: match.metadata.content,
              timestamp: match.metadata.timestamp
            }))
            .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .slice(-limit);

          console.log(`Retrieved ${messages.length} messages from Pinecone`);
          return messages;
        }
      } catch (error) {
        console.error('Error retrieving conversation history from Pinecone');
      }
    }

    // Fallback to in-memory storage
    const fallbackMessages = this.fallbackConversations.get(conversationId) || [];
    const messages = fallbackMessages
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      .slice(-limit)
      .map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }));

    console.log(`Retrieved ${messages.length} messages from fallback storage`);
    return messages;
  }

  /**
   * Search similar messages (simplified for Lambda)
   */
  async searchSimilarMessages(query, limit = 5) {
    if (!this.initialized) {
      await this.init();
    }

    if (!this.index || !this.genAI) {
      console.log('Pinecone not available, skipping similarity search');
      return [];
    }

    try {
      const queryVector = await this.generateEmbedding(query);
      if (!queryVector) {
        return [];
      }

      const queryResponse = await this.index.query({
        vector: queryVector,
        topK: limit,
        includeMetadata: true
      });

      return queryResponse.matches.map(match => ({
        content: match.metadata.content,
        role: match.metadata.role,
        conversationId: match.metadata.conversationId,
        similarity: match.score
      }));
    } catch (error) {
      console.error('Error searching similar messages');
      return [];
    }
  }

  /**
   * Clear conversation (for Lambda cleanup)
   */
  clearConversation(conversationId) {
    this.fallbackConversations.delete(conversationId);
  }

  /**
   * Get memory stats (for monitoring)
   */
  getStats() {
    return {
      pineconeAvailable: !!this.index,
      geminiAvailable: !!this.genAI,
      fallbackConversations: this.fallbackConversations.size,
      initialized: this.initialized
    };
  }
}

export default PineconeMemory;
