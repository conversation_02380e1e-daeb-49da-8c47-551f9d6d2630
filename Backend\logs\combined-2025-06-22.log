{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 00:02:51.791"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 00:02:51.840"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 00:02:51.932"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 00:07:17.761"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 00:07:17.767"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 00:07:17.768"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 00:07:17.769"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.824"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.831"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.847"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 00:07:17.853"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 00:07:18.193"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 00:07:42.414"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 00:10:38.806"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 00:10:38.812"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 00:10:38.857"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:14:27.844"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:14:27.853"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:14:27.854"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:14:27.855"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.915"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.926"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.948"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:14:27.952"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:14:28.171"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:17:51.569"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:21:11.223"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:21:11.829"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:21:12.479"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:21:12.601"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:22:03.315"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:22:03.335"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:22:03.347"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:22:03.416"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.535"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.549"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.630"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:22:03.678"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:22:04.286"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:23:52.078"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:27:20.330"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:27:20.355"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 12:27:20.458"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:27:30.862"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:27:30.867"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:27:30.868"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:27:30.870"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.915"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.922"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.936"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:27:30.942"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:27:31.079"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:27:51.706"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:43:15.527"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:43:15.582"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 12:43:15.747"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 13:55:13.447"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 13:55:13.453"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 13:55:13.454"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 13:55:13.455"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.531"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.539"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.559"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 13:55:13.574"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 13:55:13.711"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 13:57:05.557"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 13:57:05.567"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 13:57:05.621"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 13:57:19.808"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 13:57:19.813"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 13:57:19.814"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 13:57:19.815"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.844"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.852"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.883"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 13:57:19.885"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 13:57:19.988"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 13:57:46.639"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:13:58.944"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:13:58.971"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:13:59.079"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:14:10.988"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:14:10.994"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:14:10.995"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:14:10.998"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.041"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.050"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.062"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:14:11.076"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:14:11.355"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:14:31.257"}
{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:14:31.997"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:17:02.214"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:17:02.226"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:17:02.298"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:17:18.404"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:17:18.415"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:17:18.417"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:17:18.418"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.491"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.501"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.522"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:17:18.543"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:17:18.868"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:18:38.549"}
{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:18:39.143"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:38:46.821"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:38:47.022"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:38:47.231"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:38:47.260"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:38:50.063"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:39:17.646"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:39:17.655"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:39:17.656"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:39:17.657"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.777"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.785"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.799"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:39:17.810"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:39:18.039"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:39:31.178"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:16:27.798"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:16:27.975"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:16:28.157"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:16:28.168"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:16:30.487"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:20:42.824"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:20:42.831"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:20:42.834"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:20:42.838"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.891"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.919"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.938"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:20:42.941"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:20:43.103"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:35:56.234"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:35:56.533"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:35:56.827"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:35:56.829"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:36:30.937"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:36:30.944"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:36:30.945"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:36:30.946"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.003"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.015"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.050"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:36:31.051"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:36:31.211"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 15:36:58.317"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:37:35.147"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:37:35.159"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:37:35.215"}
