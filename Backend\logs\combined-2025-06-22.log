{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 00:02:51.791"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 00:02:51.840"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 00:02:51.932"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 00:07:17.761"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 00:07:17.767"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 00:07:17.768"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 00:07:17.769"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.824"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.831"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 00:07:17.847"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 00:07:17.853"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 00:07:18.193"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 00:07:42.414"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 00:10:38.806"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 00:10:38.812"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 00:10:38.857"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:14:27.844"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:14:27.853"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:14:27.854"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:14:27.855"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.915"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.926"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:14:27.948"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:14:27.952"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:14:28.171"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:17:51.569"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:21:11.223"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:21:11.829"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:21:12.479"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:21:12.601"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:22:03.315"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:22:03.335"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:22:03.347"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:22:03.416"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.535"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.549"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:22:03.630"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:22:03.678"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:22:04.286"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:23:52.078"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:27:20.330"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:27:20.355"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 12:27:20.458"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 12:27:30.862"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 12:27:30.867"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 12:27:30.868"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 12:27:30.870"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.915"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.922"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 12:27:30.936"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 12:27:30.942"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 12:27:31.079"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 12:27:51.706"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 12:43:15.527"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 12:43:15.582"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 12:43:15.747"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 13:55:13.447"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 13:55:13.453"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 13:55:13.454"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 13:55:13.455"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.531"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.539"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 13:55:13.559"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 13:55:13.574"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 13:55:13.711"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 13:57:05.557"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 13:57:05.567"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 13:57:05.621"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 13:57:19.808"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 13:57:19.813"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 13:57:19.814"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 13:57:19.815"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.844"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.852"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 13:57:19.883"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 13:57:19.885"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 13:57:19.988"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 13:57:46.639"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:13:58.944"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:13:58.971"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:13:59.079"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:14:10.988"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:14:10.994"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:14:10.995"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:14:10.998"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.041"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.050"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:14:11.062"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:14:11.076"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:14:11.355"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:14:31.257"}
{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:14:31.997"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:17:02.214"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:17:02.226"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:17:02.298"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:17:18.404"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:17:18.415"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:17:18.417"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:17:18.418"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.491"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.501"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:17:18.522"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:17:18.543"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:17:18.868"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:18:38.549"}
{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:18:39.143"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:38:46.821"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:38:47.022"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 14:38:47.231"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 14:38:47.260"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 14:38:50.063"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 14:39:17.646"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 14:39:17.655"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 14:39:17.656"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 14:39:17.657"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.777"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.785"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 14:39:17.799"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 14:39:17.810"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 14:39:18.039"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 14:39:31.178"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:16:27.798"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:16:27.975"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:16:28.157"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:16:28.168"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:16:30.487"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:20:42.824"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:20:42.831"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:20:42.834"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:20:42.838"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.891"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.919"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:20:42.938"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:20:42.941"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:20:43.103"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:35:56.234"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:35:56.533"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:35:56.827"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:35:56.829"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:36:30.937"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:36:30.944"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:36:30.945"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:36:30.946"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.003"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.015"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:36:31.050"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:36:31.051"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:36:31.211"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 15:36:58.317"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:37:35.147"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:37:35.159"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:37:35.215"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:37:57.315"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:37:57.322"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:37:57.324"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:37:57.333"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:37:57.418"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:37:57.440"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:37:57.456"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:37:57.464"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:37:57.677"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 15:40:34.816"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:48:17.071"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:48:17.941"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:48:19.469"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:48:20.069"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:49:03.852"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:49:03.864"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:49:03.867"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:49:03.870"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:49:03.940"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:49:03.952"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:49:03.963"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:49:03.966"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:49:04.271"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:50:02.301"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:50:02.472"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:50:02.490"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:50:02.494"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:50:02.606"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:50:02.643"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:50:02.673"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:50:02.678"}
{"environment":"production","error":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","level":"error","message":"UNCAUGHT EXCEPTION","service":"belilly-api","timestamp":"2025-06-22 15:50:03.362"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:50:30.204"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:50:30.217"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:50:30.343"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 15:51:52.398"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 15:51:52.444"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 15:51:52.498"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 15:52:30.175"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 15:52:30.180"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 15:52:30.182"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 15:52:30.183"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 15:52:30.276"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 15:52:30.303"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 15:52:30.320"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 15:52:30.328"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 15:52:30.518"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 16:08:00.911"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:09:58.277"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:09:58.298"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 16:09:58.426"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 16:10:17.116"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 16:10:17.120"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 16:10:17.121"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 16:10:17.122"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 16:10:17.190"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 16:10:17.200"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 16:10:17.391"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 16:10:17.557"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 16:10:17.732"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 16:10:45.883"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:40:38.862"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:40:39.065"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:40:39.231"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:40:39.234"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 16:40:58.423"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 16:40:58.429"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 16:40:58.431"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 16:40:58.432"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 16:40:58.472"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 16:40:58.479"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 16:40:58.514"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 16:40:58.524"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 16:40:58.766"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:41:30.099"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:41:30.111"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 16:41:30.272"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 16:41:52.597"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 16:41:52.601"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 16:41:52.602"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 16:41:52.603"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 16:41:52.656"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 16:41:52.688"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 16:41:52.701"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 16:41:52.716"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 16:41:52.899"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:50:51.052"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:50:51.257"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:50:51.379"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:50:51.382"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 16:50:51.474"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 16:51:03.767"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 16:51:03.771"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 16:51:03.773"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 16:51:03.773"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 16:51:03.828"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 16:51:03.834"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 16:51:03.863"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 16:51:03.864"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 16:51:04.058"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 16:52:53.241"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 16:52:53.275"}
{"environment":"production","level":"info","message":"Database connections closed","service":"belilly-api","timestamp":"2025-06-22 16:52:53.319"}
{"environment":"production","level":"info","message":"Initializing Redis client...","service":"belilly-api","timestamp":"2025-06-22 16:53:02.717"}
{"environment":"production","level":"warn","message":"Redis host not configured. Redis features will be disabled.","service":"belilly-api","timestamp":"2025-06-22 16:53:02.721"}
{"environment":"production","level":"warn","message":"Redis client initialization failed or disabled. Using in-memory fallbacks.","service":"belilly-api","timestamp":"2025-06-22 16:53:02.722"}
{"environment":"production","level":"info","message":"Connecting to databases...","service":"belilly-api","timestamp":"2025-06-22 16:53:02.723"}
{"environment":"production","level":"info","message":"MongoDB Connection 1 established to database: customer_db","service":"belilly-api","timestamp":"2025-06-22 16:53:02.789"}
{"environment":"production","level":"info","message":"MongoDB Connection 2 established to database: products_db","service":"belilly-api","timestamp":"2025-06-22 16:53:02.803"}
{"environment":"production","level":"info","message":"MongoDB Connection 3 established to database: purchases_db","service":"belilly-api","timestamp":"2025-06-22 16:53:02.809"}
{"environment":"production","level":"info","message":"Database connections established successfully","service":"belilly-api","timestamp":"2025-06-22 16:53:02.810"}
{"environment":"production","level":"info","message":"HTTP server running on http://localhost:3000 (behind Nginx reverse proxy)","service":"belilly-api","timestamp":"2025-06-22 16:53:02.957"}
{"environment":"production","level":"info","message":"Inserted 1 documents into PurchasesOnline","service":"belilly-api","timestamp":"2025-06-22 16:58:02.041"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 17:09:25.172"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 17:09:25.256"}
{"environment":"production","level":"info","message":"Received shutdown signal, closing server and database connections...","service":"belilly-api","timestamp":"2025-06-22 17:09:25.398"}
{"environment":"production","level":"info","message":"HTTP server closed","service":"belilly-api","timestamp":"2025-06-22 17:09:25.399"}
