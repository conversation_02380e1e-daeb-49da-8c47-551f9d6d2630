# PhonePe SDK Migration Guide

## Overview

This document outlines the migration from manual PhonePe API integration to the official PhonePe Node.js SDK while maintaining full backward compatibility.

## Migration Summary

### What Changed
- **Replaced**: Manual API calls with official PhonePe SDK
- **Enhanced**: Error handling and security with SDK built-in features
- **Maintained**: 100% backward compatibility with existing API contracts
- **Removed**: Old manual API implementation and unused code
- **Cleaned**: Environment variables to use only SDK configuration

### What Stayed the Same
- All existing API endpoints continue to work
- Request/response formats remain unchanged
- Payment flow structure (initiate → redirect → callback → verify)

## Installation

### Automatic Installation
```bash
cd Backend
node scripts/install-phonepe-sdk.js
```

### Manual Installation
```bash
npm install https://phonepe.mycloudrepo.io/public/repositories/phonepe-pg-sdk-node/releases/v2/phonepe-pg-sdk-node.tgz
```

## Environment Configuration

### PhonePe SDK Configuration
Add these to your `.env` file:

```env
# PhonePe SDK Configuration
PHONEPE_CLIENT_ID=PGTESTPAYUAT86
PHONEPE_CLIENT_SECRET=96434309-7796-489d-8924-ab56988a6076
PHONEPE_CLIENT_VERSION=1
PHONEPE_ENV=SANDBOX
```

**Note**: Old environment variables (MERCHANT_ID_UAT, SALT_KEY_UAT, etc.) have been removed in favor of the clean SDK configuration.

## Architecture

### Clean SDK Implementation

The payment service now uses a **clean SDK-only implementation**:

1. **Official PhonePe SDK**: All payment operations use the official SDK
2. **No Fallback**: Simplified architecture with single implementation path
3. **Clean Configuration**: Only SDK environment variables required

```javascript
// Clean SDK Implementation
const request = StandardCheckoutPayRequest.builder()
  .merchantOrderId(transactionId)
  .amount(Number(price) * 100)
  .redirectUrl(PHONEPE_CONFIG.BASE_URL + PHONEPE_CONFIG.REDIRECT_URL)
  .build();

const response = await phonePeClient.pay(request);
```

### Benefits

- **Simplified Architecture**: Single implementation path reduces complexity
- **Enhanced Security**: SDK provides built-in signature verification
- **Better Error Handling**: SDK offers structured error responses
- **Future-Proof**: Ready for PhonePe SDK updates and new features
- **Cleaner Code**: Removed all manual API implementation code

## API Endpoints

All existing endpoints remain unchanged:

### Payment Initiation
```javascript
POST /v1/purchases/online
{
  "phone": "**********",
  "price": 1000,
  "transactionId": "TXN123456"
}
```

### Payment Verification
```javascript
GET /v1/redirect/purchases/status?transactionId=TXN123456
```

### Health Check (New)
```javascript
GET /health/payment
```

## Health Monitoring

### Payment Service Health Check
```bash
curl http://localhost:3000/health/payment
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "sdk": {
    "sdkAvailable": true,
    "clientInitialized": true,
    "environment": "SANDBOX",
    "clientId": "PGTESTPAYUAT86",
    "fallbackMode": false
  },
  "configuration": {
    "environment": "SANDBOX",
    "apiStatus": "UAT",
    "baseUrl": "http://localhost:3000"
  }
}
```

### System Health Check
```bash
curl http://localhost:3000/health/detailed
```

## Error Handling

### SDK Error Handling
The SDK provides structured error responses:

```javascript
try {
  const response = await phonePeClient.pay(paymentRequest);
} catch (error) {
  // SDK-specific error handling
  console.error("SDK Error:", error.code, error.message);
  // Automatic fallback to manual implementation
}
```

### Fallback Error Handling
If SDK fails, the service automatically falls back to manual implementation:

```javascript
// If SDK fails, try fallback
console.log('Falling back to manual implementation');
return await initiatePaymentFallback({ phone, price, transactionId });
```

## Security Enhancements

### Signature Verification
The SDK provides enhanced signature verification:

```javascript
export function verifyCallback({ response, checksum }) {
  // Use SDK verification if available
  if (phonePeClient && phonePeClient.verifySignature) {
    return phonePeClient.verifySignature(response, checksum);
  }
  
  // Fallback to manual verification
  return manualSignatureVerification(response, checksum);
}
```

### Environment-Based Security
- **SANDBOX**: For testing and development
- **PRODUCTION**: For live transactions with enhanced security

## Testing

### Test SDK Installation
```bash
node -e "import('phonepe-pg-sdk-node').then(() => console.log('✅ SDK Available')).catch(() => console.log('❌ SDK Not Available'))"
```

### Test Payment Flow
1. **Initiate Payment**: Use existing frontend flow
2. **Check Health**: `GET /health/payment`
3. **Verify Logs**: Check console for SDK vs fallback usage

### Test Fallback
To test fallback mechanism:
1. Temporarily rename the SDK package
2. Restart the server
3. Verify payments still work with fallback

## Troubleshooting

### Common Issues

#### 1. SDK Import Error
```
Error: Cannot find module 'phonepe-pg-sdk-node'
```

**Solution**: Run the installation script:
```bash
node scripts/install-phonepe-sdk.js
```

#### 2. SDK Initialization Failed
```
Failed to initialize PhonePe SDK: Invalid configuration
```

**Solution**: Check environment variables:
```bash
# Verify SDK configuration
curl http://localhost:3000/health/payment
```

#### 3. Fallback Mode Active
```json
{
  "fallbackMode": true
}
```

**Solution**: This is normal if SDK is not available. Payments will still work.

### Debug Mode

Enable debug logging:
```env
NODE_ENV=development
```

Check logs for:
- `PhonePe SDK initialized successfully`
- `Initiating payment with PhonePe SDK`
- `Falling back to manual implementation`

## Migration Checklist

- [ ] Install PhonePe SDK package
- [ ] Update environment variables (optional)
- [ ] Test payment initiation
- [ ] Test payment verification
- [ ] Test callback handling
- [ ] Verify health endpoints
- [ ] Test fallback mechanism
- [ ] Update monitoring/alerting
- [ ] Deploy to staging
- [ ] Deploy to production

## Production Deployment

### Environment Variables
Update production environment:

```env
PHONEPE_ENV=PRODUCTION
PHONEPE_CLIENT_ID=YOUR_LIVE_MERCHANT_ID
PHONEPE_CLIENT_SECRET=YOUR_LIVE_SALT_KEY
API_STATUS=LIVE
```

### Monitoring
Monitor these metrics:
- SDK availability rate
- Fallback usage rate
- Payment success rate
- Error rates by implementation type

### Rollback Plan
If issues occur:
1. The fallback mechanism ensures continued operation
2. No code changes needed for rollback
3. Simply remove SDK package if necessary

## Support

### Health Endpoints
- `/health` - General system health
- `/health/payment` - Payment service specific health
- `/health/detailed` - Comprehensive system health

### Logging
All payment operations are logged with:
- Implementation type (SDK vs fallback)
- Transaction IDs
- Success/failure status
- Error details

### Contact
For issues related to:
- **SDK**: Check PhonePe documentation
- **Integration**: Review this migration guide
- **Fallback**: Existing manual implementation continues to work
