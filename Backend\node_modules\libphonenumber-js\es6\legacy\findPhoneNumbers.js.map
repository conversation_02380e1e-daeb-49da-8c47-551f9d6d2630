{"version": 3, "file": "findPhoneNumbers.js", "names": ["_findPhoneNumbers", "searchPhoneNumbers", "_searchPhoneNumbers", "normalizeArguments", "findPhoneNumbers", "arguments", "text", "options", "metadata"], "sources": ["../../source/legacy/findPhoneNumbers.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _findPhoneNumbers(text, options, metadata)\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _searchPhoneNumbers(text, options, metadata)\r\n}"], "mappings": "AAAA;AACA;AAEA,OAAOA,iBAAP,IAA4BC,kBAAkB,IAAIC,mBAAlD,QAA6E,4CAA7E;AACA,OAAOC,kBAAP,MAA+B,0BAA/B;AAEA,eAAe,SAASC,gBAAT,GACf;EACC,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAOR,iBAAiB,CAACM,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAxB;AACA;AAED;AACA;AACA;;AACA,OAAO,SAASP,kBAAT,GACP;EACC,2BAAoCE,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,wBAAQA,IAAR;EAAA,IAAcC,OAAd,wBAAcA,OAAd;EAAA,IAAuBC,QAAvB,wBAAuBA,QAAvB;;EACA,OAAON,mBAAmB,CAACI,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAA1B;AACA"}