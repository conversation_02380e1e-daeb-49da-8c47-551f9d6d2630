// toggleApi.js

import axios from 'axios';
import { buildApiUrl } from '../../utils/apiConfig';

export const toggleWishlistApi = async ({ product_id, name, color, price, brand, imageurl }) => {
  return axios.post(
    buildApiUrl('product/wishlist'),
    { product_id, name, brand, color, price, imageurl },
    {
      headers: { "Content-Type": "application/json" },
      withCredentials: true // This ensures cookies are sent with the request
    }
  );
};

export const toggleCartApi = async ({ product_id, name, color, price, brand, imageurl }) => {
  return axios.post(
    buildApiUrl('product/cart'),
    { product_id, name: name, brand: brand, color: color, price: price, imageurl: imageurl },
    {
      headers: { "Content-Type": "application/json" },
      withCredentials: true // This ensures cookies are sent with the request
    }
  );
};
