{"version": 3, "file": "getNumberType.test.js", "names": ["metadata", "type", "<PERSON><PERSON><PERSON>", "_getNumberType", "getNumberType", "parameters", "push", "apply", "describe", "it", "should", "equal", "thrower", "phone", "country", "something"], "sources": ["../../source/legacy/getNumberType.test.js"], "sourcesContent": ["import metadata from '../../metadata.max.json' assert { type: 'json' }\r\nimport Metadata from '../metadata.js'\r\nimport _getNumberType from './getNumberType.js'\r\n\r\nfunction getNumberType(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _getNumberType.apply(this, parameters)\r\n}\r\n\r\ndescribe('getNumberType', () => {\r\n\tit('should infer phone number type MOBILE', () => {\r\n\t\tgetNumberType('9150000000', 'RU').should.equal('MOBILE')\r\n\t\tgetNumberType('7912345678', 'GB').should.equal('MOBILE')\r\n\t\tgetNumberType('51234567', 'EE').should.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should infer phone number types', () =>  {\r\n\t\tgetNumberType('88005553535', 'RU').should.equal('TOLL_FREE')\r\n\t\tgetNumberType('8005553535', 'RU').should.equal('TOLL_FREE')\r\n\t\tgetNumberType('4957777777', 'RU').should.equal('FIXED_LINE')\r\n\t\tgetNumberType('8030000000', 'RU').should.equal('PREMIUM_RATE')\r\n\r\n\t\tgetNumberType('2133734253', 'US').should.equal('FIXED_LINE_OR_MOBILE')\r\n\t\tgetNumberType('5002345678', 'US').should.equal('PERSONAL_NUMBER')\r\n\t})\r\n\r\n\tit('should work when no country is passed', () => {\r\n\t\tgetNumberType('+79150000000').should.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should return FIXED_LINE_OR_MOBILE when there is ambiguity', () => {\r\n\t\t// (no such country in the metadata, therefore no unit test for this `if`)\r\n\t})\r\n\r\n\tit('should work in edge cases', function() {\r\n\t\tlet thrower\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => _getNumberType({ phone: '+78005553535' })\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Parsed phone number\r\n\t\tgetNumberType({ phone: '8005553535', country: 'RU' }).should.equal('TOLL_FREE')\r\n\r\n\t\t// Invalid phone number\r\n\t\ttype(getNumberType('123', 'RU')).should.equal('undefined')\r\n\r\n\t\t// Invalid country\r\n\t\tthrower = () => getNumberType({ phone: '8005553535', country: 'RUS' })\r\n\t\tthrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => getNumberType(89150000000, 'RU')\r\n\t\tthrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// When `options` argument is passed.\r\n\t\tgetNumberType('8005553535', 'RU', {}).should.equal('TOLL_FREE')\r\n\t\tgetNumberType('+78005553535', {}).should.equal('TOLL_FREE')\r\n\t\tgetNumberType({ phone: '8005553535', country: 'RU' }, {}).should.equal('TOLL_FREE')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;AAAA,OAAOA,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,QAAP,MAAqB,gBAArB;AACA,OAAOC,cAAP,MAA2B,oBAA3B;;AAEA,SAASC,aAAT,GAAsC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACrCA,UAAU,CAACC,IAAX,CAAgBN,QAAhB;EACA,OAAOG,cAAc,CAACI,KAAf,CAAqB,IAArB,EAA2BF,UAA3B,CAAP;AACA;;AAEDG,QAAQ,CAAC,eAAD,EAAkB,YAAM;EAC/BC,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjDL,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,QAA/C;IACAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,QAA/C;IACAP,aAAa,CAAC,UAAD,EAAa,IAAb,CAAb,CAAgCM,MAAhC,CAAuCC,KAAvC,CAA6C,QAA7C;EACA,CAJC,CAAF;EAMAF,EAAE,CAAC,iCAAD,EAAoC,YAAO;IAC5CL,aAAa,CAAC,aAAD,EAAgB,IAAhB,CAAb,CAAmCM,MAAnC,CAA0CC,KAA1C,CAAgD,WAAhD;IACAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,WAA/C;IACAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,YAA/C;IACAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,cAA/C;IAEAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,sBAA/C;IACAP,aAAa,CAAC,YAAD,EAAe,IAAf,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,iBAA/C;EACA,CARC,CAAF;EAUAF,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjDL,aAAa,CAAC,cAAD,CAAb,CAA8BM,MAA9B,CAAqCC,KAArC,CAA2C,QAA3C;EACA,CAFC,CAAF;EAIAF,EAAE,CAAC,4DAAD,EAA+D,YAAM,CACtE;EACA,CAFC,CAAF;EAIAA,EAAE,CAAC,2BAAD,EAA8B,YAAW;IAC1C,IAAIG,OAAJ,CAD0C,CAG1C;IACA;IACA;IAEA;;IACAR,aAAa,CAAC;MAAES,KAAK,EAAE,YAAT;MAAuBC,OAAO,EAAE;IAAhC,CAAD,CAAb,CAAsDJ,MAAtD,CAA6DC,KAA7D,CAAmE,WAAnE,EAR0C,CAU1C;;IACAV,IAAI,CAACG,aAAa,CAAC,KAAD,EAAQ,IAAR,CAAd,CAAJ,CAAiCM,MAAjC,CAAwCC,KAAxC,CAA8C,WAA9C,EAX0C,CAa1C;;IACAC,OAAO,GAAG;MAAA,OAAMR,aAAa,CAAC;QAAES,KAAK,EAAE,YAAT;QAAuBC,OAAO,EAAE;MAAhC,CAAD,CAAnB;IAAA,CAAV;;IACAF,OAAO,CAACF,MAAR,UAAqB,iBAArB,EAf0C,CAiB1C;;IACAE,OAAO,GAAG;MAAA,OAAMR,aAAa,CAAC,WAAD,EAAc,IAAd,CAAnB;IAAA,CAAV;;IACAQ,OAAO,CAACF,MAAR,UAAqB,oFAArB,EAnB0C,CAqB1C;;IACAN,aAAa,CAAC,YAAD,EAAe,IAAf,EAAqB,EAArB,CAAb,CAAsCM,MAAtC,CAA6CC,KAA7C,CAAmD,WAAnD;IACAP,aAAa,CAAC,cAAD,EAAiB,EAAjB,CAAb,CAAkCM,MAAlC,CAAyCC,KAAzC,CAA+C,WAA/C;IACAP,aAAa,CAAC;MAAES,KAAK,EAAE,YAAT;MAAuBC,OAAO,EAAE;IAAhC,CAAD,EAAyC,EAAzC,CAAb,CAA0DJ,MAA1D,CAAiEC,KAAjE,CAAuE,WAAvE;EACA,CAzBC,CAAF;AA0BA,CAnDO,CAAR;;AAqDA,SAASV,IAAT,CAAcc,SAAd,EAAyB;EACxB,eAAcA,SAAd;AACA"}