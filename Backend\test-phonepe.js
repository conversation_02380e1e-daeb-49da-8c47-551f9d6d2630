// test-phonepe.js
// Simple test to verify PhonePe configuration

import { config } from 'dotenv';
import { initiatePayment } from './service/paymentservice.js';

// Load environment variables
config();

async function testPhonePeConfig() {
  console.log('🧪 Testing PhonePe Configuration...\n');
  
  // Check environment variables
  console.log('📋 Environment Configuration:');
  console.log(`  API_STATUS: ${process.env.API_STATUS}`);
  console.log(`  MERCHANT_ID_UAT: ${process.env.MERCHANT_ID_UAT ? '✅ Set' : '❌ Missing'}`);
  console.log(`  SALT_KEY_UAT: ${process.env.SALT_KEY_UAT ? '✅ Set' : '❌ Missing'}`);
  console.log(`  SALT_INDEX: ${process.env.SALT_INDEX}`);
  console.log(`  UAT_URL_PAY: ${process.env.UAT_URL_PAY}`);
  console.log(`  BASE_URL: ${process.env.BASE_URL}`);
  console.log(`  REDIRECTURL: ${process.env.REDIRECTURL}\n`);
  
  // Test payment initiation with dummy data
  const testPayload = {
    phone: '9999999999',
    price: 100,
    transactionId: 'TEST-' + Date.now()
  };
  
  console.log('🚀 Testing Payment Initiation:');
  console.log(`  Test Payload: ${JSON.stringify(testPayload, null, 2)}\n`);
  
  try {
    const result = await initiatePayment(testPayload);
    console.log('✅ Payment initiation successful!');
    console.log('📄 Response:', JSON.stringify(result, null, 2));
    
    if (result?.data?.instrumentResponse?.redirectInfo?.url) {
      console.log('\n🔗 Redirect URL found:', result.data.instrumentResponse.redirectInfo.url);
    } else {
      console.log('\n⚠️  No redirect URL in response');
    }
    
  } catch (error) {
    console.log('❌ Payment initiation failed!');
    console.log('💥 Error:', error.message);
    
    if (error.message.includes('526')) {
      console.log('\n🔍 Status 526 typically indicates:');
      console.log('  - Invalid merchant credentials');
      console.log('  - Incorrect API endpoint');
      console.log('  - Malformed request payload');
      console.log('  - SSL/TLS certificate issues');
    }
  }
}

// Run the test
testPhonePeConfig()
  .then(() => {
    console.log('\n✨ Test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
