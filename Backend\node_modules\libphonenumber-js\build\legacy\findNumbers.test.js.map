{"version": 3, "file": "findNumbers.test.js", "names": ["describe", "it", "findNumbers", "metadata", "should", "deep", "equal", "phone", "country", "startsAt", "endsAt", "leniency", "ext", "phoneNumbers", "v2", "length", "number", "nationalNumber", "countryCallingCode", "thrower", "numbers", "defaultCountry", "possibleNumbers", "extended"], "sources": ["../../source/legacy/findNumbers.test.js"], "sourcesContent": ["import findNumbers from './findNumbers.js'\r\nimport metadata from '../../metadata.max.json' assert { type: 'json' }\r\n\r\ndescribe('findNumbers', () => {\r\n\tit('should find numbers', () => {\r\n\t\tfindNumbers('2133734253', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\tfindNumbers('(*************', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', 'US', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', 'US', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\tfindNumbers('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\tfindNumbers('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\tfindNumbers('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata).should.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should find numbers (v2)', () => {\r\n\t\tconst phoneNumbers = findNumbers('The number is +7 (800) 555-35-35 ext. 1234 and not (************* as written in the document.', 'US', { v2: true }, metadata)\r\n\r\n\t\tphoneNumbers.length.should.equal(2)\r\n\r\n\t\tphoneNumbers[0].startsAt.should.equal(14)\r\n\t\tphoneNumbers[0].endsAt.should.equal(42)\r\n\r\n\t\tphoneNumbers[0].number.number.should.equal('+78005553535')\r\n\t\tphoneNumbers[0].number.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumbers[0].number.country.should.equal('RU')\r\n\t\tphoneNumbers[0].number.countryCallingCode.should.equal('7')\r\n\t\tphoneNumbers[0].number.ext.should.equal('1234')\r\n\r\n\t\tphoneNumbers[1].startsAt.should.equal(51)\r\n\t\tphoneNumbers[1].endsAt.should.equal(65)\r\n\r\n\t\tphoneNumbers[1].number.number.should.equal('+12133734253')\r\n\t\tphoneNumbers[1].number.nationalNumber.should.equal('2133734253')\r\n\t\tphoneNumbers[1].number.country.should.equal('US')\r\n\t\tphoneNumbers[1].number.countryCallingCode.should.equal('1')\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\tfindNumbers('1111111111', 'US', metadata).should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\tfindNumbers('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\tfindNumbers('', metadata).should.deep.equal([])\r\n\r\n\t\t// // No country metadata for this `require` country code\r\n\t\t// thrower = () => findNumbers('123', 'ZZ', metadata)\r\n\t\t// thrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findNumbers(2141111111, 'US')\r\n\t\tthrower.should.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findNumbers('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// No metadata, no default country, no phone numbers.\r\n\t\tfindNumbers('').should.deep.equal([])\r\n\t})\r\n\r\n\tit('should find international numbers when passed a non-existent default country', () => {\r\n\t\tconst numbers = findNumbers('Phone: +7 (800) 555 35 35. National: 8 (800) 555-55-55', { defaultCountry: 'XX', v2: true }, metadata)\r\n\t\tnumbers.length.should.equal(1)\r\n\t\tnumbers[0].number.nationalNumber.should.equal('8005553535')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\tfindNumbers('2012-01-02 08:00', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\tfindNumbers('2012-01-02 08', 'US', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\tfindNumbers('213(3734253', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\tfindNumbers('2133734253a', 'US', metadata).should.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\tfindNumbers('The phone number is 231354125.', 'FR', metadata).should.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', { extended: true }, metadata)\r\n\t\tpossibleNumbers.length.should.equal(1)\r\n\t\tpossibleNumbers[0].country.should.equal('FR')\r\n\t\tpossibleNumbers[0].phone.should.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\tfindNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', metadata).should.deep.equal([])\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/merge_requests/4\r\n\tit('should return correct `startsAt` and `endsAt` when matching \"inner\" candidates in a could-be-a-candidate substring', () => {\r\n\t\tfindNumbers('39945926 77200596 16533084', 'ID', metadata)\r\n\t\t\t.should\r\n\t\t\t.deep\r\n\t\t\t.equal([{\r\n\t\t\t\tcountry: 'ID',\r\n\t\t\t\tphone: '77200596',\r\n\t\t\t\tstartsAt: 9,\r\n\t\t\t\tendsAt: 17\r\n\t\t\t}])\r\n\t})\r\n})\r\n"], "mappings": ";;AAAA;;AACA;;;;AAEAA,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,qBAAD,EAAwB,YAAM;IAC/B,IAAAC,uBAAA,EAAY,YAAZ,EAA0B,IAA1B,EAAgCC,uBAAhC,EAA0CC,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,CAAC;MAC5DC,KAAK,EAAM,YADiD;MAE5DC,OAAO,EAAI,IAFiD;MAG5DC,QAAQ,EAAG,CAHiD;MAI5DC,MAAM,EAAK;IAJiD,CAAD,CAA5D;IAOA,IAAAR,uBAAA,EAAY,gBAAZ,EAA8B,IAA9B,EAAoCC,uBAApC,EAA8CC,MAA9C,CAAqDC,IAArD,CAA0DC,KAA1D,CAAgE,CAAC;MAChEC,KAAK,EAAM,YADqD;MAEhEC,OAAO,EAAI,IAFqD;MAGhEC,QAAQ,EAAG,CAHqD;MAIhEC,MAAM,EAAK;IAJqD,CAAD,CAAhE;IAOA,IAAAR,uBAAA,EAAY,qFAAZ,EAAmG,IAAnG,EAAyGC,uBAAzG,EAAmHC,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,EAKlI;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CALkI,CAArI,EAf+B,CA2B/B;IACA;;IACA,IAAAR,uBAAA,EAAY,6HAAZ,EAA2I,IAA3I,EAAiJC,uBAAjJ,EAA2JC,MAA3J,CAAkKC,IAAlK,CAAuKC,KAAvK,CAA6K,CAAC;MAC7KC,KAAK,EAAM,YADkK;MAE7KC,OAAO,EAAI,IAFkK;MAG7KC,QAAQ,EAAG,EAHkK;MAI7KC,MAAM,EAAK;IAJkK,CAAD,EAK1K;MACFH,KAAK,EAAM,YADT;MAEFC,OAAO,EAAI,IAFT;MAGFC,QAAQ,EAAG,EAHT;MAIFC,MAAM,EAAK;IAJT,CAL0K,CAA7K,EA7B+B,CAyC/B;;IACA,IAAAR,uBAAA,EAAY,8DAAZ,EAA4EC,uBAA5E,EAAsFC,MAAtF,CAA6FC,IAA7F,CAAkGC,KAAlG,CAAwG,CAAC;MACxGC,KAAK,EAAM,YAD6F;MAExGC,OAAO,EAAI,IAF6F;MAGxGC,QAAQ,EAAG,EAH6F;MAIxGC,MAAM,EAAK;IAJ6F,CAAD,CAAxG,EA1C+B,CAiD/B;;IACA,IAAAR,uBAAA,EAAY,8DAAZ,EAA4E,IAA5E,EAAkF;MAAES,QAAQ,EAAE;IAAZ,CAAlF,EAAyGR,uBAAzG,EAAmHC,MAAnH,CAA0HC,IAA1H,CAA+HC,KAA/H,CAAqI,CAAC;MACrIC,KAAK,EAAM,YAD0H;MAErIC,OAAO,EAAI,IAF0H;MAGrIC,QAAQ,EAAG,EAH0H;MAIrIC,MAAM,EAAK;IAJ0H,CAAD,CAArI,EAlD+B,CAyD/B;;IACA,IAAAR,uBAAA,EAAY,8DAAZ,EAA4E;MAAES,QAAQ,EAAE;IAAZ,CAA5E,EAAmGR,uBAAnG,EAA6GC,MAA7G,CAAoHC,IAApH,CAAyHC,KAAzH,CAA+H,CAAC;MAC/HC,KAAK,EAAM,YADoH;MAE/HC,OAAO,EAAI,IAFoH;MAG/HC,QAAQ,EAAG,EAHoH;MAI/HC,MAAM,EAAK;IAJoH,CAAD,CAA/H,EA1D+B,CAiE/B;;IACA,IAAAR,uBAAA,EAAY,wDAAZ,EAAsE;MAAES,QAAQ,EAAE;IAAZ,CAAtE,EAA6FR,uBAA7F,EAAuGC,MAAvG,CAA8GC,IAA9G,CAAmHC,KAAnH,CAAyH,CAAC;MACzHC,KAAK,EAAM,YAD8G;MAEzHC,OAAO,EAAI,IAF8G;MAGzHC,QAAQ,EAAG,EAH8G;MAIzHC,MAAM,EAAK;IAJ8G,CAAD,CAAzH,EAlE+B,CAyE/B;;IACA,IAAAR,uBAAA,EAAY,sEAAZ,EAAoF;MAAES,QAAQ,EAAE;IAAZ,CAApF,EAA2GR,uBAA3G,EAAqHC,MAArH,CAA4HC,IAA5H,CAAiIC,KAAjI,CAAuI,CAAC;MACvIC,KAAK,EAAM,YAD4H;MAEvIC,OAAO,EAAI,IAF4H;MAGvII,GAAG,EAAQ,KAH4H;MAIvIH,QAAQ,EAAG,EAJ4H;MAKvIC,MAAM,EAAK;IAL4H,CAAD,CAAvI;EAOA,CAjFC,CAAF;EAmFAT,EAAE,CAAC,0BAAD,EAA6B,YAAM;IACpC,IAAMY,YAAY,GAAG,IAAAX,uBAAA,EAAY,+FAAZ,EAA6G,IAA7G,EAAmH;MAAEY,EAAE,EAAE;IAAN,CAAnH,EAAiIX,uBAAjI,CAArB;IAEAU,YAAY,CAACE,MAAb,CAAoBX,MAApB,CAA2BE,KAA3B,CAAiC,CAAjC;IAEAO,YAAY,CAAC,CAAD,CAAZ,CAAgBJ,QAAhB,CAAyBL,MAAzB,CAAgCE,KAAhC,CAAsC,EAAtC;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBH,MAAhB,CAAuBN,MAAvB,CAA8BE,KAA9B,CAAoC,EAApC;IAEAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBA,MAAvB,CAA8BZ,MAA9B,CAAqCE,KAArC,CAA2C,cAA3C;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBC,cAAvB,CAAsCb,MAAtC,CAA6CE,KAA7C,CAAmD,YAAnD;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBR,OAAvB,CAA+BJ,MAA/B,CAAsCE,KAAtC,CAA4C,IAA5C;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBE,kBAAvB,CAA0Cd,MAA1C,CAAiDE,KAAjD,CAAuD,GAAvD;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBJ,GAAvB,CAA2BR,MAA3B,CAAkCE,KAAlC,CAAwC,MAAxC;IAEAO,YAAY,CAAC,CAAD,CAAZ,CAAgBJ,QAAhB,CAAyBL,MAAzB,CAAgCE,KAAhC,CAAsC,EAAtC;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBH,MAAhB,CAAuBN,MAAvB,CAA8BE,KAA9B,CAAoC,EAApC;IAEAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBA,MAAvB,CAA8BZ,MAA9B,CAAqCE,KAArC,CAA2C,cAA3C;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBC,cAAvB,CAAsCb,MAAtC,CAA6CE,KAA7C,CAAmD,YAAnD;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBR,OAAvB,CAA+BJ,MAA/B,CAAsCE,KAAtC,CAA4C,IAA5C;IACAO,YAAY,CAAC,CAAD,CAAZ,CAAgBG,MAAhB,CAAuBE,kBAAvB,CAA0Cd,MAA1C,CAAiDE,KAAjD,CAAuD,GAAvD;EACA,CArBC,CAAF;EAuBAL,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C;IACA,IAAAC,uBAAA,EAAY,YAAZ,EAA0B,IAA1B,EAAgCC,uBAAhC,EAA0CC,MAA1C,CAAiDC,IAAjD,CAAsDC,KAAtD,CAA4D,EAA5D;EACA,CAHC,CAAF;EAKAL,EAAE,CAAC,iCAAD,EAAoC,YAAM;IAC3C;IACA,IAAAC,uBAAA,EAAY,sCAAZ,EAAoDC,uBAApD,EAA8DC,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,YAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF;EAMA,CARC,CAAF;EAUAT,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAIkB,OAAJ,CADqC,CAGrC;;IACA,IAAAjB,uBAAA,EAAY,EAAZ,EAAgBC,uBAAhB,EAA0BC,MAA1B,CAAiCC,IAAjC,CAAsCC,KAAtC,CAA4C,EAA5C,EAJqC,CAMrC;IACA;IACA;IAEA;;IACAa,OAAO,GAAG;MAAA,OAAM,IAAAjB,uBAAA,EAAY,UAAZ,EAAwB,IAAxB,CAAN;IAAA,CAAV;;IACAiB,OAAO,CAACf,MAAR,UAAqB,sCAArB,EAZqC,CAcrC;IACA;IACA;IAEA;;IACA,IAAAF,uBAAA,EAAY,EAAZ,EAAgBE,MAAhB,CAAuBC,IAAvB,CAA4BC,KAA5B,CAAkC,EAAlC;EACA,CApBC,CAAF;EAsBAL,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF,IAAMmB,OAAO,GAAG,IAAAlB,uBAAA,EAAY,wDAAZ,EAAsE;MAAEmB,cAAc,EAAE,IAAlB;MAAwBP,EAAE,EAAE;IAA5B,CAAtE,EAA0GX,uBAA1G,CAAhB;IACAiB,OAAO,CAACL,MAAR,CAAeX,MAAf,CAAsBE,KAAtB,CAA4B,CAA5B;IACAc,OAAO,CAAC,CAAD,CAAP,CAAWJ,MAAX,CAAkBC,cAAlB,CAAiCb,MAAjC,CAAwCE,KAAxC,CAA8C,YAA9C;EACA,CAJC,CAAF;EAMAL,EAAE,CAAC,2DAAD,EAA8D,YAAM;IACrE;IACA,IAAAC,uBAAA,EAAY,kBAAZ,EAAgC,IAAhC,EAAsCC,uBAAtC,EAAgDC,MAAhD,CAAuDC,IAAvD,CAA4DC,KAA5D,CAAkE,EAAlE,EAFqE,CAIrE;;IACA,IAAAJ,uBAAA,EAAY,eAAZ,EAA6B,IAA7B,EAAmCC,uBAAnC,EAA6CC,MAA7C,CAAoDC,IAApD,CAAyDC,KAAzD,CAA+D,CAAC;MAC/DE,OAAO,EAAI,IADoD;MAE/DD,KAAK,EAAM,YAFoD;MAG/DE,QAAQ,EAAG,CAHoD;MAI/DC,MAAM,EAAK;IAJoD,CAAD,CAA/D,EALqE,CAYrE;;IACA,IAAAR,uBAAA,EAAY,aAAZ,EAA2B,IAA3B,EAAiCC,uBAAjC,EAA2CC,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAbqE,CAerE;;IACA,IAAAJ,uBAAA,EAAY,aAAZ,EAA2B,IAA3B,EAAiCC,uBAAjC,EAA2CC,MAA3C,CAAkDC,IAAlD,CAAuDC,KAAvD,CAA6D,EAA7D,EAhBqE,CAkBrE;;IACA,IAAAJ,uBAAA,EAAY,gCAAZ,EAA8C,IAA9C,EAAoDC,uBAApD,EAA8DC,MAA9D,CAAqEC,IAArE,CAA0EC,KAA1E,CAAgF,CAAC;MAChFE,OAAO,EAAI,IADqE;MAEhFD,KAAK,EAAM,WAFqE;MAGhFE,QAAQ,EAAG,EAHqE;MAIhFC,MAAM,EAAK;IAJqE,CAAD,CAAhF,EAnBqE,CA0BrE;IACA;;IACA,IAAMY,eAAe,GAAG,IAAApB,uBAAA,EAAY,iDAAZ,EAA+D,IAA/D,EAAqE;MAAEqB,QAAQ,EAAE;IAAZ,CAArE,EAAyFpB,uBAAzF,CAAxB;IACAmB,eAAe,CAACP,MAAhB,CAAuBX,MAAvB,CAA8BE,KAA9B,CAAoC,CAApC;IACAgB,eAAe,CAAC,CAAD,CAAf,CAAmBd,OAAnB,CAA2BJ,MAA3B,CAAkCE,KAAlC,CAAwC,IAAxC;IACAgB,eAAe,CAAC,CAAD,CAAf,CAAmBf,KAAnB,CAAyBH,MAAzB,CAAgCE,KAAhC,CAAsC,WAAtC,EA/BqE,CAiCrE;IACA;;IACA,IAAAJ,uBAAA,EAAY,iDAAZ,EAA+D,IAA/D,EAAqEC,uBAArE,EAA+EC,MAA/E,CAAsFC,IAAtF,CAA2FC,KAA3F,CAAiG,EAAjG;EACA,CApCC,CAAF,CAtJ6B,CA4L7B;;EACAL,EAAE,CAAC,oHAAD,EAAuH,YAAM;IAC9H,IAAAC,uBAAA,EAAY,4BAAZ,EAA0C,IAA1C,EAAgDC,uBAAhD,EACEC,MADF,CAEEC,IAFF,CAGEC,KAHF,CAGQ,CAAC;MACPE,OAAO,EAAE,IADF;MAEPD,KAAK,EAAE,UAFA;MAGPE,QAAQ,EAAE,CAHH;MAIPC,MAAM,EAAE;IAJD,CAAD,CAHR;EASA,CAVC,CAAF;AAWA,CAxMO,CAAR"}