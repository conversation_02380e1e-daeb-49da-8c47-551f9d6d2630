{"version": 3, "file": "formatIncompletePhoneNumber.test.js", "names": ["formatIncompletePhoneNumber", "metadata", "type", "describe", "it", "result", "should", "equal", "defaultCountry", "defaultCallingCode"], "sources": ["../source/formatIncompletePhoneNumber.test.js"], "sourcesContent": ["import formatIncompletePhoneNumber from './formatIncompletePhoneNumber.js'\r\n\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('formatIncompletePhoneNumber', () => {\r\n\tit('should format parsed input value', () => {\r\n\t\tlet result\r\n\r\n\t\t// National input.\r\n\t\tformatIncompletePhoneNumber('880055535', 'RU', metadata).should.equal('8 (800) 555-35')\r\n\r\n\t\t// International input, no country.\r\n\t\tformatIncompletePhoneNumber('+780055535', null, metadata).should.equal('****** 555 35')\r\n\r\n\t\t// International input, no country argument.\r\n\t\tformatIncompletePhoneNumber('+780055535', metadata).should.equal('****** 555 35')\r\n\r\n\t\t// International input, with country.\r\n\t\tformatIncompletePhoneNumber('+780055535', 'RU', metadata).should.equal('****** 555 35')\r\n\t})\r\n\r\n\tit('should support an object argument', () => {\r\n\t\tformatIncompletePhoneNumber('880055535', { defaultCountry: 'RU' }, metadata).should.equal('8 (800) 555-35')\r\n\t\tformatIncompletePhoneNumber('880055535', { defaultCallingCode: '7' }, metadata).should.equal('8 (800) 555-35')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,2BAAP,MAAwC,kCAAxC;AAEA,OAAOC,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AAEAC,QAAQ,CAAC,6BAAD,EAAgC,YAAM;EAC7CC,EAAE,CAAC,kCAAD,EAAqC,YAAM;IAC5C,IAAIC,MAAJ,CAD4C,CAG5C;;IACAL,2BAA2B,CAAC,WAAD,EAAc,IAAd,EAAoBC,QAApB,CAA3B,CAAyDK,MAAzD,CAAgEC,KAAhE,CAAsE,gBAAtE,EAJ4C,CAM5C;;IACAP,2BAA2B,CAAC,YAAD,EAAe,IAAf,EAAqBC,QAArB,CAA3B,CAA0DK,MAA1D,CAAiEC,KAAjE,CAAuE,eAAvE,EAP4C,CAS5C;;IACAP,2BAA2B,CAAC,YAAD,EAAeC,QAAf,CAA3B,CAAoDK,MAApD,CAA2DC,KAA3D,CAAiE,eAAjE,EAV4C,CAY5C;;IACAP,2BAA2B,CAAC,YAAD,EAAe,IAAf,EAAqBC,QAArB,CAA3B,CAA0DK,MAA1D,CAAiEC,KAAjE,CAAuE,eAAvE;EACA,CAdC,CAAF;EAgBAH,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7CJ,2BAA2B,CAAC,WAAD,EAAc;MAAEQ,cAAc,EAAE;IAAlB,CAAd,EAAwCP,QAAxC,CAA3B,CAA6EK,MAA7E,CAAoFC,KAApF,CAA0F,gBAA1F;IACAP,2BAA2B,CAAC,WAAD,EAAc;MAAES,kBAAkB,EAAE;IAAtB,CAAd,EAA2CR,QAA3C,CAA3B,CAAgFK,MAAhF,CAAuFC,KAAvF,CAA6F,gBAA7F;EACA,CAHC,CAAF;AAIA,CArBO,CAAR"}