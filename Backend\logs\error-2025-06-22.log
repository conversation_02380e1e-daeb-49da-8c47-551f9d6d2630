{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:14:31.996"}
{"environment":"production","level":"error","message":"Error in secureJsonMiddleware: \"undefined\" is not valid JSON","service":"belilly-api","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at res.json (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/middleware/sanitization.js:205:45)\n    at processPurchase (file:///C:/Users/<USER>/Downloads/Belilly%20(mern)/Backend/controller/purchasecontroller.js:68:32)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-22 14:18:39.142"}
