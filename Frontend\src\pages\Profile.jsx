// Profile.jsx

import React, { useState, useEffect } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import { useNavigate, Link } from "react-router-dom";
import useProfile from "../hooks/useProfile";
import ChangePassword from "../components/ChangePassword";
import Login from "../components/Login";
import { buildApiUrl } from "../utils/apiConfig";

const Profile = () => {
  const navigate = useNavigate();
  const { profile: user, loading, error } = useProfile();

  // State to track if we're checking authentication
  const [checkingAuth, setCheckingAuth] = useState(true);

  // For login popup (if user is not logged in)
  const [showLoginPopup, setShowLoginPopup] = useState(false);

  // Reference to store the timeout
  const authTimeoutRef = React.useRef(null);

  // Initial setup - always show loader on mount
  React.useEffect(() => {
    // Always show loader initially
    setCheckingAuth(true);

    // Hide login popup while loading
    setShowLoginPopup(false);

    // Set a timeout to ensure we don't show the loader forever
    // in case user state never loads
    authTimeoutRef.current = setTimeout(() => {
      setCheckingAuth(false);

      // Only show login popup if user is not authenticated
      if (!user) {
        setShowLoginPopup(true);
      }
    }, 1000); // 1 second delay

    // Clean up timeout if component unmounts
    return () => {
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
      }
    };
  }, []);

  // Handle user state changes
  React.useEffect(() => {
    // If we have a user and we're still checking auth, we can stop checking
    if (user && checkingAuth) {
      // Clear any existing timeout
      if (authTimeoutRef.current) {
        clearTimeout(authTimeoutRef.current);
        authTimeoutRef.current = null;
      }

      // Hide loader and login popup
      setCheckingAuth(false);
      setShowLoginPopup(false);
    }
  }, [user, checkingAuth]);

  // Local state for edit mode and form data
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: "",
    phone: "",
    email: "",
    street: "",
    city: "",
    district: "",
    state: "",
    pincode: "",
    country: "India",
  });

  // For profile picture update
  const [showPictureModal, setShowPictureModal] = useState(false);
  const [pictureUrl, setPictureUrl] = useState("");

  // For delete-account modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  // For change password popup
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  // Update formData when user data is loaded
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || "",
        phone: user.phone || "",
        email: user.email || "",
        street: user.primaryAddress?.street || "",
        city: user.primaryAddress?.city || "",
        district: user.primaryAddress?.district || "",
        state: user.primaryAddress?.state || "",
        pincode: user.primaryAddress?.pincode || "",
        country: "India", // Always set to India regardless of what's in the database
      });
    }
  }, [user]);

  // If profile is loading or checking authentication, show a loading spinner
  if (loading || checkingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pink-50 via-white to-purple-50">
        <div className="p-8 bg-white rounded-2xl shadow-xl">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-pink-500 border-t-transparent"></div>
          <p className="mt-4 text-gray-600 font-medium">
            {checkingAuth ? "Loading your profile..." : "Loading profile data..."}
          </p>
        </div>
      </div>
    );
  }

  // If there's an error, show the error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="p-6 bg-white rounded-xl shadow-lg border border-red-100">
          <div className="flex items-center space-x-3">
            <svg
              className="h-12 w-12 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <p className="text-red-600 font-medium text-lg">{error}</p>
          </div>
        </div>
      </div>
    );
  }



  // Handlers
  const handleLogout = () => {
    // Set a flag in sessionStorage to prevent auto-login
    sessionStorage.setItem('user_logged_out', 'true');
    // Clear cookies using multiple approaches
    // 1. Standard removal
    Cookies.remove('token', { path: '/' });
    Cookies.remove('_csrf', { path: '/' });
    Cookies.remove('XSRF-TOKEN', { path: '/' });
    Cookies.remove('sessionId', { path: '/' });

    // 2. Set to empty with past expiration
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = '_csrf=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'XSRF-TOKEN=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'sessionId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

    // 3. Try with secure and SameSite flags
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; SameSite=Strict;';
    document.cookie = '_csrf=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; SameSite=Strict;';
    document.cookie = 'XSRF-TOKEN=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; SameSite=Strict;';
    document.cookie = 'sessionId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; SameSite=Strict;';

    setShowLoginPopup(true);

    // Force a page reload to ensure all state is cleared
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  const handleEditProfile = () => {
    setIsEditing(true);
  };

  const handleUpdateProfile = async () => {
    try {
      const updateData = {
        username: formData.username,
        phone: formData.phone,
        picture: user.picture || "",
        // Address fields
        street: formData.street,
        city: formData.city,
        district: formData.district,
        state: formData.state,
        country: "India", // Always set to India
        pincode: formData.pincode
      };

      await axios.put(buildApiUrl("profile"), updateData, {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true, // This ensures cookies are sent with the request
      });
      console.log("Profile updated with:", updateData);
      alert("Profile updated successfully.");
      setIsEditing(false);
      // Optionally, refresh profile data here
    } catch (err) {
      console.error("Failed to update profile:", err);
      alert("Something went wrong while updating your profile.");
    }
  };

  const handleDeleteAccountClick = () => {
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
  };

  const handleConfirmDelete = async () => {
    try {
      await axios.get(buildApiUrl("profile/terminate"), {
        withCredentials: true, // This ensures cookies are sent with the request
      });
      console.log("Account deletion successful.");
      alert("Account terminated successfully.");
      Cookies.remove("token");
      setShowLoginPopup(true);
    } catch (err) {
      console.error("Failed to delete account:", err);
      alert("Something went wrong while deleting the account.");
    }
  };

  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleUpdatePicture = async () => {
    try {
      const updateData = {
        username: user.username || "",
        phone: user.phone || "",
        picture: pictureUrl,
        // Keep existing address fields
        street: user.primaryAddress?.street || "",
        city: user.primaryAddress?.city || "",
        district: user.primaryAddress?.district || "",
        state: user.primaryAddress?.state || "",
        country: "India", // Always set to India
        pincode: user.primaryAddress?.pincode || ""
      };

      await axios.put(buildApiUrl("profile"), updateData, {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true, // This ensures cookies are sent with the request
      });
      console.log("Profile picture updated to:", pictureUrl);
      alert("Profile picture updated successfully.");
      setShowPictureModal(false);
      // Reload the page to see the updated picture
      window.location.reload();
    } catch (err) {
      console.error("Failed to update profile picture:", err);
      alert("Something went wrong while updating your profile picture.");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50 py-8 px-4 md:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Profile Header */}
        <div className="bg-white rounded-3xl shadow-xl p-6 mb-8">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-8">
            <div className="relative group">
              <div className="w-32 h-32 rounded-full overflow-hidden ring-4 ring-pink-100">
                <img
                  src={
                    user.picture ||
                    "https://mrwallpaper.com/images/hd/cute-anime-girl-profile-picture-9dlupjr81mez8wqq.jpg"
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = "https://mrwallpaper.com/images/hd/cute-anime-girl-profile-picture-9dlupjr81mez8wqq.jpg";
                  }}
                />
              </div>
              <button
                onClick={() => {
                  setPictureUrl(user.picture || "");
                  setShowPictureModal(true);
                }}
                className="absolute bottom-0 right-0 bg-pink-500 text-white p-2 rounded-full shadow-lg hover:bg-pink-600 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                {formData.name}
              </h1>
              <p className="text-gray-500 mb-4">{formData.email}</p>
              <div className="flex flex-wrap gap-4 justify-center md:justify-start">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="px-10 py-2 bg-pink-500 text-white rounded-full hover:bg-pink-600 transition-colors focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2"
                >
                  {isEditing ? "Cancel" : "Edit"}
                </button>
                <button
                  onClick={() => setShowChangePasswordModal(true)}
                  className="px-6 py-2 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2"
                >
                  Change Password
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <div className="bg-white rounded-3xl shadow-xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Personal Information */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                Personal Information
              </h2>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name
                </label>
                <input
                  type="text"
                  readOnly={!isEditing}
                  value={formData.username}
                  onChange={(e) => handleChange("name", e.target.value)}
                  className={`w-full px-4 py-3 rounded-xl border ${
                    isEditing
                      ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                      : "bg-gray-50 border-gray-200"
                  } transition-colors`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="text"
                  readOnly={!isEditing}
                  value={formData.phone}
                  onChange={(e) => handleChange("phone", e.target.value)}
                  className={`w-full px-4 py-3 rounded-xl border ${
                    isEditing
                      ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                      : "bg-gray-50 border-gray-200"
                  } transition-colors`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  readOnly={!isEditing}
                  value={formData.email}
                  onChange={(e) => handleChange("email", e.target.value)}
                  className={`w-full px-4 py-3 rounded-xl border ${
                    isEditing
                      ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                      : "bg-gray-50 border-gray-200"
                  } transition-colors`}
                />
              </div>
            </div>

            {/* Address Information */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                Address Information
              </h2>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Street Address
                </label>
                <input
                  type="text"
                  readOnly={!isEditing}
                  value={formData.street}
                  onChange={(e) => handleChange("street", e.target.value)}
                  className={`w-full px-4 py-3 rounded-xl border ${
                    isEditing
                      ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                      : "bg-gray-50 border-gray-200"
                  } transition-colors`}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City
                  </label>
                  <input
                    type="text"
                    readOnly={!isEditing}
                    value={formData.city}
                    onChange={(e) => handleChange("city", e.target.value)}
                    className={`w-full px-4 py-3 rounded-xl border ${
                      isEditing
                        ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                        : "bg-gray-50 border-gray-200"
                    } transition-colors`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    District
                  </label>
                  <input
                    type="text"
                    readOnly={!isEditing}
                    value={formData.district}
                    onChange={(e) => handleChange("district", e.target.value)}
                    className={`w-full px-4 py-3 rounded-xl border ${
                      isEditing
                        ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                        : "bg-gray-50 border-gray-200"
                    } transition-colors`}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    State
                  </label>
                  <input
                    type="text"
                    readOnly={!isEditing}
                    value={formData.state}
                    onChange={(e) => handleChange("state", e.target.value)}
                    className={`w-full px-4 py-3 rounded-xl border ${
                      isEditing
                        ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                        : "bg-gray-50 border-gray-200"
                    } transition-colors`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pincode
                  </label>
                  <input
                    type="text"
                    readOnly={!isEditing}
                    value={formData.pincode}
                    onChange={(e) => handleChange("pincode", e.target.value)}
                    className={`w-full px-4 py-3 rounded-xl border ${
                      isEditing
                        ? "border-pink-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
                        : "bg-gray-50 border-gray-200"
                    } transition-colors`}
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Country
                </label>
                <input
                  type="text"
                  readOnly={true}
                  value={formData.country}
                  className="w-full px-4 py-3 rounded-xl border bg-gray-100 border-gray-200 cursor-not-allowed transition-colors"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 space-y-4">
            {isEditing && (
              <button
                onClick={handleUpdateProfile}
                className="w-full bg-gradient-to-r from-pink-200 to-purple-200 text-white font-semibold py-3 rounded-xl hover:from-pink-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2"
              >
                Save Changes
              </button>
            )}
            <div className="flex flex-col sm:flex-row gap-4">
            <button
                onClick={handleLogout}
                className="flex-1 px-6 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              >
                Logout
              </button>
              <button
                onClick={() => setShowDeleteModal(true)}
                className="flex-1 px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2"
              >
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Account Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white rounded-3xl shadow-xl p-8 w-11/12 max-w-md m-4">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              Delete Account
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete your account? This action cannot be undone
              and all your data will be permanently removed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleCancelDelete}
                className="flex-1 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="flex-1 px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors"
              >
                Delete Account
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Change Password Modal */}
      {showChangePasswordModal && (
        <ChangePassword onClose={() => setShowChangePasswordModal(false)} />
      )}

      {/* Login Popup */}
      {showLoginPopup && (
        <Login
          isOpen={showLoginPopup}
          onClose={() => {
            setShowLoginPopup(false);
            window.location.reload();
          }}
        />
      )}

      {/* Profile Picture URL Modal */}
      {showPictureModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white rounded-3xl shadow-xl p-8 w-11/12 max-w-md m-4">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              Update Profile Picture
            </h3>
            <p className="text-gray-600 mb-6">
              Enter the URL of your new profile picture:
            </p>
            <div className="mb-6">
              <input
                type="text"
                value={pictureUrl}
                onChange={(e) => setPictureUrl(e.target.value)}
                placeholder="https://example.com/your-image.jpg"
                className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:ring-2 focus:ring-pink-400 focus:border-pink-400"
              />
            </div>
            {pictureUrl && (
              <div className="mb-6">
                <p className="text-sm text-gray-600 mb-2">Preview:</p>
                <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-2 border-pink-200">
                  <img
                    src={pictureUrl}
                    alt="Preview"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = "https://mrwallpaper.com/images/hd/cute-anime-girl-profile-picture-9dlupjr81mez8wqq.jpg";
                    }}
                  />
                </div>
              </div>
            )}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => setShowPictureModal(false)}
                className="flex-1 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdatePicture}
                disabled={!pictureUrl}
                className={`flex-1 px-6 py-3 rounded-xl transition-colors ${pictureUrl ? 'bg-pink-500 text-white hover:bg-pink-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
              >
                Update Picture
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;
